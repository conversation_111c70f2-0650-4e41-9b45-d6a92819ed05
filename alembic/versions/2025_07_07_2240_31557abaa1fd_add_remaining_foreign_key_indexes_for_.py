"""Add remaining foreign key indexes for gaming_project_id columns

Revision ID: 31557abaa1fd
Revises: 6340ca765d81
Create Date: 2025-07-07 22:40:40.049431

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '31557abaa1fd'
down_revision: Union[str, None] = '6340ca765d81'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Add missing foreign key indexes for gaming_project_id columns
    op.create_index('idx_nft_collections_gaming_project_id', 'nft_collections', ['gaming_project_id'], unique=False)
    op.create_index('idx_blockchain_data_gaming_project_id', 'blockchain_data', ['gaming_project_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('idx_game_events_gaming_contract_id'), 'game_events', ['gaming_contract_id'], unique=False)
    op.create_index(op.f('idx_blockchain_data_article_id'), 'blockchain_data', ['article_id'], unique=False)
    op.create_index(op.f('idx_articles_duplicate_of_id'), 'articles', ['duplicate_of_id'], unique=False)
    # ### end Alembic commands ###
