"""Add missing foreign key indexes for performance optimization

Revision ID: 6340ca765d81
Revises: e8115d0cec9b
Create Date: 2025-07-07 22:39:23.137041

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '6340ca765d81'
down_revision: Union[str, None] = 'e8115d0cec9b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('idx_articles_gaming_category_published_desc'), table_name='articles', postgresql_where='((gaming_category IS NOT NULL) AND (published_at IS NOT NULL))')
    op.drop_index(op.f('idx_articles_published_at_desc'), table_name='articles', postgresql_where='(published_at IS NOT NULL)')
    op.drop_index(op.f('idx_articles_relevance_score_desc'), table_name='articles', postgresql_where='(relevance_score IS NOT NULL)')
    op.create_index(op.f('ix_articles_created_at'), 'articles', ['created_at'], unique=False)
    op.create_index(op.f('ix_articles_updated_at'), 'articles', ['updated_at'], unique=False)
    op.drop_index(op.f('idx_blockchain_data_blockchain_timestamp'), table_name='blockchain_data', postgresql_where='(blockchain IS NOT NULL)')
    op.drop_index(op.f('idx_blockchain_data_timestamp_desc'), table_name='blockchain_data', postgresql_where='(block_timestamp IS NOT NULL)')
    op.create_index(op.f('ix_blockchain_data_created_at'), 'blockchain_data', ['created_at'], unique=False)
    op.create_index(op.f('ix_blockchain_data_updated_at'), 'blockchain_data', ['updated_at'], unique=False)
    op.create_foreign_key(None, 'blockchain_data', 'gaming_projects', ['gaming_project_id'], ['id'])
    op.create_index(op.f('ix_contract_analyses_created_at'), 'contract_analyses', ['created_at'], unique=False)
    op.create_index(op.f('ix_contract_analyses_updated_at'), 'contract_analyses', ['updated_at'], unique=False)
    op.create_index(op.f('ix_game_events_created_at'), 'game_events', ['created_at'], unique=False)
    op.create_index(op.f('ix_game_events_updated_at'), 'game_events', ['updated_at'], unique=False)
    op.create_index(op.f('ix_gaming_contracts_created_at'), 'gaming_contracts', ['created_at'], unique=False)
    op.create_index(op.f('ix_gaming_contracts_updated_at'), 'gaming_contracts', ['updated_at'], unique=False)
    op.drop_index(op.f('idx_gaming_projects_blockchain_active'), table_name='gaming_projects', postgresql_where='(blockchain IS NOT NULL)')
    op.drop_index(op.f('idx_gaming_projects_category_active'), table_name='gaming_projects', postgresql_where='(primary_genre IS NOT NULL)')
    op.drop_index(op.f('idx_gaming_projects_name_lower'), table_name='gaming_projects', postgresql_where='(project_name IS NOT NULL)')
    op.create_index(op.f('ix_gaming_projects_created_at'), 'gaming_projects', ['created_at'], unique=False)
    op.create_index(op.f('ix_gaming_projects_updated_at'), 'gaming_projects', ['updated_at'], unique=False)
    op.create_index(op.f('ix_gaming_protocol_daily_summary_created_at'), 'gaming_protocol_daily_summary', ['created_at'], unique=False)
    op.create_index(op.f('ix_gaming_protocol_daily_summary_updated_at'), 'gaming_protocol_daily_summary', ['updated_at'], unique=False)
    op.create_index(op.f('ix_gaming_protocol_metrics_history_created_at'), 'gaming_protocol_metrics_history', ['created_at'], unique=False)
    op.create_index(op.f('ix_gaming_protocol_metrics_history_updated_at'), 'gaming_protocol_metrics_history', ['updated_at'], unique=False)
    op.create_index(op.f('ix_nft_collections_created_at'), 'nft_collections', ['created_at'], unique=False)
    op.create_index(op.f('ix_nft_collections_updated_at'), 'nft_collections', ['updated_at'], unique=False)
    op.create_foreign_key(None, 'nft_collections', 'gaming_projects', ['gaming_project_id'], ['id'])
    op.create_index(op.f('ix_reddit_posts_created_at'), 'reddit_posts', ['created_at'], unique=False)
    op.create_index(op.f('ix_reddit_posts_updated_at'), 'reddit_posts', ['updated_at'], unique=False)
    op.create_index(op.f('ix_social_media_filters_created_at'), 'social_media_filters', ['created_at'], unique=False)
    op.create_index(op.f('ix_social_media_filters_updated_at'), 'social_media_filters', ['updated_at'], unique=False)
    op.create_index(op.f('ix_sources_created_at'), 'sources', ['created_at'], unique=False)
    op.create_index(op.f('ix_sources_updated_at'), 'sources', ['updated_at'], unique=False)
    op.create_index(op.f('ix_twitter_posts_updated_at'), 'twitter_posts', ['updated_at'], unique=False)

    # Add missing foreign key indexes identified by validator
    op.create_index('idx_articles_duplicate_of_id', 'articles', ['duplicate_of_id'], unique=False)
    op.create_index('idx_game_events_gaming_contract_id', 'game_events', ['gaming_contract_id'], unique=False)
    op.create_index('idx_blockchain_data_article_id', 'blockchain_data', ['article_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_twitter_posts_updated_at'), table_name='twitter_posts')
    op.drop_index(op.f('ix_sources_updated_at'), table_name='sources')
    op.drop_index(op.f('ix_sources_created_at'), table_name='sources')
    op.drop_index(op.f('ix_social_media_filters_updated_at'), table_name='social_media_filters')
    op.drop_index(op.f('ix_social_media_filters_created_at'), table_name='social_media_filters')
    op.drop_index(op.f('ix_reddit_posts_updated_at'), table_name='reddit_posts')
    op.drop_index(op.f('ix_reddit_posts_created_at'), table_name='reddit_posts')
    op.drop_constraint(None, 'nft_collections', type_='foreignkey')
    op.drop_index(op.f('ix_nft_collections_updated_at'), table_name='nft_collections')
    op.drop_index(op.f('ix_nft_collections_created_at'), table_name='nft_collections')
    op.drop_index(op.f('ix_gaming_protocol_metrics_history_updated_at'), table_name='gaming_protocol_metrics_history')
    op.drop_index(op.f('ix_gaming_protocol_metrics_history_created_at'), table_name='gaming_protocol_metrics_history')
    op.drop_index(op.f('ix_gaming_protocol_daily_summary_updated_at'), table_name='gaming_protocol_daily_summary')
    op.drop_index(op.f('ix_gaming_protocol_daily_summary_created_at'), table_name='gaming_protocol_daily_summary')
    op.drop_index(op.f('ix_gaming_projects_updated_at'), table_name='gaming_projects')
    op.drop_index(op.f('ix_gaming_projects_created_at'), table_name='gaming_projects')
    op.create_index(op.f('idx_gaming_projects_name_lower'), 'gaming_projects', [sa.literal_column('lower(project_name::text)')], unique=False, postgresql_where='(project_name IS NOT NULL)')
    op.create_index(op.f('idx_gaming_projects_category_active'), 'gaming_projects', ['primary_genre', 'is_active'], unique=False, postgresql_where='(primary_genre IS NOT NULL)')
    op.create_index(op.f('idx_gaming_projects_blockchain_active'), 'gaming_projects', ['blockchain', 'is_active'], unique=False, postgresql_where='(blockchain IS NOT NULL)')
    op.drop_index(op.f('ix_gaming_contracts_updated_at'), table_name='gaming_contracts')
    op.drop_index(op.f('ix_gaming_contracts_created_at'), table_name='gaming_contracts')
    op.drop_index(op.f('ix_game_events_updated_at'), table_name='game_events')
    op.drop_index(op.f('ix_game_events_created_at'), table_name='game_events')
    op.drop_index(op.f('ix_contract_analyses_updated_at'), table_name='contract_analyses')
    op.drop_index(op.f('ix_contract_analyses_created_at'), table_name='contract_analyses')
    op.drop_constraint(None, 'blockchain_data', type_='foreignkey')
    op.drop_index(op.f('ix_blockchain_data_updated_at'), table_name='blockchain_data')
    op.drop_index(op.f('ix_blockchain_data_created_at'), table_name='blockchain_data')
    op.create_index(op.f('idx_blockchain_data_timestamp_desc'), 'blockchain_data', [sa.literal_column('block_timestamp DESC')], unique=False, postgresql_where='(block_timestamp IS NOT NULL)')
    op.create_index(op.f('idx_blockchain_data_blockchain_timestamp'), 'blockchain_data', ['blockchain', sa.literal_column('block_timestamp DESC')], unique=False, postgresql_where='(blockchain IS NOT NULL)')
    op.drop_index(op.f('ix_articles_updated_at'), table_name='articles')
    op.drop_index(op.f('ix_articles_created_at'), table_name='articles')
    op.create_index(op.f('idx_articles_relevance_score_desc'), 'articles', [sa.literal_column('relevance_score DESC NULLS LAST')], unique=False, postgresql_where='(relevance_score IS NOT NULL)')
    op.create_index(op.f('idx_articles_published_at_desc'), 'articles', [sa.literal_column('published_at DESC')], unique=False, postgresql_where='(published_at IS NOT NULL)')
    op.create_index(op.f('idx_articles_gaming_category_published_desc'), 'articles', ['gaming_category', sa.literal_column('published_at DESC')], unique=False, postgresql_where='((gaming_category IS NOT NULL) AND (published_at IS NOT NULL))')
    # ### end Alembic commands ###
