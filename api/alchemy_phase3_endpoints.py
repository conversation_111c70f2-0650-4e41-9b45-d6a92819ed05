"""
Alchemy Phase 3 API Endpoints
Enhanced Gaming Features: Wallet Portfolios, NFT Intelligence, Protocol Metrics
"""
from fastapi import APIRouter, HTTPException, Query, BackgroundTasks
from typing import Dict, List, Optional, Any
from datetime import datetime
from pydantic import BaseModel
import logging

from blockchain.data_clients.manager import BlockchainDataManager
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/alchemy/phase3", tags=["Alchemy Phase 3"])

# Initialize blockchain data manager
blockchain_manager = BlockchainDataManager()


# Pydantic models for request/response validation
class WalletPortfolioRequest(BaseModel):
    addresses: List[str]
    networks: Optional[List[str]] = None


class NFTCollectionAnalysisResponse(BaseModel):
    collection_address: str
    collection_name: str
    network: str
    total_supply: int
    floor_price: float
    floor_price_usd: float
    volume_24h: float
    holders_count: int
    rarity_scores: Dict[str, float]
    trait_distribution: Dict[str, Dict[str, int]]
    gaming_project: Optional[str]
    utility_type: str


class CrossCollectionHolderResponse(BaseModel):
    wallet_address: str
    collections_held: List[str]
    total_nfts: int
    total_value_usd: float
    whale_score: float
    gaming_focus_score: float
    collection_overlap: Dict[str, List[str]]


class EnhancedProtocolMetricsResponse(BaseModel):
    protocol_name: str
    multi_chain_metrics: Dict[str, Any]
    aggregated_metrics: Dict[str, Any]
    cross_chain_insights: Dict[str, Any]
    timestamp: str


# Gaming Wallet Portfolio Endpoints
@router.post("/wallet/portfolio")
async def get_gaming_wallet_portfolio(request: WalletPortfolioRequest):
    """Get comprehensive gaming wallet portfolio analysis across multiple chains"""
    try:
        if not request.addresses:
            raise HTTPException(status_code=400, detail="At least one wallet address is required")
        
        if len(request.addresses) > 5:
            raise HTTPException(status_code=400, detail="Maximum 5 wallet addresses allowed")
        
        # Validate wallet addresses
        for address in request.addresses:
            if not address.startswith('0x') or len(address) != 42:
                raise HTTPException(status_code=400, detail=f"Invalid wallet address: {address}")
        
        await blockchain_manager.initialize_clients()
        portfolio_data = await blockchain_manager.get_gaming_wallet_portfolio(
            request.addresses, 
            request.networks
        )
        
        if not portfolio_data:
            raise HTTPException(status_code=404, detail="No portfolio data found")
        
        return {
            "status": "success",
            "data": portfolio_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting wallet portfolio: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/wallet/portfolio/{address}")
async def get_single_wallet_portfolio(
    address: str,
    networks: Optional[str] = Query(None, description="Comma-separated list of networks")
):
    """Get gaming portfolio for a single wallet address"""
    try:
        # Validate address
        if not address.startswith('0x') or len(address) != 42:
            raise HTTPException(status_code=400, detail="Invalid wallet address")
        
        # Parse networks
        network_list = networks.split(',') if networks else None
        
        await blockchain_manager.initialize_clients()
        portfolio_data = await blockchain_manager.get_gaming_wallet_portfolio([address], network_list)
        
        if not portfolio_data:
            raise HTTPException(status_code=404, detail="No portfolio data found")
        
        return {
            "status": "success",
            "data": portfolio_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting single wallet portfolio: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# Gaming NFT Intelligence Endpoints
@router.get("/nft/collection/{collection_address}/analysis")
async def get_nft_collection_analysis(
    collection_address: str,
    network: str = Query("eth-mainnet", description="Blockchain network")
):
    """Get comprehensive NFT collection analysis including rarity and market data"""
    try:
        # Validate collection address
        if not collection_address.startswith('0x') or len(collection_address) != 42:
            raise HTTPException(status_code=400, detail="Invalid collection address")
        
        await blockchain_manager.initialize_clients()
        analysis_data = await blockchain_manager.get_gaming_nft_collection_analysis(
            collection_address, 
            network
        )
        
        if not analysis_data:
            raise HTTPException(status_code=404, detail="No collection analysis data found")
        
        return {
            "status": "success",
            "data": analysis_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting NFT collection analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.post("/nft/cross-collection/holders")
async def get_cross_collection_holder_analysis(
    collection_addresses: List[str],
    network: str = Query("eth-mainnet", description="Blockchain network")
):
    """Analyze holders across multiple gaming NFT collections"""
    try:
        if not collection_addresses:
            raise HTTPException(status_code=400, detail="At least one collection address is required")
        
        if len(collection_addresses) > 10:
            raise HTTPException(status_code=400, detail="Maximum 10 collection addresses allowed")
        
        # Validate collection addresses
        for address in collection_addresses:
            if not address.startswith('0x') or len(address) != 42:
                raise HTTPException(status_code=400, detail=f"Invalid collection address: {address}")
        
        await blockchain_manager.initialize_clients()
        analysis_data = await blockchain_manager.get_cross_collection_holder_analysis(
            collection_addresses, 
            network
        )
        
        return {
            "status": "success",
            "data": analysis_data,
            "total_cross_holders": len(analysis_data),
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting cross-collection holder analysis: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# Enhanced Gaming Protocol Metrics Endpoints
@router.get("/protocol/{protocol_name}/enhanced-metrics")
async def get_enhanced_protocol_metrics(
    protocol_name: str,
    networks: Optional[str] = Query(None, description="Comma-separated list of networks")
):
    """Get enhanced gaming protocol metrics across multiple blockchain networks"""
    try:
        # Parse networks
        network_list = networks.split(',') if networks else None
        
        await blockchain_manager.initialize_clients()
        metrics_data = await blockchain_manager.get_enhanced_gaming_protocol_metrics(
            protocol_name, 
            network_list
        )
        
        if not metrics_data:
            raise HTTPException(status_code=404, detail=f"No metrics data found for protocol: {protocol_name}")
        
        return {
            "status": "success",
            "data": metrics_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting enhanced protocol metrics: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/protocol/multi-chain/summary")
async def get_multi_chain_protocol_summary():
    """Get summary of gaming protocol activity across all supported chains"""
    try:
        from config.gaming_config import gaming_project_manager
        
        await blockchain_manager.initialize_clients()
        
        # Get metrics for all gaming projects
        all_protocols_data = {}
        total_summary = {
            'total_protocols': 0,
            'total_networks': 0,
            'total_transactions_24h': 0,
            'total_volume_24h_usd': 0.0,
            'active_protocols': []
        }
        
        for project_slug, project in gaming_project_manager.projects.items():
            try:
                metrics = await blockchain_manager.get_enhanced_gaming_protocol_metrics(project_slug)
                if metrics:
                    all_protocols_data[project_slug] = metrics
                    total_summary['active_protocols'].append(project_slug)
                    
                    # Aggregate totals
                    aggregated = metrics.get('aggregated_metrics', {})
                    total_summary['total_transactions_24h'] += aggregated.get('total_transactions_24h', 0)
                    total_summary['total_volume_24h_usd'] += aggregated.get('total_volume_24h_usd', 0.0)
                    
            except Exception as e:
                logger.warning(f"Failed to get metrics for {project_slug}: {e}")
                continue
        
        total_summary['total_protocols'] = len(all_protocols_data)
        total_summary['total_networks'] = len(set(
            network for metrics in all_protocols_data.values() 
            for network in metrics.get('aggregated_metrics', {}).get('active_networks', [])
        ))
        
        return {
            "status": "success",
            "summary": total_summary,
            "protocols": all_protocols_data,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error getting multi-chain protocol summary: {e}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


# Health check endpoint
@router.get("/health")
async def health_check():
    """Health check for Alchemy Phase 3 endpoints"""
    try:
        await blockchain_manager.initialize_clients()
        alchemy_health = await blockchain_manager.get_alchemy_health_status()
        
        return {
            "status": "healthy",
            "alchemy_status": alchemy_health,
            "features": [
                "Gaming Wallet Portfolios",
                "NFT Collection Analysis", 
                "Cross-Collection Holder Analysis",
                "Enhanced Protocol Metrics",
                "Multi-Chain Activity Aggregation"
            ],
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }
