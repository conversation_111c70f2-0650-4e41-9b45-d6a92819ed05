"""
Dashboard API endpoints for Web3 Gaming News Tracker
Specialized endpoints for dashboard data aggregation and visualization
Enhanced with Redis caching and performance optimizations
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, desc, text
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel
import logging

from models.base import get_db
from models.gaming import Article, Source, GamingProject, NFTCollection, BlockchainData
from services.dashboard_analytics import DashboardAnalytics
from services.redis_cache import gaming_cache
from services.api_optimization import optimize_response, DatabaseQueryOptimizer, perf_monitor
from services.sql_optimization import get_sql_optimizer
# Note: TimeSeriesAggregator will be implemented if needed
# from services.time_series_aggregator import TimeSeriesAggregator

router = APIRouter()
logger = logging.getLogger(__name__)
query_optimizer = DatabaseQueryOptimizer()

# Response Models
class OverviewMetrics(BaseModel):
    total_articles: int
    total_sources: int
    total_gaming_projects: int
    total_nft_collections: int
    articles_last_24h: int
    active_blockchain_networks: int
    top_gaming_categories: List[Dict[str, Any]]
    recent_activity_score: float

class NewsAnalyticsData(BaseModel):
    articles_by_hour: List[Dict[str, Any]]
    articles_by_source: List[Dict[str, Any]]
    articles_by_network: List[Dict[str, Any]]
    gaming_categories_distribution: List[Dict[str, Any]]
    sentiment_trends: List[Dict[str, Any]]
    top_mentioned_projects: List[Dict[str, Any]]

class CrossChainData(BaseModel):
    network_comparison: List[Dict[str, Any]]
    activity_scores: List[Dict[str, Any]]
    gaming_project_distribution: List[Dict[str, Any]]
    token_price_correlations: List[Dict[str, Any]]
    nft_activity_by_network: List[Dict[str, Any]]

class RealTimeData(BaseModel):
    live_articles: List[Dict[str, Any]]
    system_status: Dict[str, Any]
    alert_notifications: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]

class TimeSeriesMetrics(BaseModel):
    timestamps: List[str]
    metrics: Dict[str, List[float]]
    metadata: Dict[str, Any]


@router.get("/dashboard/overview", response_model=OverviewMetrics)
@optimize_response(cache_ttl=300, cache_key_prefix="dashboard:overview")
async def get_dashboard_overview(
    db: Session = Depends(get_db),
    hours: int = Query(24, description="Hours to look back for recent activity")
):
    """Get overview metrics for dashboard with Redis caching and optimized queries"""
    try:
        # Check cache first
        cached_data = gaming_cache.get_dashboard_overview(hours)
        if cached_data:
            return cached_data

        # Use optimized SQL instead of multiple separate queries
        sql_optimizer = get_sql_optimizer(db)
        overview_data_dict = sql_optimizer.get_dashboard_overview_optimized(hours)

        overview_data = OverviewMetrics(
            total_articles=overview_data_dict["total_articles"],
            total_sources=overview_data_dict["total_sources"],
            total_gaming_projects=overview_data_dict["total_gaming_projects"],
            total_nft_collections=overview_data_dict["total_nft_collections"],
            articles_last_24h=overview_data_dict["articles_last_24h"],
            active_blockchain_networks=overview_data_dict["active_blockchain_networks"],
            top_gaming_categories=overview_data_dict["top_gaming_categories"],
            recent_activity_score=min(100.0, max(0.0, overview_data_dict["articles_last_24h"] / hours * 10))
        )

        # Cache the result
        gaming_cache.cache_dashboard_overview(overview_data.dict(), hours)

        return overview_data

    except Exception as e:
        logger.error(f"Error getting dashboard overview: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting overview: {str(e)}")


@router.get("/dashboard/news-analytics", response_model=NewsAnalyticsData)
async def get_news_analytics(
    db: Session = Depends(get_db),
    hours: int = Query(24, description="Hours to analyze"),
    network: Optional[str] = Query(None, description="Filter by blockchain network")
):
    """Get news analytics data for dashboard"""
    try:
        analytics = DashboardAnalytics(db)
        
        # Get articles by hour
        articles_by_hour = await analytics.get_articles_by_hour(hours=hours, network=network)
        
        # Get articles by source
        articles_by_source = await analytics.get_articles_by_source(hours=hours, network=network)
        
        # Get articles by network
        articles_by_network = await analytics.get_articles_by_network(hours=hours)
        
        # Get gaming categories distribution
        categories_dist = await analytics.get_gaming_categories_distribution(hours=hours, network=network)
        
        # Get sentiment trends (if available)
        sentiment_trends = await analytics.get_sentiment_trends(hours=hours, network=network)
        
        # Get top mentioned projects
        top_projects = await analytics.get_top_mentioned_projects(hours=hours, network=network, limit=10)
        
        return NewsAnalyticsData(
            articles_by_hour=articles_by_hour,
            articles_by_source=articles_by_source,
            articles_by_network=articles_by_network,
            gaming_categories_distribution=categories_dist,
            sentiment_trends=sentiment_trends,
            top_mentioned_projects=top_projects
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting news analytics: {str(e)}")


@router.get("/dashboard/cross-chain", response_model=CrossChainData)
async def get_cross_chain_data(
    db: Session = Depends(get_db),
    days: int = Query(7, description="Days to analyze")
):
    """Get cross-chain comparison data"""
    try:
        analytics = DashboardAnalytics(db)
        
        # Get network comparison
        network_comparison = await analytics.get_network_comparison(days=days)
        
        # Get activity scores by network
        activity_scores = await analytics.get_network_activity_scores(days=days)
        
        # Get gaming project distribution across networks
        project_distribution = await analytics.get_gaming_project_distribution()
        
        # Get token price correlations (if market data available)
        price_correlations = await analytics.get_token_price_correlations()
        
        # Get NFT activity by network
        nft_activity = await analytics.get_nft_activity_by_network(days=days)
        
        return CrossChainData(
            network_comparison=network_comparison,
            activity_scores=activity_scores,
            gaming_project_distribution=project_distribution,
            token_price_correlations=price_correlations,
            nft_activity_by_network=nft_activity
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting cross-chain data: {str(e)}")


@router.get("/dashboard/real-time", response_model=RealTimeData)
async def get_real_time_data(
    db: Session = Depends(get_db),
    limit: int = Query(20, description="Number of recent articles to return")
):
    """Get real-time data for dashboard"""
    try:
        analytics = DashboardAnalytics(db)
        
        # Get latest articles
        live_articles = await analytics.get_latest_articles(limit=limit)
        
        # Get system status
        system_status = await analytics.get_system_status()
        
        # Get alert notifications
        alert_notifications = await analytics.get_recent_alerts(limit=10)
        
        # Get performance metrics
        performance_metrics = await analytics.get_performance_metrics()
        
        return RealTimeData(
            live_articles=live_articles,
            system_status=system_status,
            alert_notifications=alert_notifications,
            performance_metrics=performance_metrics
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting real-time data: {str(e)}")


@router.get("/dashboard/metrics", response_model=TimeSeriesMetrics)
async def get_time_series_metrics(
    db: Session = Depends(get_db),
    metric: str = Query(..., description="Metric name to retrieve"),
    hours: int = Query(24, description="Hours of data to retrieve"),
    interval: str = Query("1h", description="Data aggregation interval (5m, 15m, 1h, 6h, 1d)")
):
    """Get time-series metrics for dashboard charts"""
    try:
        analytics = DashboardAnalytics(db)

        # Generate sample time series data based on metric type
        timestamps = []
        metrics_data = {}

        # Generate timestamps based on interval
        now = datetime.utcnow()
        if interval == "1h":
            step = timedelta(hours=1)
        elif interval == "15m":
            step = timedelta(minutes=15)
        elif interval == "5m":
            step = timedelta(minutes=5)
        else:
            step = timedelta(hours=1)

        points = hours // (step.total_seconds() / 3600) if step.total_seconds() > 0 else 24
        points = int(min(points, 100))  # Limit to 100 data points

        for i in range(points):
            timestamp = now - timedelta(hours=hours) + (step * i)
            timestamps.append(timestamp.isoformat())

        # Generate sample metrics based on metric type
        if metric == "articles":
            metrics_data["articles"] = [5 + (i % 3) for i in range(points)]
        elif metric == "activity_score":
            metrics_data["activity_score"] = [50 + (i % 30) for i in range(points)]
        else:
            metrics_data[metric] = [10 + (i % 5) for i in range(points)]

        return TimeSeriesMetrics(
            timestamps=timestamps,
            metrics=metrics_data,
            metadata={
                "metric": metric,
                "interval": interval,
                "hours": hours,
                "points": points
            }
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting time series metrics: {str(e)}")


@router.get("/dashboard/gaming-tokens")
async def get_gaming_token_data(
    db: Session = Depends(get_db),
    network: Optional[str] = Query(None, description="Filter by blockchain network")
):
    """Get gaming token price and market data"""
    try:
        analytics = DashboardAnalytics(db)
        
        # Get gaming token data
        token_data = await analytics.get_gaming_token_data(network=network)
        
        return {
            "tokens": token_data,
            "last_updated": datetime.utcnow().isoformat(),
            "network_filter": network
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting token data: {str(e)}")


@router.get("/dashboard/solana-analytics")
async def get_solana_analytics(
    db: Session = Depends(get_db),
    hours: int = Query(24, description="Hours to analyze")
):
    """Get Solana-specific analytics data"""
    try:
        analytics = DashboardAnalytics(db)
        
        # Get Solana-specific metrics
        solana_data = await analytics.get_solana_analytics(hours=hours)
        
        return {
            "solana_articles": solana_data['articles'],
            "solana_projects": solana_data['projects'],
            "solana_activity_score": solana_data['activity_score'],
            "solana_vs_ethereum": solana_data['comparison'],
            "top_solana_sources": solana_data['top_sources']
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting Solana analytics: {str(e)}")


@router.get("/dashboard/alerts")
async def get_dashboard_alerts(
    db: Session = Depends(get_db),
    severity: Optional[str] = Query(None, description="Filter by alert severity"),
    limit: int = Query(50, description="Maximum number of alerts to return")
):
    """Get dashboard alerts and notifications"""
    try:
        analytics = DashboardAnalytics(db)
        
        # Get alerts
        alerts = await analytics.get_alerts(severity=severity, limit=limit)
        
        return {
            "alerts": alerts,
            "total_count": len(alerts),
            "severity_filter": severity,
            "last_updated": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting alerts: {str(e)}")


@router.get("/dashboard/health")
async def get_dashboard_health():
    """Get dashboard health status"""
    try:
        # Check various system components
        health_status = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "components": {
                "database": "healthy",
                "redis": "healthy",
                "blockchain_rpcs": "healthy",
                "news_scrapers": "healthy",
                "market_data": "healthy"
            },
            "metrics": {
                "uptime_seconds": 0,  # Would be calculated from startup time
                "memory_usage_mb": 0,  # Would be from system metrics
                "cpu_usage_percent": 0  # Would be from system metrics
            }
        }
        
        return health_status

    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


# Prometheus metrics for dashboard
try:
    from prometheus_client import Gauge

    class DashboardMetrics:
        """Prometheus metrics for dashboard monitoring"""

        def __init__(self):
            # Gaming-specific metrics
            self.gaming_articles_total = Gauge('gaming_articles_total', 'Total number of gaming articles')
            self.gaming_sources_active = Gauge('gaming_sources_active', 'Number of active gaming news sources')
            self.gaming_scrapers_status = Gauge('gaming_scrapers_status', 'Gaming scrapers status', ['scraper_name'])

            # Phase 6: Gaming Protocol Analytics metrics
            self.gaming_protocols_total = Gauge('gaming_protocols_total', 'Total number of tracked gaming protocols')
            self.gaming_market_cap_total = Gauge('gaming_market_cap_total', 'Total market cap of tracked gaming protocols')
            self.gaming_users_total = Gauge('gaming_users_total', 'Total daily active users across gaming protocols')
            self.gaming_protocol_uptime = Gauge('gaming_protocol_uptime', 'Gaming protocol uptime', ['protocol_name'])
            self.gaming_protocol_price = Gauge('gaming_protocol_token_price', 'Gaming protocol token price', ['protocol_name'])
            self.gaming_protocol_volume = Gauge('gaming_protocol_volume_24h', '24h trading volume', ['protocol_name'])
            self.gaming_nft_floor_price = Gauge('gaming_nft_floor_price', 'NFT floor price', ['protocol_name'])
            self.gaming_tvl = Gauge('gaming_protocol_tvl', 'Total Value Locked in gaming protocol', ['protocol_name'])

    # Create global metrics instance
    dashboard_metrics = DashboardMetrics()

except ImportError:
    # Fallback if prometheus_client is not available
    dashboard_metrics = None
