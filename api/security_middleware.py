"""
Security Middleware for Web3 Gaming News Tracker API
Implements authentication, rate limiting, input validation, and security headers
"""

import time
import hashlib
import hmac
import json
import re
from typing import Dict, List, Optional, Set, Callable
from datetime import datetime, timedelta
from collections import defaultdict, deque
import logging

from fastapi import Request, Response, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import redis
from sqlalchemy.sql import text

from config.settings import get_settings
from utils.logging_config import log_security_event

settings = get_settings()
logger = logging.getLogger(__name__)
security = HTTPBearer(auto_error=False)

# Redis client for rate limiting and session management
try:
    redis_client = redis.from_url(settings.redis.url)
except Exception as e:
    logger.warning(f"Redis connection failed, using in-memory rate limiting: {e}")
    redis_client = None


class SecurityHeaders:
    """Security headers configuration"""
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        """Get security headers for responses"""
        return {
            # Prevent XSS attacks
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            
            # HTTPS enforcement (in production)
            "Strict-Transport-Security": "max-age=31536000; includeSubDomains" if settings.environment == "production" else "",
            
            # Content Security Policy
            "Content-Security-Policy": (
                "default-src 'self'; "
                "script-src 'self' 'unsafe-inline' 'unsafe-eval'; "
                "style-src 'self' 'unsafe-inline'; "
                "img-src 'self' data: https:; "
                "connect-src 'self' wss: ws:; "
                "font-src 'self' data:; "
                "object-src 'none'; "
                "base-uri 'self'; "
                "form-action 'self'"
            ),
            
            # Referrer policy
            "Referrer-Policy": "strict-origin-when-cross-origin",
            
            # Permissions policy
            "Permissions-Policy": "geolocation=(), microphone=(), camera=()",
            
            # Server identification
            "Server": "Web3GamingTracker/1.0"
        }


class RateLimiter:
    """Advanced rate limiting with multiple strategies"""
    
    def __init__(self):
        self.memory_store = defaultdict(lambda: defaultdict(deque))
        self.blocked_ips = set()
        self.suspicious_ips = defaultdict(int)
        
        # Rate limit configurations
        self.limits = {
            "default": {"requests": 1000, "window": 3600},  # 1000 requests per hour
            "api_key": {"requests": 5000, "window": 3600},   # 5000 requests per hour with API key
            "content_intelligence": {"requests": 100, "window": 3600},  # 100 requests per hour
            "market_analytics": {"requests": 200, "window": 3600},      # 200 requests per hour
            "blockchain_data": {"requests": 500, "window": 3600},       # 500 requests per hour
        }
        
        # Burst protection
        self.burst_limits = {
            "default": {"requests": 50, "window": 60},      # 50 requests per minute
            "api_key": {"requests": 100, "window": 60},     # 100 requests per minute with API key
        }
    
    def get_client_id(self, request: Request) -> str:
        """Get unique client identifier"""
        # Check for API key first
        api_key = request.headers.get("X-API-Key")
        if api_key:
            return f"api_key:{hashlib.sha256(api_key.encode()).hexdigest()[:16]}"
        
        # Use IP address as fallback
        client_ip = self._get_client_ip(request)
        return f"ip:{client_ip}"
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP address"""
        # Check for forwarded headers (behind proxy)
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # Fallback to direct connection
        return request.client.host if request.client else "unknown"
    
    def get_rate_limit_key(self, request: Request) -> str:
        """Determine rate limit category based on endpoint"""
        path = request.url.path
        
        if "/content-intelligence/" in path:
            return "content_intelligence"
        elif "/market-analytics/" in path:
            return "market_analytics"
        elif "/blockchain/" in path:
            return "blockchain_data"
        elif request.headers.get("X-API-Key"):
            return "api_key"
        else:
            return "default"
    
    async def check_rate_limit(self, request: Request) -> Optional[Dict[str, any]]:
        """Check if request should be rate limited"""
        client_id = self.get_client_id(request)
        client_ip = self._get_client_ip(request)
        
        # Check if IP is blocked
        if client_ip in self.blocked_ips:
            log_security_event("rate_limit_blocked", {
                "client_ip": client_ip,
                "client_id": client_id,
                "endpoint": request.url.path
            })
            return {
                "blocked": True,
                "reason": "IP temporarily blocked due to suspicious activity",
                "retry_after": 3600
            }
        
        rate_limit_key = self.get_rate_limit_key(request)
        current_time = time.time()
        
        # Check main rate limit
        main_limit = self.limits[rate_limit_key]
        if not await self._check_limit(client_id, "main", main_limit, current_time):
            self._handle_rate_limit_exceeded(client_ip, client_id)
            return {
                "blocked": True,
                "reason": f"Rate limit exceeded: {main_limit['requests']} requests per {main_limit['window']} seconds",
                "retry_after": main_limit["window"]
            }
        
        # Check burst limit
        burst_key = "api_key" if "api_key:" in client_id else "default"
        burst_limit = self.burst_limits[burst_key]
        if not await self._check_limit(client_id, "burst", burst_limit, current_time):
            self._handle_rate_limit_exceeded(client_ip, client_id)
            return {
                "blocked": True,
                "reason": f"Burst limit exceeded: {burst_limit['requests']} requests per {burst_limit['window']} seconds",
                "retry_after": burst_limit["window"]
            }
        
        return None
    
    async def _check_limit(self, client_id: str, limit_type: str, limit_config: Dict, current_time: float) -> bool:
        """Check specific rate limit"""
        window = limit_config["window"]
        max_requests = limit_config["requests"]
        
        if redis_client:
            return await self._check_redis_limit(client_id, limit_type, limit_config, current_time)
        else:
            return self._check_memory_limit(client_id, limit_type, limit_config, current_time)
    
    async def _check_redis_limit(self, client_id: str, limit_type: str, limit_config: Dict, current_time: float) -> bool:
        """Redis-based rate limiting"""
        try:
            key = f"rate_limit:{client_id}:{limit_type}"
            window = limit_config["window"]
            max_requests = limit_config["requests"]
            
            # Use sliding window with Redis sorted sets
            pipe = redis_client.pipeline()
            
            # Remove old entries
            pipe.zremrangebyscore(key, 0, current_time - window)
            
            # Count current requests
            pipe.zcard(key)
            
            # Add current request
            pipe.zadd(key, {str(current_time): current_time})
            
            # Set expiration
            pipe.expire(key, window)
            
            results = pipe.execute()
            current_count = results[1]
            
            return current_count < max_requests
            
        except Exception as e:
            logger.error(f"Redis rate limiting error: {e}")
            # Fallback to memory-based limiting
            return self._check_memory_limit(client_id, limit_type, limit_config, current_time)
    
    def _check_memory_limit(self, client_id: str, limit_type: str, limit_config: Dict, current_time: float) -> bool:
        """Memory-based rate limiting"""
        window = limit_config["window"]
        max_requests = limit_config["requests"]
        
        requests = self.memory_store[client_id][limit_type]
        
        # Remove old requests
        while requests and requests[0] < current_time - window:
            requests.popleft()
        
        # Check if under limit
        if len(requests) >= max_requests:
            return False
        
        # Add current request
        requests.append(current_time)
        return True
    
    def _handle_rate_limit_exceeded(self, client_ip: str, client_id: str):
        """Handle rate limit exceeded events"""
        self.suspicious_ips[client_ip] += 1
        
        # Block IP if too many rate limit violations
        if self.suspicious_ips[client_ip] >= 10:
            self.blocked_ips.add(client_ip)
            logger.warning(f"Blocked IP {client_ip} due to repeated rate limit violations")
            
            log_security_event("ip_blocked", {
                "client_ip": client_ip,
                "client_id": client_id,
                "violation_count": self.suspicious_ips[client_ip]
            })
    
    def get_rate_limit_headers(self, request: Request) -> Dict[str, str]:
        """Get rate limit headers for response"""
        client_id = self.get_client_id(request)
        rate_limit_key = self.get_rate_limit_key(request)
        limit_config = self.limits[rate_limit_key]
        
        # Calculate remaining requests (simplified)
        remaining = limit_config["requests"] - 1  # Approximate
        reset_time = int(time.time() + limit_config["window"])
        
        return {
            "X-Rate-Limit-Limit": str(limit_config["requests"]),
            "X-Rate-Limit-Remaining": str(max(0, remaining)),
            "X-Rate-Limit-Reset": str(reset_time),
            "X-Rate-Limit-Window": str(limit_config["window"])
        }


class InputValidator:
    """Input validation and sanitization"""
    
    # Dangerous patterns to detect
    SQL_INJECTION_PATTERNS = [
        r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)",
        r"(--|#|/\*|\*/)",
        r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
        r"(\bUNION\s+SELECT\b)",
        r"(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)",
    ]
    
    XSS_PATTERNS = [
        r"<script[^>]*>.*?</script>",
        r"javascript:",
        r"on\w+\s*=",
        r"<iframe[^>]*>",
        r"<object[^>]*>",
        r"<embed[^>]*>",
    ]
    
    def __init__(self):
        self.sql_regex = re.compile("|".join(self.SQL_INJECTION_PATTERNS), re.IGNORECASE)
        self.xss_regex = re.compile("|".join(self.XSS_PATTERNS), re.IGNORECASE)
    
    def validate_input(self, data: any, field_name: str = "input") -> Dict[str, any]:
        """Validate input data for security threats"""
        threats = []
        
        if isinstance(data, str):
            threats.extend(self._check_string_threats(data, field_name))
        elif isinstance(data, dict):
            for key, value in data.items():
                sub_threats = self.validate_input(value, f"{field_name}.{key}")
                threats.extend(sub_threats["threats"])
        elif isinstance(data, list):
            for i, item in enumerate(data):
                sub_threats = self.validate_input(item, f"{field_name}[{i}]")
                threats.extend(sub_threats["threats"])
        
        return {
            "valid": len(threats) == 0,
            "threats": threats,
            "sanitized_data": self._sanitize_data(data) if threats else data
        }
    
    def _check_string_threats(self, text: str, field_name: str) -> List[Dict[str, str]]:
        """Check string for security threats"""
        threats = []
        
        # SQL injection detection
        if self.sql_regex.search(text):
            threats.append({
                "type": "sql_injection",
                "field": field_name,
                "description": "Potential SQL injection attempt detected"
            })
        
        # XSS detection
        if self.xss_regex.search(text):
            threats.append({
                "type": "xss",
                "field": field_name,
                "description": "Potential XSS attempt detected"
            })
        
        # Path traversal detection
        if "../" in text or "..\\/" in text:
            threats.append({
                "type": "path_traversal",
                "field": field_name,
                "description": "Potential path traversal attempt detected"
            })
        
        # Command injection detection
        if any(cmd in text.lower() for cmd in ["rm ", "del ", "format ", "shutdown", "reboot"]):
            threats.append({
                "type": "command_injection",
                "field": field_name,
                "description": "Potential command injection attempt detected"
            })
        
        return threats
    
    def _sanitize_data(self, data: any) -> any:
        """Sanitize data by removing dangerous content"""
        if isinstance(data, str):
            # Remove potential XSS content
            sanitized = re.sub(r"<script[^>]*>.*?</script>", "", data, flags=re.IGNORECASE | re.DOTALL)
            sanitized = re.sub(r"javascript:", "", sanitized, flags=re.IGNORECASE)
            sanitized = re.sub(r"on\w+\s*=", "", sanitized, flags=re.IGNORECASE)
            return sanitized
        elif isinstance(data, dict):
            return {key: self._sanitize_data(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self._sanitize_data(item) for item in data]
        else:
            return data


class APIKeyValidator:
    """API key validation and management"""
    
    def __init__(self):
        self.valid_api_keys = set()
        self.api_key_permissions = {}
        self.api_key_usage = defaultdict(int)
        
        # Load API keys from environment or database
        self._load_api_keys()
    
    def _load_api_keys(self):
        """Load valid API keys from configuration"""
        # In production, this would load from a secure database
        # For now, we'll use environment variables
        
        # Development API key
        if settings.environment == "development":
            dev_key = "dev_key_12345"
            self.valid_api_keys.add(dev_key)
            self.api_key_permissions[dev_key] = ["read", "write"]
    
    def validate_api_key(self, api_key: str) -> Dict[str, any]:
        """Validate API key and return permissions"""
        if not api_key:
            return {"valid": False, "reason": "No API key provided"}
        
        if api_key not in self.valid_api_keys:
            log_security_event("invalid_api_key", {"api_key_hash": hashlib.sha256(api_key.encode()).hexdigest()[:16]})
            return {"valid": False, "reason": "Invalid API key"}
        
        permissions = self.api_key_permissions.get(api_key, [])
        self.api_key_usage[api_key] += 1
        
        return {
            "valid": True,
            "permissions": permissions,
            "usage_count": self.api_key_usage[api_key]
        }


class SecurityMiddleware(BaseHTTPMiddleware):
    """Comprehensive security middleware"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.rate_limiter = RateLimiter()
        self.input_validator = InputValidator()
        self.api_key_validator = APIKeyValidator()
        self.security_headers = SecurityHeaders()
        
        # Endpoints that require API key authentication
        self.protected_endpoints = {
            "/api/v1/content-intelligence/",
            "/api/v1/market-analytics/",
            "/api/v1/gaming-form/",
        }
        
        # Public endpoints that don't require authentication
        self.public_endpoints = {
            "/health",
            "/stats",
            "/docs",
            "/redoc",
            "/openapi.json",
            "/metrics"
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through security checks"""
        start_time = time.time()
        
        try:
            # Skip security checks for public endpoints
            if any(request.url.path.startswith(endpoint) for endpoint in self.public_endpoints):
                response = await call_next(request)
                return self._add_security_headers(response)
            
            # Rate limiting check
            rate_limit_result = await self.rate_limiter.check_rate_limit(request)
            if rate_limit_result and rate_limit_result.get("blocked"):
                return self._create_rate_limit_response(rate_limit_result)
            
            # API key validation for protected endpoints
            if any(request.url.path.startswith(endpoint) for endpoint in self.protected_endpoints):
                api_key_result = await self._validate_api_key(request)
                if not api_key_result["valid"]:
                    return self._create_auth_error_response(api_key_result["reason"])
            
            # Input validation
            if request.method in ["POST", "PUT", "PATCH"]:
                validation_result = await self._validate_request_input(request)
                if not validation_result["valid"]:
                    return self._create_validation_error_response(validation_result["threats"])
            
            # Process request
            response = await call_next(request)
            
            # Add security headers
            response = self._add_security_headers(response)
            
            # Add rate limit headers
            rate_limit_headers = self.rate_limiter.get_rate_limit_headers(request)
            for header, value in rate_limit_headers.items():
                response.headers[header] = value
            
            # Log successful request
            processing_time = time.time() - start_time
            log_security_event("request_processed", {
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "processing_time": processing_time,
                "client_ip": self.rate_limiter._get_client_ip(request)
            })
            
            return response
            
        except Exception as e:
            logger.error(f"Security middleware error: {e}")
            
            # Log security incident
            log_security_event("security_middleware_error", {
                "error": str(e),
                "method": request.method,
                "path": request.url.path,
                "client_ip": self.rate_limiter._get_client_ip(request)
            })
            
            # Return generic error response
            return Response(
                content=json.dumps({"error": "Internal security error"}),
                status_code=500,
                headers={"Content-Type": "application/json"}
            )
    
    async def _validate_api_key(self, request: Request) -> Dict[str, any]:
        """Validate API key from request"""
        api_key = request.headers.get("X-API-Key")
        return self.api_key_validator.validate_api_key(api_key)
    
    async def _validate_request_input(self, request: Request) -> Dict[str, any]:
        """Validate request input for security threats"""
        try:
            # Get request body
            body = await request.body()
            if body:
                try:
                    data = json.loads(body)
                    return self.input_validator.validate_input(data, "request_body")
                except json.JSONDecodeError:
                    # If not JSON, validate as string
                    return self.input_validator.validate_input(body.decode(), "request_body")
            
            # Validate query parameters
            query_params = dict(request.query_params)
            if query_params:
                return self.input_validator.validate_input(query_params, "query_params")
            
            return {"valid": True, "threats": []}
            
        except Exception as e:
            logger.error(f"Input validation error: {e}")
            return {"valid": False, "threats": [{"type": "validation_error", "description": str(e)}]}
    
    def _add_security_headers(self, response: Response) -> Response:
        """Add security headers to response"""
        headers = self.security_headers.get_security_headers()
        for header, value in headers.items():
            if value:  # Only add non-empty headers
                response.headers[header] = value
        return response
    
    def _create_rate_limit_response(self, rate_limit_result: Dict[str, any]) -> Response:
        """Create rate limit exceeded response"""
        return Response(
            content=json.dumps({
                "error": "Rate limit exceeded",
                "message": rate_limit_result["reason"],
                "retry_after": rate_limit_result["retry_after"]
            }),
            status_code=429,
            headers={
                "Content-Type": "application/json",
                "Retry-After": str(rate_limit_result["retry_after"])
            }
        )
    
    def _create_auth_error_response(self, reason: str) -> Response:
        """Create authentication error response"""
        return Response(
            content=json.dumps({
                "error": "Authentication required",
                "message": reason
            }),
            status_code=401,
            headers={"Content-Type": "application/json"}
        )
    
    def _create_validation_error_response(self, threats: List[Dict[str, str]]) -> Response:
        """Create input validation error response"""
        return Response(
            content=json.dumps({
                "error": "Invalid input",
                "message": "Request contains potentially dangerous content",
                "threats": threats
            }),
            status_code=400,
            headers={"Content-Type": "application/json"}
        )
