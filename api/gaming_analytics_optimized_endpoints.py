"""
Optimized Gaming Analytics API endpoints
Replaces inefficient queries with SQL joins and aggregations
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from pydantic import BaseModel

from models.base import get_db
from services.sql_optimization import get_sql_optimizer
from services.redis_cache import gaming_cache

router = APIRouter()


class OptimizedGamingProjectMetrics(BaseModel):
    """Optimized gaming project with comprehensive metrics"""
    id: int
    project_name: str
    name: str
    blockchain_network: str
    category: Optional[str]
    token_symbol: Optional[str]
    token_address: Optional[str]
    is_active: bool
    daily_active_users: Optional[int]
    monthly_active_users: Optional[int]
    total_value_locked: float
    metrics: Dict[str, Any]


class OptimizedBlockchainActivity(BaseModel):
    """Optimized blockchain activity with gaming context"""
    id: int
    blockchain_network: str
    contract_address: str
    event_type: str
    event_name: Optional[str]
    block_number: int
    transaction_hash: str
    block_timestamp: str
    player_address: Optional[str]
    token_id: Optional[str]
    amount: Optional[str]
    game_action: Optional[str]
    gas_used: Optional[int]
    decoded_data: Optional[Dict[str, Any]]
    gaming_context: Dict[str, Any]


@router.get("/gaming-analytics/projects-optimized", response_model=List[OptimizedGamingProjectMetrics])
async def get_gaming_projects_optimized(
    db: Session = Depends(get_db),
    blockchain: Optional[str] = Query(None, description="Filter by blockchain network"),
    category: Optional[str] = Query(None, description="Filter by gaming category"),
    hours: int = Query(24, description="Hours for recent activity metrics"),
    limit: int = Query(50, description="Maximum number of projects to return")
):
    """Get gaming projects with comprehensive metrics using optimized SQL joins"""
    try:
        # Check cache first
        cache_key = f"gaming_projects_optimized:{blockchain}:{category}:{hours}:{limit}"
        cached_data = gaming_cache.get(cache_key)
        if cached_data:
            return cached_data

        # Use optimized SQL query
        sql_optimizer = get_sql_optimizer(db)
        projects_data = sql_optimizer.get_gaming_projects_with_metrics_optimized(
            blockchain=blockchain,
            category=category,
            hours=hours
        )

        # Limit results
        limited_projects = projects_data[:limit]

        # Convert to response model
        result = [
            OptimizedGamingProjectMetrics(
                id=project["id"],
                project_name=project["project_name"],
                name=project["name"],
                blockchain_network=project["blockchain_network"],
                category=project["category"],
                token_symbol=project["token_symbol"],
                token_address=project["token_address"],
                is_active=project["is_active"],
                daily_active_users=project["daily_active_users"],
                monthly_active_users=project["monthly_active_users"],
                total_value_locked=project["total_value_locked"],
                metrics=project["metrics"]
            )
            for project in limited_projects
        ]

        # Cache the result for 10 minutes
        gaming_cache.set(cache_key, result, ttl=600)

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting optimized gaming projects: {str(e)}")


@router.get("/gaming-analytics/blockchain-activity-optimized", response_model=List[OptimizedBlockchainActivity])
async def get_blockchain_activity_optimized(
    db: Session = Depends(get_db),
    hours: int = Query(24, description="Hours to look back for activity"),
    networks: Optional[List[str]] = Query(None, description="Filter by blockchain networks"),
    limit: int = Query(100, description="Maximum number of activities to return")
):
    """Get blockchain activity with gaming project context using optimized SQL joins"""
    try:
        # Check cache first
        networks_key = ",".join(sorted(networks)) if networks else "all"
        cache_key = f"blockchain_activity_optimized:{hours}:{networks_key}:{limit}"
        cached_data = gaming_cache.get(cache_key)
        if cached_data:
            return cached_data

        # Use optimized SQL query
        sql_optimizer = get_sql_optimizer(db)
        activity_data = sql_optimizer.get_blockchain_activity_with_projects_optimized(
            hours=hours,
            networks=networks
        )

        # Limit results
        limited_activity = activity_data[:limit]

        # Convert to response model
        result = [
            OptimizedBlockchainActivity(
                id=activity["id"],
                blockchain_network=activity["blockchain_network"],
                contract_address=activity["contract_address"],
                event_type=activity["event_type"],
                event_name=activity["event_name"],
                block_number=activity["block_number"],
                transaction_hash=activity["transaction_hash"],
                block_timestamp=activity["block_timestamp"],
                player_address=activity["player_address"],
                token_id=activity["token_id"],
                amount=activity["amount"],
                game_action=activity["game_action"],
                gas_used=activity["gas_used"],
                decoded_data=activity["decoded_data"],
                gaming_context=activity["gaming_context"]
            )
            for activity in limited_activity
        ]

        # Cache the result for 5 minutes
        gaming_cache.set(cache_key, result, ttl=300)

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting optimized blockchain activity: {str(e)}")


@router.get("/gaming-analytics/cross-chain-analysis-optimized")
async def get_cross_chain_analysis_optimized(
    db: Session = Depends(get_db),
    days: int = Query(7, description="Days to analyze for cross-chain activity"),
    min_activity_threshold: int = Query(10, description="Minimum activity count to include project")
):
    """Get cross-chain gaming analysis with optimized aggregations"""
    try:
        # Check cache first
        cache_key = f"cross_chain_analysis_optimized:{days}:{min_activity_threshold}"
        cached_data = gaming_cache.get(cache_key)
        if cached_data:
            return cached_data

        sql_optimizer = get_sql_optimizer(db)
        since = datetime.utcnow() - timedelta(days=days)

        # Optimized cross-chain analysis query
        from sqlalchemy import text
        cross_chain_query = text("""
            WITH project_network_activity AS (
                SELECT 
                    gp.project_name,
                    gp.name as display_name,
                    gp.category,
                    bd.blockchain_network,
                    COUNT(DISTINCT bd.id) as activity_count,
                    COUNT(DISTINCT bd.player_address) as unique_players,
                    COUNT(DISTINCT bd.transaction_hash) as unique_transactions,
                    AVG(bd.gas_used) as avg_gas_used,
                    COUNT(DISTINCT DATE(bd.block_timestamp)) as active_days
                FROM gaming_projects gp
                INNER JOIN blockchain_data bd ON (
                    bd.contract_address = gp.token_address
                    OR bd.contract_address = ANY(
                        SELECT nft.contract_address 
                        FROM nft_collections nft 
                        WHERE nft.gaming_project_id = gp.id
                    )
                )
                WHERE bd.block_timestamp >= :since
                    AND gp.is_active = true
                GROUP BY gp.project_name, gp.name, gp.category, bd.blockchain_network
                HAVING COUNT(DISTINCT bd.id) >= :min_activity_threshold
            ),
            network_totals AS (
                SELECT 
                    blockchain_network,
                    COUNT(DISTINCT project_name) as projects_count,
                    SUM(activity_count) as total_activity,
                    SUM(unique_players) as total_unique_players,
                    AVG(avg_gas_used) as network_avg_gas
                FROM project_network_activity
                GROUP BY blockchain_network
            ),
            multi_chain_projects AS (
                SELECT 
                    project_name,
                    display_name,
                    category,
                    COUNT(DISTINCT blockchain_network) as networks_count,
                    SUM(activity_count) as total_cross_chain_activity,
                    SUM(unique_players) as total_cross_chain_players,
                    json_agg(
                        json_build_object(
                            'network', blockchain_network,
                            'activity_count', activity_count,
                            'unique_players', unique_players,
                            'unique_transactions', unique_transactions,
                            'avg_gas_used', avg_gas_used,
                            'active_days', active_days
                        )
                        ORDER BY activity_count DESC
                    ) as network_breakdown
                FROM project_network_activity
                GROUP BY project_name, display_name, category
            )
            SELECT 
                'network_analysis' as analysis_type,
                json_agg(
                    json_build_object(
                        'network', nt.blockchain_network,
                        'projects_count', nt.projects_count,
                        'total_activity', nt.total_activity,
                        'total_unique_players', nt.total_unique_players,
                        'network_avg_gas', nt.network_avg_gas
                    )
                    ORDER BY nt.total_activity DESC
                ) as data
            FROM network_totals nt
            
            UNION ALL
            
            SELECT 
                'multi_chain_projects' as analysis_type,
                json_agg(
                    json_build_object(
                        'project_name', mcp.project_name,
                        'display_name', mcp.display_name,
                        'category', mcp.category,
                        'networks_count', mcp.networks_count,
                        'total_cross_chain_activity', mcp.total_cross_chain_activity,
                        'total_cross_chain_players', mcp.total_cross_chain_players,
                        'network_breakdown', mcp.network_breakdown
                    )
                    ORDER BY mcp.networks_count DESC, mcp.total_cross_chain_activity DESC
                ) as data
            FROM multi_chain_projects mcp
            WHERE mcp.networks_count > 1
        """)

        results = db.execute(cross_chain_query, {
            "since": since,
            "min_activity_threshold": min_activity_threshold
        }).fetchall()

        # Process results
        analysis_data = {}
        for row in results:
            analysis_data[row.analysis_type] = row.data

        result = {
            "analysis_period_days": days,
            "min_activity_threshold": min_activity_threshold,
            "network_analysis": analysis_data.get("network_analysis", []),
            "multi_chain_projects": analysis_data.get("multi_chain_projects", []),
            "summary": {
                "total_networks": len(analysis_data.get("network_analysis", [])),
                "multi_chain_projects_count": len(analysis_data.get("multi_chain_projects", [])),
                "analysis_timestamp": datetime.utcnow().isoformat()
            }
        }

        # Cache the result for 30 minutes
        gaming_cache.set(cache_key, result, ttl=1800)

        return result

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error getting cross-chain analysis: {str(e)}")


@router.get("/gaming-analytics/performance-comparison")
async def compare_query_performance(
    db: Session = Depends(get_db),
    test_type: str = Query("overview", description="Type of query to test: overview, projects, social")
):
    """Compare performance between old and optimized queries for testing"""
    try:
        import time
        
        results = {
            "test_type": test_type,
            "timestamp": datetime.utcnow().isoformat(),
            "performance_comparison": {}
        }
        
        if test_type == "overview":
            # Test old approach (multiple queries)
            start_time = time.time()
            from services.dashboard_analytics import DashboardAnalytics
            analytics = DashboardAnalytics(db)
            
            # Simulate old approach with multiple queries
            total_articles = db.query(Article).count()
            active_networks = await analytics.get_active_blockchain_networks()
            top_categories = await analytics.get_top_gaming_categories(limit=5)
            
            old_time = time.time() - start_time
            
            # Test new optimized approach
            start_time = time.time()
            sql_optimizer = get_sql_optimizer(db)
            overview_data = sql_optimizer.get_dashboard_overview_optimized(24)
            new_time = time.time() - start_time
            
            results["performance_comparison"] = {
                "old_approach_seconds": round(old_time, 4),
                "optimized_approach_seconds": round(new_time, 4),
                "improvement_factor": round(old_time / new_time, 2) if new_time > 0 else "N/A",
                "improvement_percentage": round(((old_time - new_time) / old_time) * 100, 2) if old_time > 0 else "N/A"
            }
        
        return results
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error comparing query performance: {str(e)}")
