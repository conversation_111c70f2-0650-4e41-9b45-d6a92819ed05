"""
FastAPI main application for Web3 Gaming News Tracker
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi import FastAPI, Depends, HTTPException, Query, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON>NR<PERSON>ponse
from typing import List, Optional
import logging
import asyncio
import uuid
import json
import time
from config.settings import get_settings
from models.base import get_db
from api.routes import articles, gaming, blockchain, sources, news
from api.blockchain_endpoints import router as blockchain_router
from monitoring.middleware import MetricsMiddleware
from monitoring.prometheus_metrics import metrics_endpoint
from monitoring.custom_metrics import collect_all_custom_metrics
from utils.error_handling import error_handler, ErrorContext
from api.security_middleware import SecurityMiddleware
from utils.logging_config import get_logger, log_api_request

settings = get_settings()

# Configure logging
logging.basicConfig(level=getattr(logging, settings.monitoring.log_level))
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Web3 Gaming News Tracker API",
    description="""
    Comprehensive API for accessing web3 gaming news, blockchain data, content intelligence, and market analytics.

    ## Features

    * **Gaming News**: Access to curated gaming articles from multiple sources
    * **Blockchain Integration**: Real-time blockchain data from multiple networks
    * **Content Intelligence**: Advanced NLP analysis and sentiment scoring
    * **Market Analytics**: Sector analysis and investment tracking
    * **Competitive Analysis**: Project comparison and market positioning

    ## Phase 7 Enhancements

    * **Advanced NLP Classification**: 11-category gaming content classification
    * **Multi-dimensional Sentiment Analysis**: Community, market, and technical sentiment
    * **Trend Detection**: Emerging and declining trend identification
    * **Market Intelligence**: Investment signals and risk assessment
    * **Entity Recognition**: Gaming project and token extraction
    * **Competitive Metrics**: 8-metric competitive analysis framework

    ## Authentication

    Currently in development mode. Production will include API key authentication and rate limiting.

    ## Rate Limits

    * General API: 1000 requests/hour
    * Content Intelligence: 100 requests/hour
    * Market Analytics: 200 requests/hour
    * Blockchain Data: 500 requests/hour
    """,
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    contact={
        "name": "Web3 Gaming Tracker API Support",
        "email": "<EMAIL>",
    },
    license_info={
        "name": "MIT License",
        "url": "https://opensource.org/licenses/MIT",
    },
    servers=[
        {
            "url": "http://localhost:8000",
            "description": "Development server"
        },
        {
            "url": "https://api.web3gaming-tracker.com",
            "description": "Production server"
        }
    ]
)

# Add CORS middleware with security-focused configuration
allowed_origins = ["*"] if settings.environment == "development" else [
    "https://web3gaming-tracker.com",
    "https://api.web3gaming-tracker.com",
    "https://dashboard.web3gaming-tracker.com"
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization", "X-API-Key", "X-Request-ID"],
    expose_headers=["X-Request-ID", "X-Rate-Limit-Remaining", "X-Rate-Limit-Reset"],
)

# Add security middleware (first for early security checks)
app.add_middleware(SecurityMiddleware)

# Add Prometheus metrics middleware
app.add_middleware(MetricsMiddleware)

# Request logging middleware
@app.middleware("http")
async def request_logging_middleware(request: Request, call_next):
    """Middleware for logging API requests and handling errors"""
    start_time = time.time()
    request_id = str(uuid.uuid4())

    # Add request ID to request state
    request.state.request_id = request_id

    try:
        response = await call_next(request)

        # Calculate response time
        response_time = time.time() - start_time

        # Log successful request
        log_api_request(
            method=request.method,
            endpoint=str(request.url.path),
            status_code=response.status_code,
            response_time=response_time,
            request_id=request_id
        )

        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id

        return response

    except Exception as e:
        # Calculate response time
        response_time = time.time() - start_time

        # Create error context
        context = ErrorContext(
            request_id=request_id,
            endpoint=str(request.url.path),
            method=request.method,
            ip_address=request.client.host if request.client else None,
            user_agent=request.headers.get("user-agent"),
            additional_data={
                "query_params": dict(request.query_params),
                "path_params": dict(request.path_params)
            }
        )

        # Handle error with standardized system
        standard_error = error_handler.handle_error(e, context, raise_http=False)

        # Log failed request
        log_api_request(
            method=request.method,
            endpoint=str(request.url.path),
            status_code=500,
            response_time=response_time,
            request_id=request_id,
            error=str(e)
        )

        # Return standardized error response
        http_exception = error_handler.to_http_exception(standard_error)
        return JSONResponse(
            status_code=http_exception.status_code,
            content=http_exception.detail,
            headers={"X-Request-ID": request_id}
        )


@app.on_event("startup")
async def startup_event():
    """Application startup event"""
    logger.info("Starting Web3 Gaming News Tracker API")
    
    # Test database connection
    try:
        from models.base import engine
        from sqlalchemy import text
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        logger.info("✅ Database connection successful")
    except Exception as e:
        logger.error(f"❌ Database connection failed: {e}")
    
    # Test blockchain connections
    try:
        from blockchain.rpc import test_all_connections
        results = await test_all_connections()
        connected_chains = [chain for chain, status in results.items() if status]
        logger.info(f"✅ Blockchain connections: {connected_chains}")
    except Exception as e:
        logger.error(f"❌ Blockchain connection test failed: {e}")

    # Initialize blockchain data clients
    try:
        from blockchain.data_clients.manager import blockchain_data_manager
        await blockchain_data_manager.initialize_clients()
        client_status = await blockchain_data_manager.get_client_status()
        logger.info(f"✅ Blockchain data clients: {client_status['initialized_clients']}")
    except Exception as e:
        logger.error(f"❌ Blockchain data client initialization failed: {e}")

    # Initialize gaming analytics system
    try:
        from blockchain.gaming_analytics import gaming_analytics
        await gaming_analytics.initialize()
        logger.info("✅ Gaming analytics system initialized")
    except Exception as e:
        logger.error(f"❌ Gaming analytics initialization failed: {e}")

    # Start background metrics collection
    try:
        asyncio.create_task(metrics_collection_loop())
        logger.info("✅ Metrics collection started")
    except Exception as e:
        logger.error(f"❌ Metrics collection startup failed: {e}")

    # Start real-time data streaming
    try:
        from models.base import get_db
        db = next(get_db())
        streamer = RealTimeDataStreamer(db)
        asyncio.create_task(streamer.start_streaming())
        logger.info("✅ Real-time data streaming started")
    except Exception as e:
        logger.error(f"❌ Real-time streaming startup failed: {e}")


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time data streaming"""
    connection_id = str(uuid.uuid4())

    try:
        await manager.connect(websocket, connection_id)

        while True:
            # Receive message from client
            data = await websocket.receive_text()

            try:
                message = json.loads(data)
                await handle_websocket_message(websocket, connection_id, message)
            except json.JSONDecodeError:
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format"
                }, connection_id)
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Error processing message"
                }, connection_id)

    except WebSocketDisconnect:
        manager.disconnect(connection_id)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(connection_id)


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event"""
    logger.info("Shutting down Web3 Gaming News Tracker API")


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Web3 Gaming News Tracker API",
        "version": "1.0.0",
        "docs": "/docs",
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Test database
        from models.base import engine
        from sqlalchemy import text
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"
    
    try:
        # Test Redis
        import redis
        r = redis.from_url(settings.redis.url)
        r.ping()
        redis_status = "healthy"
    except Exception as e:
        redis_status = f"unhealthy: {str(e)}"
    
    return {
        "status": "healthy" if db_status == "healthy" and redis_status == "healthy" else "unhealthy",
        "database": db_status,
        "redis": redis_status,
        "environment": settings.environment
    }


@app.get("/stats")
async def get_stats(db = Depends(get_db)):
    """Get system statistics"""
    try:
        from models.gaming import Article, Source, GamingProject, NFTCollection
        from sqlalchemy import func
        
        # Get article stats
        article_count = db.query(func.count(Article.id)).scalar()
        source_count = db.query(func.count(Source.id)).scalar()
        project_count = db.query(func.count(GamingProject.id)).scalar()
        nft_count = db.query(func.count(NFTCollection.id)).scalar()
        
        # Get recent articles (SQLite compatible)
        from datetime import datetime, timedelta
        yesterday = datetime.now() - timedelta(hours=24)
        recent_articles = db.query(func.count(Article.id)).filter(
            Article.created_at >= yesterday
        ).scalar()
        
        return {
            "articles": {
                "total": article_count,
                "last_24h": recent_articles
            },
            "sources": {
                "total": source_count
            },
            "gaming_projects": {
                "total": project_count
            },
            "nft_collections": {
                "total": nft_count
            }
        }
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving statistics")


# Include routers
app.include_router(articles.router, prefix="/api/v1/articles", tags=["articles"])
app.include_router(gaming.router, prefix="/api/v1/gaming", tags=["gaming"])
app.include_router(blockchain.router, prefix="/api/v1/blockchain", tags=["blockchain"])
app.include_router(sources.router, prefix="/api/v1/sources", tags=["sources"])
app.include_router(news.router)

# Include new blockchain integration endpoints
app.include_router(blockchain_router, prefix="/api/v1")

# Include Phase 4 enhanced gaming endpoints
from api.gaming_endpoints import router as enhanced_gaming_router
app.include_router(enhanced_gaming_router, prefix="/api/v1")

# Include blockchain scraper endpoints
from api.blockchain_scraper_endpoints import router as blockchain_scraper_router
app.include_router(blockchain_scraper_router)

# Include social media endpoints
from api.social_media_endpoints import router as social_media_router
app.include_router(social_media_router, prefix="/api/v1", tags=["social-media"])

# Include Phase 5 dashboard endpoints
from api.dashboard_endpoints import router as dashboard_router
app.include_router(dashboard_router, prefix="/api/v1")

# Include Phase 6 gaming analytics endpoints
from api.gaming_analytics_endpoints import router as gaming_analytics_router
app.include_router(gaming_analytics_router, prefix="/api/v1")

# Include gaming configuration endpoints
from api.gaming_config_api import router as gaming_config_router
app.include_router(gaming_config_router)

# Include gaming form endpoints
from api.gaming_form_endpoints import router as gaming_form_router
app.include_router(gaming_form_router)

# Include gaming review endpoints
from api.gaming_review_endpoints import router as gaming_review_router
app.include_router(gaming_review_router)

# Include notification endpoints
from api.notification_endpoints import router as notification_router
app.include_router(notification_router)

# Include contract vetting endpoints
from api.contract_vetting_endpoints import router as contract_vetting_router
app.include_router(contract_vetting_router)

# Include Phase 7 content intelligence endpoints
from api.content_intelligence_endpoints import router as content_intelligence_router
app.include_router(content_intelligence_router, prefix="/api/v1")

# Include Alchemy Phase 3 enhanced gaming features endpoints
from api.alchemy_phase3_endpoints import router as alchemy_phase3_router
app.include_router(alchemy_phase3_router)

# WebSocket imports and setup
from api.websocket_manager import manager, handle_websocket_message, RealTimeDataStreamer


# Add Prometheus metrics endpoint
@app.get("/metrics")
async def get_metrics():
    """Prometheus metrics endpoint"""
    return await metrics_endpoint()


async def metrics_collection_loop():
    """Background task for collecting custom metrics"""
    while True:
        try:
            await collect_all_custom_metrics()
            await asyncio.sleep(300)  # Collect every 5 minutes
        except Exception as e:
            logger.error(f"Error in metrics collection loop: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler with standardized error handling"""

    # Create error context
    context = ErrorContext(
        request_id=getattr(request.state, 'request_id', None),
        endpoint=str(request.url.path),
        method=request.method,
        ip_address=request.client.host if request.client else None,
        user_agent=request.headers.get("user-agent"),
        additional_data={
            "query_params": dict(request.query_params),
            "path_params": dict(request.path_params)
        }
    )

    # Handle error with standardized system
    standard_error = error_handler.handle_error(exc, context, raise_http=False)

    # Convert to HTTP exception and return response
    http_exception = error_handler.to_http_exception(standard_error)

    return JSONResponse(
        status_code=http_exception.status_code,
        content=http_exception.detail,
        headers={"X-Request-ID": context.request_id or "unknown"}
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host=settings.api.host,
        port=settings.api.port,
        reload=settings.api.debug
    )
