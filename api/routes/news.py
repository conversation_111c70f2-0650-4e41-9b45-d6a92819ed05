"""
News scraping API endpoints
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, List, Optional
import logging

from scrapers.news import (
    news_manager,
    scrape_news_now,
    get_scraping_stats
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/news", tags=["news"])


@router.post("/scrape")
async def trigger_news_scraping(background_tasks: BackgroundTasks):
    """Trigger immediate news scraping"""
    try:
        # Run scraping in background
        background_tasks.add_task(scrape_news_now)
        
        return {
            "status": "started",
            "message": "News scraping started in background"
        }
    except Exception as e:
        logger.error(f"Error triggering news scraping: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/scrape/status")
async def get_scraping_status():
    """Get current scraping status and statistics"""
    try:
        stats = get_scraping_stats()
        return {
            "status": "success",
            "data": stats
        }
    except Exception as e:
        logger.error(f"Error getting scraping status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/scrape/source/{source_slug}")
async def scrape_single_source(source_slug: str, background_tasks: BackgroundTasks):
    """Scrape a single news source"""
    try:
        # Run single source scraping in background
        background_tasks.add_task(news_manager.scrape_single_source, source_slug)
        
        return {
            "status": "started",
            "message": f"Scraping started for source: {source_slug}"
        }
    except Exception as e:
        logger.error(f"Error scraping source {source_slug}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recent")
async def get_recent_articles(hours: int = 24):
    """Get recently scraped articles"""
    try:
        articles = await news_manager.get_recent_articles(hours)
        return {
            "status": "success",
            "data": articles,
            "count": len(articles)
        }
    except Exception as e:
        logger.error(f"Error getting recent articles: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/cleanup")
async def cleanup_old_articles(days: int = 30):
    """Clean up old articles"""
    try:
        deleted_count = await news_manager.cleanup_old_articles(days)
        return {
            "status": "success",
            "message": f"Deleted {deleted_count} old articles",
            "deleted_count": deleted_count
        }
    except Exception as e:
        logger.error(f"Error cleaning up articles: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/sources/config")
async def update_source_configs():
    """Update source configurations"""
    try:
        result = await news_manager.update_source_configs()
        return result
    except Exception as e:
        logger.error(f"Error updating source configs: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/sources/available")
async def get_available_scrapers():
    """Get list of available news scrapers"""
    try:
        from scrapers.news import GAMING_SCRAPERS
        
        scrapers_info = {}
        for source_name, scraper_class in GAMING_SCRAPERS.items():
            scrapers_info[source_name] = {
                "name": source_name,
                "class": scraper_class.__name__,
                "description": scraper_class.__doc__ or "Gaming news scraper"
            }
        
        return {
            "status": "success",
            "data": scrapers_info,
            "count": len(scrapers_info)
        }
    except Exception as e:
        logger.error(f"Error getting available scrapers: {e}")
        raise HTTPException(status_code=500, detail=str(e))
