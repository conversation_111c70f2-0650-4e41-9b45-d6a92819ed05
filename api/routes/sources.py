"""
Sources API routes
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from models.base import get_db
from models.gaming import Source
from pydantic import BaseModel

router = APIRouter()


class SourceResponse(BaseModel):
    """Source response model"""
    id: int
    name: str
    url: str
    source_type: str
    category: Optional[str]
    is_active: bool
    scrape_frequency: int
    reliability_score: float
    article_count: int
    
    class Config:
        from_attributes = True


@router.get("/", response_model=List[SourceResponse])
async def get_sources(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    source_type: Optional[str] = Query(None),
    category: Optional[str] = Query(None),
    is_active: Optional[bool] = Query(None),
    db: Session = Depends(get_db)
):
    """Get news sources"""
    query = db.query(Source)
    
    if source_type:
        query = query.filter(Source.source_type == source_type)
    
    if category:
        query = query.filter(Source.category == category)
    
    if is_active is not None:
        query = query.filter(Source.is_active == is_active)
    
    sources = query.order_by(Source.reliability_score.desc()).offset(skip).limit(limit).all()
    return sources


@router.get("/{source_id}", response_model=SourceResponse)
async def get_source(source_id: int, db: Session = Depends(get_db)):
    """Get a specific source"""
    source = db.query(Source).filter(Source.id == source_id).first()
    if not source:
        raise HTTPException(status_code=404, detail="Source not found")
    return source
