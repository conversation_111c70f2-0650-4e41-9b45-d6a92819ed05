"""
Blockchain API routes
"""
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from models.base import get_db
from models.gaming import BlockchainData
from blockchain.rpc import rpc_manager
from blockchain.data_clients.manager import blockchain_data_manager
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/status")
async def get_blockchain_status():
    """Get blockchain connection status"""
    from blockchain.rpc import test_all_connections
    rpc_results = await test_all_connections()

    # Also get data client status
    data_client_status = await blockchain_data_manager.get_client_status()

    return {
        "rpc_connections": rpc_results,
        "data_clients": data_client_status
    }


@router.post("/data/initialize")
async def initialize_data_clients():
    """Initialize blockchain data clients"""
    try:
        await blockchain_data_manager.initialize_clients()
        status = await blockchain_data_manager.get_client_status()
        return {
            "status": "success",
            "message": "Data clients initialized",
            "client_status": status
        }
    except Exception as e:
        logger.error(f"Failed to initialize data clients: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/data/gaming-tokens")
async def get_gaming_tokens_data(
    tokens: str = Query(..., description="Comma-separated list of token symbols"),
    background_tasks: BackgroundTasks = None
):
    """Get gaming token data from all available sources"""
    try:
        token_list = [token.strip().upper() for token in tokens.split(',')]

        if not token_list:
            raise HTTPException(status_code=400, detail="No tokens specified")

        data = await blockchain_data_manager.get_gaming_tokens_data(token_list)

        return {
            "status": "success",
            "tokens": token_list,
            "data": data,
            "sources": list(blockchain_data_manager.clients.keys())
        }
    except Exception as e:
        logger.error(f"Failed to get gaming tokens data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/data/nft-collection/{collection_address}")
async def get_nft_collection_data(collection_address: str):
    """Get NFT collection data from all available sources"""
    try:
        data = await blockchain_data_manager.get_nft_collection_data(collection_address)

        return {
            "status": "success",
            "collection_address": collection_address,
            "data": data,
            "sources": list(blockchain_data_manager.clients.keys())
        }
    except Exception as e:
        logger.error(f"Failed to get NFT collection data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/data/protocol/{protocol_name}")
async def get_gaming_protocol_metrics(protocol_name: str):
    """Get gaming protocol metrics from all available sources"""
    try:
        data = await blockchain_data_manager.get_gaming_protocol_metrics(protocol_name)

        return {
            "status": "success",
            "protocol_name": protocol_name,
            "data": data,
            "sources": list(blockchain_data_manager.clients.keys())
        }
    except Exception as e:
        logger.error(f"Failed to get gaming protocol metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/data/comprehensive")
async def get_comprehensive_gaming_data():
    """Get comprehensive gaming data from all sources"""
    try:
        data = await blockchain_data_manager.get_comprehensive_gaming_data()

        return {
            "status": "success",
            "data": data
        }
    except Exception as e:
        logger.error(f"Failed to get comprehensive gaming data: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/data/cache")
async def clear_data_cache():
    """Clear blockchain data cache"""
    try:
        blockchain_data_manager.clear_cache()
        return {
            "status": "success",
            "message": "Data cache cleared"
        }
    except Exception as e:
        logger.error(f"Failed to clear data cache: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/data/test-connections")
async def test_data_connections():
    """Test connections to all blockchain data sources"""
    try:
        results = await blockchain_data_manager.test_all_connections()

        return {
            "status": "success",
            "connection_results": results,
            "healthy_connections": sum(1 for connected in results.values() if connected),
            "total_connections": len(results)
        }
    except Exception as e:
        logger.error(f"Failed to test data connections: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/latest-blocks")
async def get_latest_blocks():
    """Get latest block numbers for all chains"""
    results = {}
    for chain in rpc_manager.connections.keys():
        try:
            block_number = await rpc_manager.get_latest_block(chain)
            results[chain] = block_number
        except Exception as e:
            results[chain] = f"Error: {str(e)}"
    return results


@router.get("/block/{chain}/{block_number}")
async def get_block(chain: str, block_number: int):
    """Get block data for a specific chain and block number"""
    if chain not in rpc_manager.connections:
        raise HTTPException(status_code=400, detail=f"Unsupported chain: {chain}")
    
    block_data = await rpc_manager.get_block(chain, block_number)
    if not block_data:
        raise HTTPException(status_code=404, detail="Block not found")
    
    return block_data


@router.get("/transaction/{chain}/{tx_hash}")
async def get_transaction(chain: str, tx_hash: str):
    """Get transaction data"""
    if chain not in rpc_manager.connections:
        raise HTTPException(status_code=400, detail=f"Unsupported chain: {chain}")
    
    tx_data = await rpc_manager.get_transaction(chain, tx_hash)
    if not tx_data:
        raise HTTPException(status_code=404, detail="Transaction not found")
    
    return tx_data


@router.get("/contract/{chain}/{contract_address}")
async def get_contract_info(chain: str, contract_address: str):
    """Get basic contract information"""
    if chain not in rpc_manager.connections:
        raise HTTPException(status_code=400, detail=f"Unsupported chain: {chain}")
    
    from blockchain.rpc import get_gaming_contract_data
    contract_data = await get_gaming_contract_data(chain, contract_address)
    if not contract_data:
        raise HTTPException(status_code=404, detail="Contract not found")
    
    return contract_data


@router.get("/data/")
async def get_blockchain_data(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    blockchain: Optional[str] = Query(None),
    event_type: Optional[str] = Query(None),
    contract_address: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get blockchain data from database"""
    query = db.query(BlockchainData)
    
    if blockchain:
        query = query.filter(BlockchainData.blockchain == blockchain)
    
    if event_type:
        query = query.filter(BlockchainData.event_type == event_type)
    
    if contract_address:
        query = query.filter(BlockchainData.contract_address == contract_address)
    
    data = query.order_by(BlockchainData.block_timestamp.desc()).offset(skip).limit(limit).all()
    return data
