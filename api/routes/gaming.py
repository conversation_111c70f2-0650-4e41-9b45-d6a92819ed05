"""
Gaming API routes
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from models.base import get_db
from models.gaming import GamingProject, NFTCollection
from pydantic import BaseModel

router = APIRouter()


class GamingProjectResponse(BaseModel):
    """Gaming project response model"""
    id: int
    name: str
    slug: str
    description: Optional[str]
    website: Optional[str]
    category: str
    subcategory: Optional[str]
    blockchain: Optional[str]
    token_symbol: Optional[str]
    token_address: Optional[str]
    market_cap: Optional[float]
    token_price: Optional[float]
    daily_active_users: Optional[int]
    total_value_locked: Optional[float]
    is_active: bool
    
    class Config:
        from_attributes = True


@router.get("/projects/", response_model=List[GamingProjectResponse])
async def get_gaming_projects(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    category: Optional[str] = Query(None),
    blockchain: Optional[str] = Query(None),
    is_active: bool = Query(True),
    db: Session = Depends(get_db)
):
    """Get gaming projects"""
    query = db.query(GamingProject)
    
    if category:
        query = query.filter(GamingProject.category == category)
    
    if blockchain:
        query = query.filter(GamingProject.blockchain == blockchain)
    
    if is_active is not None:
        query = query.filter(GamingProject.is_active == is_active)
    
    projects = query.order_by(GamingProject.market_cap.desc()).offset(skip).limit(limit).all()
    return projects


@router.get("/projects/{project_id}", response_model=GamingProjectResponse)
async def get_gaming_project(project_id: int, db: Session = Depends(get_db)):
    """Get a specific gaming project"""
    project = db.query(GamingProject).filter(GamingProject.id == project_id).first()
    if not project:
        raise HTTPException(status_code=404, detail="Gaming project not found")
    return project


@router.get("/nfts/")
async def get_nft_collections(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    gaming_category: Optional[str] = Query(None),
    blockchain: Optional[str] = Query(None),
    is_verified: Optional[bool] = Query(None),
    db: Session = Depends(get_db)
):
    """Get NFT collections"""
    query = db.query(NFTCollection)
    
    if gaming_category:
        query = query.filter(NFTCollection.gaming_category == gaming_category)
    
    if blockchain:
        query = query.filter(NFTCollection.blockchain == blockchain)
    
    if is_verified is not None:
        query = query.filter(NFTCollection.is_verified == is_verified)
    
    collections = query.order_by(NFTCollection.volume_24h.desc()).offset(skip).limit(limit).all()
    return collections
