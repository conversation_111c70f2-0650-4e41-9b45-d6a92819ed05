"""
Articles API routes
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta
from models.base import get_db
from models.gaming import Article, Source
from pydantic import BaseModel

router = APIRouter()


class ArticleResponse(BaseModel):
    """Article response model"""
    id: int
    title: str
    content: Optional[str]
    summary: Optional[str]
    url: str
    author: Optional[str]
    published_at: Optional[datetime]
    gaming_category: Optional[str]
    gaming_subcategory: Optional[str]
    gaming_projects: Optional[List[str]]
    gaming_tokens: Optional[List[str]]
    sentiment_score: Optional[float]
    relevance_score: Optional[float]
    keywords: Optional[List[str]]
    tags: Optional[List[str]]
    views: int
    likes: int
    shares: int
    comments: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


@router.get("/", response_model=List[ArticleResponse])
async def get_articles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    gaming_category: Optional[str] = Query(None),
    gaming_project: Optional[str] = Query(None),
    gaming_token: Optional[str] = Query(None),
    source_id: Optional[int] = Query(None),
    min_relevance: Optional[float] = Query(None, ge=0, le=1),
    from_date: Optional[datetime] = Query(None),
    to_date: Optional[datetime] = Query(None),
    search: Optional[str] = Query(None),
    db: Session = Depends(get_db)
):
    """Get articles with filtering options"""
    query = db.query(Article)
    
    # Apply filters
    if gaming_category:
        query = query.filter(Article.gaming_category == gaming_category)
    
    if gaming_project:
        query = query.filter(Article.gaming_projects.contains([gaming_project]))
    
    if gaming_token:
        query = query.filter(Article.gaming_tokens.contains([gaming_token]))
    
    if source_id:
        query = query.filter(Article.source_id == source_id)
    
    if min_relevance:
        query = query.filter(Article.relevance_score >= min_relevance)
    
    if from_date:
        query = query.filter(Article.published_at >= from_date)
    
    if to_date:
        query = query.filter(Article.published_at <= to_date)
    
    if search:
        query = query.filter(
            Article.title.ilike(f"%{search}%") |
            Article.content.ilike(f"%{search}%")
        )
    
    # Order by relevance and date
    query = query.order_by(
        Article.relevance_score.desc(),
        Article.published_at.desc()
    )
    
    articles = query.offset(skip).limit(limit).all()
    return articles


@router.get("/{article_id}", response_model=ArticleResponse)
async def get_article(article_id: int, db: Session = Depends(get_db)):
    """Get a specific article"""
    article = db.query(Article).filter(Article.id == article_id).first()
    if not article:
        raise HTTPException(status_code=404, detail="Article not found")
    
    # Increment view count
    article.views += 1
    db.commit()
    
    return article


@router.get("/trending/")
async def get_trending_articles(
    hours: int = Query(24, ge=1, le=168),  # 1 hour to 1 week
    limit: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get trending articles based on engagement"""
    since = datetime.utcnow() - timedelta(hours=hours)
    
    articles = db.query(Article).filter(
        Article.published_at >= since
    ).order_by(
        (Article.views + Article.likes + Article.shares).desc(),
        Article.relevance_score.desc()
    ).limit(limit).all()
    
    return articles


@router.get("/categories/")
async def get_gaming_categories(db: Session = Depends(get_db)):
    """Get all gaming categories with article counts"""
    from sqlalchemy import func
    
    categories = db.query(
        Article.gaming_category,
        func.count(Article.id).label('count')
    ).filter(
        Article.gaming_category.isnot(None)
    ).group_by(
        Article.gaming_category
    ).order_by(
        func.count(Article.id).desc()
    ).all()
    
    return [{"category": cat, "count": count} for cat, count in categories]


@router.get("/projects/")
async def get_gaming_projects(db: Session = Depends(get_db)):
    """Get all gaming projects mentioned in articles"""
    from sqlalchemy import func, text
    
    # This query unnests the gaming_projects array and counts occurrences
    result = db.execute(text("""
        SELECT project, COUNT(*) as count
        FROM articles, unnest(gaming_projects) as project
        WHERE gaming_projects IS NOT NULL
        GROUP BY project
        ORDER BY count DESC
        LIMIT 50
    """)).fetchall()
    
    return [{"project": project, "count": count} for project, count in result]


@router.get("/tokens/")
async def get_gaming_tokens(db: Session = Depends(get_db)):
    """Get all gaming tokens mentioned in articles"""
    from sqlalchemy import func, text
    
    # This query unnests the gaming_tokens array and counts occurrences
    result = db.execute(text("""
        SELECT token, COUNT(*) as count
        FROM articles, unnest(gaming_tokens) as token
        WHERE gaming_tokens IS NOT NULL
        GROUP BY token
        ORDER BY count DESC
        LIMIT 50
    """)).fetchall()
    
    return [{"token": token, "count": count} for token, count in result]
