import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Tabs,
  Tab,
  Paper,
  CircularProgress,
  Alert,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  Psychology as AIIcon,
  TrendingUp as TrendIcon,
  Sentiment as SentimentIcon,
  Category as CategoryIcon,
  Analytics as AnalyticsIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';

import { contentIntelligenceAPI } from '../services/api';

const COLORS = ['#00d4ff', '#ff6b35', '#4caf50', '#ff9800', '#9c27b0', '#f44336', '#2196f3', '#795548'];

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`content-intelligence-tabpanel-${index}`}
      aria-labelledby={`content-intelligence-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ContentIntelligence = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [timeframe, setTimeframe] = useState('24h');
  const [category, setCategory] = useState('');
  const queryClient = useQueryClient();

  // Fetch dashboard data
  const { data: dashboardData, isLoading, error, refetch } = useQuery(
    ['content-intelligence-dashboard', timeframe, category],
    () => contentIntelligenceAPI.getDashboard(timeframe, category),
    {
      refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
      staleTime: 2 * 60 * 1000, // Consider data stale after 2 minutes
    }
  );

  // Health check
  const { data: healthData } = useQuery(
    'content-intelligence-health',
    contentIntelligenceAPI.getHealth,
    {
      refetchInterval: 30 * 1000, // Check health every 30 seconds
    }
  );

  // Manual refresh mutation
  const refreshMutation = useMutation(
    () => contentIntelligenceAPI.getDashboard(timeframe, category),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('content-intelligence-dashboard');
      },
    }
  );

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const handleTimeframeChange = (event) => {
    setTimeframe(event.target.value);
  };

  const handleCategoryChange = (event) => {
    setCategory(event.target.value);
  };

  const handleRefresh = () => {
    refreshMutation.mutate();
  };

  const getSentimentColor = (sentiment) => {
    if (sentiment > 0.1) return '#4caf50'; // Positive - Green
    if (sentiment < -0.1) return '#f44336'; // Negative - Red
    return '#ff9800'; // Neutral - Orange
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading content intelligence data: {error.message}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ color: 'primary.main' }}>
          Content Intelligence & Market Analytics
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Timeframe</InputLabel>
            <Select value={timeframe} onChange={handleTimeframeChange} label="Timeframe">
              <MenuItem value="1h">1 Hour</MenuItem>
              <MenuItem value="6h">6 Hours</MenuItem>
              <MenuItem value="24h">24 Hours</MenuItem>
              <MenuItem value="7d">7 Days</MenuItem>
              <MenuItem value="30d">30 Days</MenuItem>
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Category</InputLabel>
            <Select value={category} onChange={handleCategoryChange} label="Category">
              <MenuItem value="">All Categories</MenuItem>
              <MenuItem value="p2e">Play-to-Earn</MenuItem>
              <MenuItem value="nft">NFT Gaming</MenuItem>
              <MenuItem value="defi">DeFi Gaming</MenuItem>
              <MenuItem value="metaverse">Metaverse</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleRefresh}
            disabled={refreshMutation.isLoading}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Health Status */}
      {healthData && (
        <Alert 
          severity={healthData.status === 'healthy' ? 'success' : 'warning'} 
          sx={{ mb: 3 }}
        >
          Content Intelligence Services: {healthData.status}
          {healthData.test_classification && (
            <Typography variant="body2" sx={{ mt: 1 }}>
              Test Classification: {healthData.test_classification.primary_category} 
              (Confidence: {formatPercentage(healthData.test_classification.confidence)})
            </Typography>
          )}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AnalyticsIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Total Articles</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                {dashboardData?.summary?.total_articles || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Analyzed in {timeframe}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SentimentIcon sx={{ mr: 1, color: getSentimentColor(dashboardData?.sentiment_analysis?.overall_market_sentiment || 0) }} />
                <Typography variant="h6">Market Sentiment</Typography>
              </Box>
              <Typography 
                variant="h4" 
                sx={{ color: getSentimentColor(dashboardData?.sentiment_analysis?.overall_market_sentiment || 0) }}
              >
                {dashboardData?.sentiment_analysis?.overall_market_sentiment > 0 ? 'Positive' : 
                 dashboardData?.sentiment_analysis?.overall_market_sentiment < 0 ? 'Negative' : 'Neutral'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Trend: {dashboardData?.sentiment_analysis?.trend_direction || 'Stable'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendIcon sx={{ mr: 1, color: 'success.main' }} />
                <Typography variant="h6">Market Phase</Typography>
              </Box>
              <Typography variant="h4" color="success.main">
                {dashboardData?.trend_analysis?.market_phase || 'Unknown'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Current market condition
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AIIcon sx={{ mr: 1, color: 'secondary.main' }} />
                <Typography variant="h6">AI Confidence</Typography>
              </Box>
              <Typography variant="h4" color="secondary.main">
                {dashboardData?.trend_analysis?.trend_scores?.confidence ? 
                  formatPercentage(dashboardData.trend_analysis.trend_scores.confidence) : 'N/A'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Analysis reliability
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label="content intelligence tabs">
          <Tab label="Category Analysis" icon={<CategoryIcon />} />
          <Tab label="Sentiment Tracking" icon={<SentimentIcon />} />
          <Tab label="Trend Detection" icon={<TrendIcon />} />
          <Tab label="Market Intelligence" icon={<AnalyticsIcon />} />
        </Tabs>
      </Paper>

      {/* Tab Panels */}
      <TabPanel value={selectedTab} index={0}>
        <Grid container spacing={3}>
          {/* Category Distribution */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Content Category Distribution
                </Typography>
                {dashboardData?.category_distribution && (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={Object.entries(dashboardData.category_distribution).map(([key, value]) => ({
                          name: key.toUpperCase(),
                          value: value,
                          percentage: (value / dashboardData.summary.total_articles * 100).toFixed(1)
                        }))}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percentage }) => `${name}: ${percentage}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {Object.entries(dashboardData.category_distribution).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Category Performance */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Category Performance Metrics
                </Typography>
                <Box sx={{ mt: 2 }}>
                  {dashboardData?.category_distribution && Object.entries(dashboardData.category_distribution).map(([category, count]) => (
                    <Box key={category} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">{category.toUpperCase()}</Typography>
                        <Typography variant="body2">{count} articles</Typography>
                      </Box>
                      <LinearProgress 
                        variant="determinate" 
                        value={(count / dashboardData.summary.total_articles) * 100}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={selectedTab} index={1}>
        <Grid container spacing={3}>
          {/* Sentiment by Category */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sentiment Analysis by Category
                </Typography>
                {dashboardData?.sentiment_analysis?.sentiment_by_category && (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={Object.entries(dashboardData.sentiment_analysis.sentiment_by_category).map(([key, value]) => ({
                      category: key.toUpperCase(),
                      sentiment: value,
                      color: getSentimentColor(value)
                    }))}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="category" />
                      <YAxis domain={[-1, 1]} />
                      <Tooltip 
                        formatter={(value) => [value.toFixed(3), 'Sentiment Score']}
                        labelFormatter={(label) => `Category: ${label}`}
                      />
                      <Bar dataKey="sentiment" fill="#8884d8">
                        {Object.entries(dashboardData.sentiment_analysis.sentiment_by_category).map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={getSentimentColor(entry[1])} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Sentiment Summary */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sentiment Summary
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Overall Market Sentiment</Typography>
                    <Typography 
                      variant="h5" 
                      sx={{ color: getSentimentColor(dashboardData?.sentiment_analysis?.overall_market_sentiment || 0) }}
                    >
                      {(dashboardData?.sentiment_analysis?.overall_market_sentiment || 0).toFixed(3)}
                    </Typography>
                  </Box>
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary">Trend Direction</Typography>
                    <Chip 
                      label={dashboardData?.sentiment_analysis?.trend_direction || 'Unknown'}
                      color={
                        dashboardData?.sentiment_analysis?.trend_direction === 'bullish' ? 'success' :
                        dashboardData?.sentiment_analysis?.trend_direction === 'bearish' ? 'error' : 'default'
                      }
                      size="small"
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={selectedTab} index={2}>
        <Grid container spacing={3}>
          {/* Emerging Themes */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Emerging Themes
                </Typography>
                <Box sx={{ mt: 2 }}>
                  {dashboardData?.trend_analysis?.emerging_themes?.map((theme, index) => (
                    <Chip
                      key={index}
                      label={theme}
                      sx={{ mr: 1, mb: 1 }}
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Trend Scores */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Trend Analysis Metrics
                </Typography>
                <Box sx={{ mt: 2 }}>
                  {dashboardData?.trend_analysis?.trend_scores && Object.entries(dashboardData.trend_analysis.trend_scores).map(([key, value]) => (
                    <Box key={key} sx={{ mb: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">{key.replace('_', ' ').toUpperCase()}</Typography>
                        <Typography variant="body2">{typeof value === 'number' ? value.toFixed(3) : value}</Typography>
                      </Box>
                      {typeof value === 'number' && (
                        <LinearProgress 
                          variant="determinate" 
                          value={Math.abs(value) * 100}
                          sx={{ height: 6, borderRadius: 3 }}
                        />
                      )}
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={selectedTab} index={3}>
        <Grid container spacing={3}>
          {/* Investment Signals */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Investment Signals
                </Typography>
                <Box sx={{ mt: 2 }}>
                  {dashboardData?.market_intelligence?.investment_signals?.map((signal, index) => (
                    <Alert 
                      key={index}
                      severity={signal.type === 'bullish' ? 'success' : signal.type === 'bearish' ? 'error' : 'info'}
                      sx={{ mb: 1 }}
                    >
                      <Typography variant="body2">{signal.message || signal}</Typography>
                    </Alert>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Risk Assessment */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Risk Assessment
                </Typography>
                <Box sx={{ mt: 2 }}>
                  {dashboardData?.market_intelligence?.risk_assessment && Object.entries(dashboardData.market_intelligence.risk_assessment).map(([key, value]) => (
                    <Box key={key} sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        {key.replace('_', ' ').toUpperCase()}
                      </Typography>
                      <Typography variant="body1">
                        {typeof value === 'number' ? value.toFixed(2) : value}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default ContentIntelligence;
