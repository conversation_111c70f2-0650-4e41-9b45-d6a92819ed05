import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  Tabs,
  Tab,
  Paper,
  CircularProgress,
  Alert,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Badge,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Assessment as AssessmentIcon,
  CompareArrows as CompareIcon,
  Portfolio as PortfolioIcon,
  Notifications as AlertIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from 'recharts';

import { contentIntelligenceAPI } from '../services/api';

const COLORS = ['#00d4ff', '#ff6b35', '#4caf50', '#ff9800', '#9c27b0', '#f44336', '#2196f3', '#795548'];

function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`market-analytics-tabpanel-${index}`}
      aria-labelledby={`market-analytics-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const MarketAnalytics = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [timeframe, setTimeframe] = useState('7d');
  const [selectedProjects, setSelectedProjects] = useState([]);
  const queryClient = useQueryClient();

  // Fetch sector analysis
  const { data: sectorData, isLoading: sectorLoading, error: sectorError } = useQuery(
    ['sector-analysis', timeframe],
    () => contentIntelligenceAPI.getSectorAnalysis(timeframe),
    {
      refetchInterval: 10 * 60 * 1000, // Refresh every 10 minutes
    }
  );

  // Fetch market alerts
  const { data: alertsData, isLoading: alertsLoading } = useQuery(
    'market-alerts',
    () => contentIntelligenceAPI.getMarketAlerts(),
    {
      refetchInterval: 2 * 60 * 1000, // Refresh every 2 minutes
    }
  );

  // Fetch competitive landscape
  const { data: competitiveData, isLoading: competitiveLoading } = useQuery(
    ['competitive-landscape', selectedProjects],
    () => contentIntelligenceAPI.getCompetitiveLandscape(selectedProjects.length > 0 ? selectedProjects : null),
    {
      refetchInterval: 15 * 60 * 1000, // Refresh every 15 minutes
    }
  );

  // Monitor projects mutation
  const monitorMutation = useMutation(
    (projects) => contentIntelligenceAPI.monitorProjects(projects),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('market-alerts');
      },
    }
  );

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  const handleTimeframeChange = (event) => {
    setTimeframe(event.target.value);
  };

  const handleProjectSelection = (event) => {
    setSelectedProjects(event.target.value);
  };

  const handleMonitorProjects = () => {
    if (selectedProjects.length > 0) {
      monitorMutation.mutate(selectedProjects);
    }
  };

  const getAlertSeverityColor = (severity) => {
    switch (severity?.toLowerCase()) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  const getAlertIcon = (alertType) => {
    switch (alertType?.toLowerCase()) {
      case 'price_movement': return <TrendingUpIcon />;
      case 'volume_spike': return <AssessmentIcon />;
      case 'news_alert': return <InfoIcon />;
      default: return <WarningIcon />;
    }
  };

  const formatCurrency = (value) => {
    if (!value) return 'N/A';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatPercentage = (value) => {
    if (value === null || value === undefined) return 'N/A';
    return `${(value * 100).toFixed(2)}%`;
  };

  if (sectorLoading && alertsLoading && competitiveLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ color: 'primary.main' }}>
          Market Analytics & Intelligence
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Timeframe</InputLabel>
            <Select value={timeframe} onChange={handleTimeframeChange} label="Timeframe">
              <MenuItem value="1d">1 Day</MenuItem>
              <MenuItem value="7d">7 Days</MenuItem>
              <MenuItem value="30d">30 Days</MenuItem>
              <MenuItem value="90d">90 Days</MenuItem>
            </Select>
          </FormControl>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Monitor Projects</InputLabel>
            <Select
              multiple
              value={selectedProjects}
              onChange={handleProjectSelection}
              label="Monitor Projects"
              renderValue={(selected) => (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {selected.map((value) => (
                    <Chip key={value} label={value} size="small" />
                  ))}
                </Box>
              )}
            >
              <MenuItem value="axie-infinity">Axie Infinity</MenuItem>
              <MenuItem value="star-atlas">Star Atlas</MenuItem>
              <MenuItem value="gala-games">Gala Games</MenuItem>
              <MenuItem value="decentraland">Decentraland</MenuItem>
              <MenuItem value="the-sandbox">The Sandbox</MenuItem>
            </Select>
          </FormControl>
          <Button
            variant="outlined"
            startIcon={<AlertIcon />}
            onClick={handleMonitorProjects}
            disabled={selectedProjects.length === 0 || monitorMutation.isLoading}
          >
            Monitor
          </Button>
        </Box>
      </Box>

      {/* Alert Summary */}
      {alertsData && alertsData.alerts && alertsData.alerts.length > 0 && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="h6">Active Market Alerts</Typography>
          <Typography variant="body2">
            {alertsData.alerts.length} active alerts - Check the Alerts tab for details
          </Typography>
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <AssessmentIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Market Cap</Typography>
              </Box>
              <Typography variant="h4" color="primary">
                {formatCurrency(sectorData?.total_market_cap)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Gaming sector total
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUpIcon sx={{ mr: 1, color: 'success.main' }} />
                <Typography variant="h6">24h Change</Typography>
              </Box>
              <Typography 
                variant="h4" 
                sx={{ color: (sectorData?.market_change_24h || 0) >= 0 ? 'success.main' : 'error.main' }}
              >
                {formatPercentage(sectorData?.market_change_24h)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Market movement
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PortfolioIcon sx={{ mr: 1, color: 'warning.main' }} />
                <Typography variant="h6">Active Projects</Typography>
              </Box>
              <Typography variant="h4" color="warning.main">
                {sectorData?.active_projects || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Tracked protocols
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Badge badgeContent={alertsData?.alerts?.length || 0} color="error">
                  <AlertIcon sx={{ mr: 1, color: 'error.main' }} />
                </Badge>
                <Typography variant="h6">Alerts</Typography>
              </Box>
              <Typography variant="h4" color="error.main">
                {alertsData?.alerts?.length || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Active alerts
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={selectedTab} onChange={handleTabChange} aria-label="market analytics tabs">
          <Tab label="Sector Analysis" icon={<AssessmentIcon />} />
          <Tab label="Market Alerts" icon={<AlertIcon />} />
          <Tab label="Competitive Analysis" icon={<CompareIcon />} />
          <Tab label="Portfolio Tracking" icon={<PortfolioIcon />} />
        </Tabs>
      </Paper>

      {/* Tab Panels */}
      <TabPanel value={selectedTab} index={0}>
        <Grid container spacing={3}>
          {/* Market Performance Chart */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Gaming Sector Performance
                </Typography>
                {sectorData?.performance_data && (
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={sectorData.performance_data}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Line type="monotone" dataKey="market_cap" stroke="#8884d8" strokeWidth={2} />
                      <Line type="monotone" dataKey="volume" stroke="#82ca9d" strokeWidth={2} />
                    </LineChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Top Performers */}
          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Top Performers ({timeframe})
                </Typography>
                <List>
                  {sectorData?.top_performers?.slice(0, 5).map((project, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        <TrendingUpIcon color="success" />
                      </ListItemIcon>
                      <ListItemText
                        primary={project.name}
                        secondary={`${formatPercentage(project.change)} change`}
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Sector Metrics */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sector Health Metrics
                </Typography>
                {sectorData?.health_metrics && (
                  <ResponsiveContainer width="100%" height={300}>
                    <RadarChart data={Object.entries(sectorData.health_metrics).map(([key, value]) => ({
                      metric: key.replace('_', ' ').toUpperCase(),
                      value: typeof value === 'number' ? value * 100 : 0
                    }))}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="metric" />
                      <PolarRadiusAxis angle={90} domain={[0, 100]} />
                      <Radar name="Health Score" dataKey="value" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                    </RadarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={selectedTab} index={1}>
        <Grid container spacing={3}>
          {/* Active Alerts */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Active Market Alerts
                </Typography>
                {alertsData?.alerts && alertsData.alerts.length > 0 ? (
                  <List>
                    {alertsData.alerts.map((alert, index) => (
                      <ListItem key={index}>
                        <ListItemIcon>
                          {getAlertIcon(alert.alert_type)}
                        </ListItemIcon>
                        <ListItemText
                          primary={
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body1">{alert.project_name}</Typography>
                              <Chip 
                                label={alert.severity} 
                                color={getAlertSeverityColor(alert.severity)}
                                size="small"
                              />
                            </Box>
                          }
                          secondary={
                            <Box>
                              <Typography variant="body2">{alert.message}</Typography>
                              <Typography variant="caption" color="text.secondary">
                                {new Date(alert.timestamp).toLocaleString()}
                              </Typography>
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>
                ) : (
                  <Alert severity="info">No active alerts</Alert>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={selectedTab} index={2}>
        <Grid container spacing={3}>
          {/* Competitive Landscape */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Competitive Landscape Analysis
                </Typography>
                {competitiveData?.projects && (
                  <ResponsiveContainer width="100%" height={400}>
                    <BarChart data={competitiveData.projects}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="market_cap" fill="#8884d8" />
                      <Bar dataKey="user_count" fill="#82ca9d" />
                    </BarChart>
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      <TabPanel value={selectedTab} index={3}>
        <Grid container spacing={3}>
          {/* Portfolio Overview */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Portfolio Tracking
                </Typography>
                <Alert severity="info">
                  Portfolio tracking feature coming soon. Use the monitor projects feature to track specific gaming protocols.
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>
    </Box>
  );
};

export default MarketAnalytics;
