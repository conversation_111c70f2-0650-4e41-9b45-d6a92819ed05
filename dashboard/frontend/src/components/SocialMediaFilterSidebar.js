import React, { useState, useEffect } from 'react';
import {
  Box,
  Drawer,
  Typo<PERSON>,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Alert,
  Tooltip,
  Fab
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  ExpandMore as ExpandMoreIcon,
  FilterList as FilterListIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { socialMediaAPI } from '../services/api';

const SocialMediaFilterSidebar = ({ open, onClose, onFiltersChange }) => {
  const [filters, setFilters] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingFilter, setEditingFilter] = useState(null);
  const [formData, setFormData] = useState({
    filter_name: '',
    filter_type: 'both',
    keywords: [],
    excluded_keywords: [],
    gaming_projects: [],
    gaming_influencers: [],
    gaming_tokens: [],
    min_engagement_score: 0,
    min_relevance_score: 0.0,
    min_sentiment_score: -1.0,
    max_age_hours: 24,
    description: ''
  });

  // Dynamic options loaded from API
  const [predefinedProjects, setPredefinedProjects] = useState([]);
  const [predefinedInfluencers, setPredefinedInfluencers] = useState([
    'Filbertsteiner', 'Amanda Zhu', 'jihoz_axie', 'sinjinMAYG'
  ]);
  const [predefinedKeywords, setPredefinedKeywords] = useState([
    'p2e', 'playtoearn', 'web3', 'cryptogaming', 'blockchain gaming',
    'nft gaming', 'gamefi', 'metaverse', 'guild', 'scholarship'
  ]);

  useEffect(() => {
    if (open) {
      loadFilters();
      loadPredefinedProjects();
    }
  }, [open]);

  const loadPredefinedProjects = async () => {
    try {
      const response = await fetch('/api/v1/gaming');
      if (response.ok) {
        const data = await response.json();
        const projectNames = data.projects?.map(p => p.name) || [];
        setPredefinedProjects(projectNames);
      }
    } catch (error) {
      console.error('Failed to load projects:', error);
      // Fallback to hardcoded list
      setPredefinedProjects([
        'Race Poker', 'Axie Infinity', 'Gala Games', 'Honeyland',
        'Sunflowerland', 'Hamster Kombat', 'Decentraland', 'MAYG', 'Star Atlas'
      ]);
    }
  };

  const loadFilters = async () => {
    try {
      setLoading(true);
      const response = await socialMediaAPI.getFilters();
      setFilters(response.data.filters);
    } catch (err) {
      console.error('Error loading filters:', err);
      setError('Failed to load filters');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateFilter = () => {
    setEditingFilter(null);
    setFormData({
      filter_name: '',
      filter_type: 'both',
      keywords: [],
      excluded_keywords: [],
      gaming_projects: [],
      gaming_influencers: [],
      gaming_tokens: [],
      min_engagement_score: 0,
      min_relevance_score: 0.0,
      min_sentiment_score: -1.0,
      max_age_hours: 24,
      description: ''
    });
    setDialogOpen(true);
  };

  const handleEditFilter = (filter) => {
    setEditingFilter(filter);
    setFormData({
      filter_name: filter.filter_name,
      filter_type: filter.filter_type,
      keywords: filter.keywords || [],
      excluded_keywords: filter.excluded_keywords || [],
      gaming_projects: filter.gaming_projects || [],
      gaming_influencers: filter.gaming_influencers || [],
      gaming_tokens: filter.gaming_tokens || [],
      min_engagement_score: filter.min_engagement_score,
      min_relevance_score: filter.min_relevance_score,
      min_sentiment_score: filter.min_sentiment_score,
      max_age_hours: filter.max_age_hours,
      description: filter.description || ''
    });
    setDialogOpen(true);
  };

  const handleSaveFilter = async () => {
    try {
      if (editingFilter) {
        await socialMediaAPI.updateFilter(editingFilter.id, formData);
      } else {
        await socialMediaAPI.createFilter(formData);
      }
      setDialogOpen(false);
      loadFilters();
      if (onFiltersChange) onFiltersChange();
    } catch (err) {
      console.error('Error saving filter:', err);
      setError('Failed to save filter');
    }
  };

  const handleDeleteFilter = async (filterId) => {
    if (window.confirm('Are you sure you want to delete this filter?')) {
      try {
        await socialMediaAPI.deleteFilter(filterId);
        loadFilters();
        if (onFiltersChange) onFiltersChange();
      } catch (err) {
        console.error('Error deleting filter:', err);
        setError('Failed to delete filter');
      }
    }
  };

  const handleToggleFilter = async (filterId) => {
    try {
      await socialMediaAPI.toggleFilter(filterId);
      loadFilters();
      if (onFiltersChange) onFiltersChange();
    } catch (err) {
      console.error('Error toggling filter:', err);
      setError('Failed to toggle filter');
    }
  };

  const handleToggleLock = async (filterId) => {
    try {
      await socialMediaAPI.toggleFilterLock(filterId);
      loadFilters();
    } catch (err) {
      console.error('Error toggling lock:', err);
      setError('Failed to toggle lock');
    }
  };

  const handleArrayFieldChange = (field, value) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({ ...prev, [field]: items }));
  };

  const addPredefinedItem = (field, item) => {
    setFormData(prev => ({
      ...prev,
      [field]: [...(prev[field] || []), item].filter((v, i, a) => a.indexOf(v) === i)
    }));
  };

  const removePredefinedItem = (field, item) => {
    setFormData(prev => ({
      ...prev,
      [field]: (prev[field] || []).filter(i => i !== item)
    }));
  };

  return (
    <>
      <Drawer
        anchor="right"
        open={open}
        onClose={onClose}
        sx={{
          '& .MuiDrawer-paper': {
            width: 400,
            p: 2
          }
        }}
      >
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">Social Media Filters</Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateFilter}
          fullWidth
          sx={{ mb: 2 }}
        >
          Create New Filter
        </Button>

        <List>
          {filters.map((filter) => (
            <ListItem key={filter.id} divider>
              <ListItemText
                primary={
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography variant="subtitle2">{filter.filter_name}</Typography>
                    <Chip
                      label={filter.filter_type}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                    {filter.is_locked && <LockIcon fontSize="small" />}
                  </Box>
                }
                secondary={
                  <Box>
                    <Typography variant="caption" display="block">
                      {filter.description || 'No description'}
                    </Typography>
                    <Box display="flex" gap={0.5} mt={0.5}>
                      {filter.keywords?.slice(0, 3).map((keyword, index) => (
                        <Chip key={index} label={keyword} size="small" />
                      ))}
                      {filter.keywords?.length > 3 && (
                        <Chip label={`+${filter.keywords.length - 3}`} size="small" />
                      )}
                    </Box>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={filter.is_active}
                          onChange={() => handleToggleFilter(filter.id)}
                          size="small"
                        />
                      }
                      label="Active"
                      sx={{ mt: 0.5 }}
                    />
                  </Box>
                }
              />
              <ListItemSecondaryAction>
                <Box display="flex" gap={0.5}>
                  <Tooltip title={filter.is_locked ? "Unlock" : "Lock"}>
                    <IconButton
                      size="small"
                      onClick={() => handleToggleLock(filter.id)}
                    >
                      {filter.is_locked ? <LockIcon /> : <LockOpenIcon />}
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Edit">
                    <IconButton
                      size="small"
                      onClick={() => handleEditFilter(filter)}
                      disabled={filter.is_locked}
                    >
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete">
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteFilter(filter.id)}
                      disabled={filter.is_locked}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </ListItemSecondaryAction>
            </ListItem>
          ))}
        </List>
      </Drawer>

      {/* Filter Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingFilter ? 'Edit Filter' : 'Create New Filter'}
        </DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} mt={1}>
            <TextField
              label="Filter Name"
              value={formData.filter_name}
              onChange={(e) => setFormData(prev => ({ ...prev, filter_name: e.target.value }))}
              fullWidth
              required
            />

            <FormControl fullWidth>
              <InputLabel>Filter Type</InputLabel>
              <Select
                value={formData.filter_type}
                onChange={(e) => setFormData(prev => ({ ...prev, filter_type: e.target.value }))}
              >
                <MenuItem value="twitter">Twitter Only</MenuItem>
                <MenuItem value="reddit">Reddit Only</MenuItem>
                <MenuItem value="both">Both Platforms</MenuItem>
              </Select>
            </FormControl>

            <TextField
              label="Description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              fullWidth
              multiline
              rows={2}
            />

            {/* Keywords Section */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Keywords & Content Filters</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box display="flex" flexDirection="column" gap={2}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>Quick Add Keywords:</Typography>
                    <Box display="flex" flexWrap="wrap" gap={0.5}>
                      {predefinedKeywords.map((keyword) => (
                        <Chip
                          key={keyword}
                          label={keyword}
                          size="small"
                          onClick={() => addPredefinedItem('keywords', keyword)}
                          color={formData.keywords.includes(keyword) ? 'primary' : 'default'}
                        />
                      ))}
                    </Box>
                  </Box>
                  <TextField
                    label="Keywords (comma-separated)"
                    value={formData.keywords.join(', ')}
                    onChange={(e) => handleArrayFieldChange('keywords', e.target.value)}
                    fullWidth
                    helperText="Keywords to include in posts"
                  />
                  <TextField
                    label="Excluded Keywords (comma-separated)"
                    value={formData.excluded_keywords.join(', ')}
                    onChange={(e) => handleArrayFieldChange('excluded_keywords', e.target.value)}
                    fullWidth
                    helperText="Keywords to exclude from posts"
                  />
                </Box>
              </AccordionDetails>
            </Accordion>

            {/* Gaming Projects Section */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Gaming Projects</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box display="flex" flexDirection="column" gap={2}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>Quick Add Projects:</Typography>
                    <Box display="flex" flexWrap="wrap" gap={0.5}>
                      {predefinedProjects.map((project) => (
                        <Chip
                          key={project}
                          label={project}
                          size="small"
                          onClick={() => addPredefinedItem('gaming_projects', project)}
                          color={formData.gaming_projects.includes(project) ? 'primary' : 'default'}
                        />
                      ))}
                    </Box>
                  </Box>
                  <TextField
                    label="Gaming Projects (comma-separated)"
                    value={formData.gaming_projects.join(', ')}
                    onChange={(e) => handleArrayFieldChange('gaming_projects', e.target.value)}
                    fullWidth
                  />
                </Box>
              </AccordionDetails>
            </Accordion>

            {/* Gaming Influencers Section */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Gaming Influencers</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box display="flex" flexDirection="column" gap={2}>
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>Quick Add Influencers:</Typography>
                    <Box display="flex" flexWrap="wrap" gap={0.5}>
                      {predefinedInfluencers.map((influencer) => (
                        <Chip
                          key={influencer}
                          label={influencer}
                          size="small"
                          onClick={() => addPredefinedItem('gaming_influencers', influencer)}
                          color={formData.gaming_influencers.includes(influencer) ? 'primary' : 'default'}
                        />
                      ))}
                    </Box>
                  </Box>
                  <TextField
                    label="Gaming Influencers (comma-separated)"
                    value={formData.gaming_influencers.join(', ')}
                    onChange={(e) => handleArrayFieldChange('gaming_influencers', e.target.value)}
                    fullWidth
                  />
                </Box>
              </AccordionDetails>
            </Accordion>

            {/* Quality Thresholds */}
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography>Quality Thresholds</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Box display="flex" flexDirection="column" gap={2}>
                  <TextField
                    label="Minimum Engagement Score"
                    type="number"
                    value={formData.min_engagement_score}
                    onChange={(e) => setFormData(prev => ({ ...prev, min_engagement_score: parseInt(e.target.value) }))}
                    fullWidth
                    helperText="Minimum likes/upvotes required"
                  />
                  <TextField
                    label="Maximum Age (hours)"
                    type="number"
                    value={formData.max_age_hours}
                    onChange={(e) => setFormData(prev => ({ ...prev, max_age_hours: parseInt(e.target.value) }))}
                    fullWidth
                    helperText="Maximum age of posts to include"
                  />
                </Box>
              </AccordionDetails>
            </Accordion>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)} startIcon={<CancelIcon />}>
            Cancel
          </Button>
          <Button onClick={handleSaveFilter} variant="contained" startIcon={<SaveIcon />}>
            {editingFilter ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Floating Action Button */}
      {!open && (
        <Fab
          color="primary"
          aria-label="filters"
          sx={{
            position: 'fixed',
            bottom: 16,
            right: 16,
            zIndex: 1000
          }}
          onClick={() => onClose(false)}
        >
          <FilterListIcon />
        </Fab>
      )}
    </>
  );
};

export default SocialMediaFilterSidebar;
