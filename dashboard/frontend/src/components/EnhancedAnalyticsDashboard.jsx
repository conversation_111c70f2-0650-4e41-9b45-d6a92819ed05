import React, { useState, useEffect } from 'react';
import { gamingAnalyticsAPI } from '../services/api';
import '../styles/components.css';

const EnhancedAnalyticsDashboard = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [selectedProtocol, setSelectedProtocol] = useState('axie-infinity');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  const protocols = [
    { id: 'axie-infinity', name: 'Axie Infinity' },
    { id: 'the-sandbox', name: 'The Sandbox' },
    { id: 'decentraland', name: 'Decentraland' },
    { id: 'gala-games', name: 'Gala Games' },
    { id: 'splinterlands', name: 'Splinterlands' }
  ];

  const tabs = [
    { id: 'overview', name: 'Overview', icon: '📊' },
    { id: 'tvl', name: 'TVL', icon: '💰' },
    { id: 'users', name: 'Users', icon: '👥' },
    { id: 'economics', name: 'P2E Economics', icon: '🎮' },
    { id: 'nfts', name: 'NFT Floors', icon: '🖼️' }
  ];

  useEffect(() => {
    fetchAnalyticsData();
  }, [selectedProtocol]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await gamingAnalyticsAPI.getEnhancedProtocolAnalytics(selectedProtocol);
      setAnalyticsData(data);
    } catch (err) {
      console.error('Error fetching enhanced analytics:', err);
      setError('Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  const formatNumber = (num, decimals = 2) => {
    if (num === null || num === undefined) return 'N/A';
    if (num >= 1e9) return `${(num / 1e9).toFixed(decimals)}B`;
    if (num >= 1e6) return `${(num / 1e6).toFixed(decimals)}M`;
    if (num >= 1e3) return `${(num / 1e3).toFixed(decimals)}K`;
    return num.toFixed(decimals);
  };

  const formatCurrency = (amount, currency = 'USD') => {
    if (amount === null || amount === undefined) return 'N/A';
    return `$${formatNumber(amount)}`;
  };

  const formatPercentage = (value) => {
    if (value === null || value === undefined) return 'N/A';
    const sign = value >= 0 ? '+' : '';
    return `${sign}${value.toFixed(2)}%`;
  };

  const getHealthColor = (score) => {
    if (score >= 80) return '#00ff88';
    if (score >= 60) return '#ffaa00';
    if (score >= 40) return '#ff6600';
    return '#ff0044';
  };

  const renderOverviewTab = () => {
    if (!analyticsData?.summary_metrics) return <div>No overview data available</div>;

    const summary = analyticsData.summary_metrics;
    
    return (
      <div className="analytics-overview">
        <div className="metrics-grid">
          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">🏥</span>
              <h3>Overall Health</h3>
            </div>
            <div className="metric-value" style={{ color: getHealthColor(summary.overall_health_score) }}>
              {summary.overall_health_score?.toFixed(1) || 'N/A'}
            </div>
            <div className="metric-label">Health Score</div>
          </div>

          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">💚</span>
              <h3>Economic Sustainability</h3>
            </div>
            <div className="metric-value">
              {summary.economic_sustainability || 'Unknown'}
            </div>
            <div className="metric-label">Sustainability Rating</div>
          </div>

          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">👥</span>
              <h3>User Engagement</h3>
            </div>
            <div className="metric-value">
              {summary.user_engagement || 'Unknown'}
            </div>
            <div className="metric-label">Engagement Level</div>
          </div>

          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">📈</span>
              <h3>Market Performance</h3>
            </div>
            <div className="metric-value">
              {summary.market_performance || 'Unknown'}
            </div>
            <div className="metric-label">Market Trend</div>
          </div>

          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">🖼️</span>
              <h3>NFT Market</h3>
            </div>
            <div className="metric-value">
              {summary.nft_market_health || 'Unknown'}
            </div>
            <div className="metric-label">NFT Health</div>
          </div>
        </div>
      </div>
    );
  };

  const renderTVLTab = () => {
    if (!analyticsData?.tvl_metrics) return <div>No TVL data available</div>;

    const tvl = analyticsData.tvl_metrics;
    
    return (
      <div className="analytics-tvl">
        <div className="metrics-grid">
          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">💰</span>
              <h3>Total Value Locked</h3>
            </div>
            <div className="metric-value">
              {formatCurrency(tvl.tvl_usd)}
            </div>
            <div className="metric-change" style={{ color: tvl.tvl_change_24h >= 0 ? '#00ff88' : '#ff0044' }}>
              {formatPercentage(tvl.tvl_change_24h)} (24h)
            </div>
          </div>

          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">📊</span>
              <h3>Confidence Score</h3>
            </div>
            <div className="metric-value">
              {(tvl.confidence_score * 100).toFixed(1)}%
            </div>
            <div className="metric-label">Data Reliability</div>
          </div>

          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">🔗</span>
              <h3>Chain</h3>
            </div>
            <div className="metric-value">
              {tvl.chain}
            </div>
            <div className="metric-label">Blockchain</div>
          </div>
        </div>

        {tvl.token_breakdown && (
          <div className="token-breakdown">
            <h4>Token Breakdown</h4>
            <div className="token-list">
              {Object.entries(tvl.token_breakdown).map(([token, amount]) => (
                <div key={token} className="token-item">
                  <span className="token-symbol">{token}</span>
                  <span className="token-amount">{formatCurrency(amount)}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderUsersTab = () => {
    if (!analyticsData?.user_activity_metrics) return <div>No user activity data available</div>;

    const users = analyticsData.user_activity_metrics;
    
    return (
      <div className="analytics-users">
        <div className="metrics-grid">
          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">👥</span>
              <h3>Daily Active Users</h3>
            </div>
            <div className="metric-value">
              {formatNumber(users.daily_active_users, 0)}
            </div>
            <div className="metric-label">DAU</div>
          </div>

          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">📅</span>
              <h3>Weekly Active Users</h3>
            </div>
            <div className="metric-value">
              {formatNumber(users.weekly_active_users, 0)}
            </div>
            <div className="metric-label">WAU</div>
          </div>

          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">🔄</span>
              <h3>User Retention</h3>
            </div>
            <div className="metric-value">
              {formatPercentage(users.user_retention_24h)}
            </div>
            <div className="metric-label">24h Retention</div>
          </div>

          <div className="metric-card">
            <div className="metric-header">
              <span className="metric-icon">💸</span>
              <h3>Avg Transactions</h3>
            </div>
            <div className="metric-value">
              {users.avg_transactions_per_user?.toFixed(1) || 'N/A'}
            </div>
            <div className="metric-label">Per User</div>
          </div>
        </div>

        {users.user_segmentation && (
          <div className="user-segmentation">
            <h4>User Segmentation</h4>
            <div className="segment-list">
              {Object.entries(users.user_segmentation).map(([segment, count]) => (
                <div key={segment} className="segment-item">
                  <span className="segment-name">{segment.replace('_', ' ')}</span>
                  <span className="segment-count">{formatNumber(count, 0)}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderEconomicsTab = () => {
    const tokenEconomics = analyticsData?.token_economics;
    const earningMechanics = analyticsData?.earning_mechanics;

    if (!tokenEconomics && !earningMechanics) {
      return <div>No P2E economics data available</div>;
    }

    return (
      <div className="analytics-economics">
        {earningMechanics && (
          <div className="earning-mechanics">
            <h4>P2E Economics</h4>
            <div className="metrics-grid">
              <div className="metric-card">
                <div className="metric-header">
                  <span className="metric-icon">💰</span>
                  <h3>Avg Daily Earnings</h3>
                </div>
                <div className="metric-value">
                  {formatCurrency(earningMechanics.current_economics?.avg_player_earnings_daily_usd ||
                                 earningMechanics.earning_potential?.avg_daily_usd || 0)}
                </div>
                <div className="metric-label">Per Player</div>
              </div>

              <div className="metric-card">
                <div className="metric-header">
                  <span className="metric-icon">📊</span>
                  <h3>Sustainability Score</h3>
                </div>
                <div className="metric-value">
                  {(earningMechanics.sustainability_metrics?.overall_score || 0).toFixed(1)}%
                </div>
                <div className="metric-label">Health Score</div>
              </div>

              <div className="metric-card">
                <div className="metric-header">
                  <span className="metric-icon">🏆</span>
                  <h3>Top Earner</h3>
                </div>
                <div className="metric-value">
                  {formatCurrency(earningMechanics.top_earner_daily_usd)}
                </div>
                <div className="metric-label">Daily</div>
              </div>

              <div className="metric-card">
                <div className="metric-header">
                  <span className="metric-icon">🌱</span>
                  <h3>Sustainability</h3>
                </div>
                <div className="metric-value" style={{ color: getHealthColor(earningMechanics.sustainability_score) }}>
                  {earningMechanics.sustainability_score?.toFixed(1) || 'N/A'}
                </div>
                <div className="metric-label">Score</div>
              </div>
            </div>
          </div>
        )}

        {tokenEconomics && Object.keys(tokenEconomics).length > 0 && (
          <div className="token-economics">
            <h4>Token Economics</h4>
            <div className="token-grid">
              {Object.entries(tokenEconomics).map(([token, data]) => (
                <div key={token} className="token-card">
                  <h5>{data.token_symbol}</h5>
                  <div className="token-metrics">
                    <div className="token-metric">
                      <span className="label">Price:</span>
                      <span className="value">{formatCurrency(data.current_price_usd)}</span>
                    </div>
                    <div className="token-metric">
                      <span className="label">24h Change:</span>
                      <span className="value" style={{ color: data.price_change_24h >= 0 ? '#00ff88' : '#ff0044' }}>
                        {formatPercentage(data.price_change_24h)}
                      </span>
                    </div>
                    <div className="token-metric">
                      <span className="label">Market Cap:</span>
                      <span className="value">{formatCurrency(data.market_cap_usd)}</span>
                    </div>
                    <div className="token-metric">
                      <span className="label">Staking Ratio:</span>
                      <span className="value">{formatPercentage(data.staking_ratio)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  const renderNFTsTab = () => {
    const nftCollections = analyticsData?.nft_collections;

    if (!nftCollections || Object.keys(nftCollections).length === 0) {
      return <div>No NFT floor data available</div>;
    }

    return (
      <div className="analytics-nfts">
        <h4>NFT Collections</h4>
        <div className="nft-grid">
          {Object.entries(nftCollections).map(([collection, data]) => (
            <div key={collection} className="nft-card">
              <h5>{data.collection_name}</h5>
              <div className="nft-metrics">
                <div className="nft-metric">
                  <span className="label">Floor Price:</span>
                  <span className="value">{data.floor_price_eth?.toFixed(4) || 'N/A'} ETH</span>
                </div>
                <div className="nft-metric">
                  <span className="label">USD Value:</span>
                  <span className="value">{formatCurrency(data.floor_price_usd)}</span>
                </div>
                <div className="nft-metric">
                  <span className="label">24h Change:</span>
                  <span className="value" style={{ color: data.floor_change_24h >= 0 ? '#00ff88' : '#ff0044' }}>
                    {formatPercentage(data.floor_change_24h)}
                  </span>
                </div>
                <div className="nft-metric">
                  <span className="label">Volume 24h:</span>
                  <span className="value">{data.volume_24h_eth?.toFixed(2) || 'N/A'} ETH</span>
                </div>
                <div className="nft-metric">
                  <span className="label">Sales 24h:</span>
                  <span className="value">{data.sales_count_24h || 'N/A'}</span>
                </div>
                <div className="nft-metric">
                  <span className="label">Total Supply:</span>
                  <span className="value">{formatNumber(data.total_supply, 0)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return renderOverviewTab();
      case 'tvl':
        return renderTVLTab();
      case 'users':
        return renderUsersTab();
      case 'economics':
        return renderEconomicsTab();
      case 'nfts':
        return renderNFTsTab();
      default:
        return <div>Tab not found</div>;
    }
  };

  if (loading) {
    return (
      <div className="enhanced-analytics-dashboard">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading enhanced analytics...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="enhanced-analytics-dashboard">
        <div className="error-message">
          <p>{error}</p>
          <button onClick={fetchAnalyticsData} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="enhanced-analytics-dashboard">
      <div className="dashboard-header">
        <h2>Enhanced Gaming Analytics</h2>
        <div className="protocol-selector">
          <select 
            value={selectedProtocol} 
            onChange={(e) => setSelectedProtocol(e.target.value)}
            className="protocol-select"
          >
            {protocols.map(protocol => (
              <option key={protocol.id} value={protocol.id}>
                {protocol.name}
              </option>
            ))}
          </select>
          <button onClick={fetchAnalyticsData} className="refresh-button">
            🔄 Refresh
          </button>
        </div>
      </div>

      <div className="analytics-tabs">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            <span className="tab-icon">{tab.icon}</span>
            <span className="tab-name">{tab.name}</span>
          </button>
        ))}
      </div>

      <div className="analytics-content">
        {renderTabContent()}
      </div>

      {analyticsData?.timestamp && (
        <div className="analytics-footer">
          <p>Last updated: {new Date(analyticsData.timestamp).toLocaleString()}</p>
        </div>
      )}
    </div>
  );
};

export default EnhancedAnalyticsDashboard;
