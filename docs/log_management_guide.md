# Log Management System Guide

## Overview

The Web3 Gaming News Tracker includes a comprehensive log management system that provides automated log rotation, archival, monitoring, alerting, and analysis capabilities. This system ensures optimal log storage, performance monitoring, and proactive issue detection.

## System Components

### 1. Log Manager (`scripts/log_manager.py`)
**Purpose**: Core log management operations including rotation, archival, and cleanup.

**Key Features**:
- Automated log rotation based on file size
- Compressed archival of old log files
- Intelligent cleanup with retention policies
- Health monitoring and disk space management
- Comprehensive reporting and analysis

**Usage**:
```bash
# Check log status
python scripts/log_manager.py status

# Rotate large log files
python scripts/log_manager.py rotate --max-size 100

# Archive old logs
python scripts/log_manager.py archive --days 7

# Generate comprehensive report
python scripts/log_manager.py report

# Run full maintenance cycle
python scripts/log_manager.py maintenance
```

### 2. Log Monitor (`scripts/log_monitor.py`)
**Purpose**: Real-time log monitoring with pattern detection and alerting.

**Key Features**:
- Real-time log tailing and analysis
- Configurable alert patterns and thresholds
- Performance metrics tracking
- Multiple notification channels (email, webhook, Slack)
- Error rate and response time monitoring

**Usage**:
```bash
# Start real-time monitoring
python scripts/log_monitor.py --log-dir logs

# Monitor with email notifications
python scripts/log_monitor.py --email-config email_config.json

# Monitor with webhook notifications
python scripts/log_monitor.py --webhook-url https://hooks.slack.com/...
```

### 3. Log Aggregator (`scripts/log_aggregator.py`)
**Purpose**: Log collection, analysis, and export capabilities.

**Key Features**:
- Multi-source log collection and parsing
- SQLite-based log storage for fast queries
- Error pattern analysis and classification
- Performance trend analysis
- Export to CSV, JSON, and Elasticsearch

**Usage**:
```bash
# Collect logs from all sources
python scripts/log_aggregator.py collect

# Query logs with filters
python scripts/log_aggregator.py query --level ERROR --service api

# Analyze error patterns
python scripts/log_aggregator.py analyze --hours 24

# Generate performance report
python scripts/log_aggregator.py performance --hours 24

# Export logs to CSV
python scripts/log_aggregator.py export --format csv --output logs.csv
```

### 4. Log Service (`scripts/log_service.py`)
**Purpose**: Automated log management service with scheduling and monitoring.

**Key Features**:
- Automated scheduled tasks (rotation, archival, cleanup)
- Continuous monitoring and alerting
- Emergency procedures for critical situations
- Configuration-driven operation
- Integration with external monitoring systems

**Usage**:
```bash
# Run complete log management service
python scripts/log_service.py --config config/log_management.yaml

# Run only monitoring
python scripts/log_service.py --mode monitor

# Run only scheduled tasks
python scripts/log_service.py --mode scheduler
```

## Configuration

### Log Management Configuration (`config/log_management.yaml`)

The system uses a comprehensive YAML configuration file that controls all aspects of log management:

#### Key Configuration Sections:

**Retention Policies**:
```yaml
retention_policies:
  debug: 7        # Debug logs kept for 1 week
  info: 30        # Info logs kept for 1 month  
  warning: 90     # Warning logs kept for 3 months
  error: 365      # Error logs kept for 1 year
  critical: 730   # Critical logs kept for 2 years
```

**Log Rotation**:
```yaml
rotation:
  max_file_size_mb: 100      # Rotate when file exceeds 100MB
  max_backup_count: 10       # Keep 10 backup files
  compress_rotated: true     # Compress rotated files
  rotation_schedule: "daily" # daily, weekly, monthly
```

**Monitoring Alerts**:
```yaml
alerts:
  - name: "Critical Errors"
    pattern: '"level":\s*"CRITICAL"'
    threshold: 1
    time_window_seconds: 60
    severity: "critical"
```

**Automation Schedules**:
```yaml
automation:
  schedules:
    log_rotation:
      enabled: true
      cron: "0 2 * * *"  # Daily at 2 AM
    log_archival:
      enabled: true
      cron: "0 3 * * 0"  # Weekly on Sunday at 3 AM
```

## Log Structure and Formats

### JSON Log Format
The system primarily uses structured JSON logging:

```json
{
  "timestamp": "2025-01-07T22:30:00.000Z",
  "level": "INFO",
  "service": "web3-gaming-tracker",
  "module": "api",
  "function": "get_articles",
  "line": 45,
  "message": "API request completed",
  "request_method": "GET",
  "endpoint": "/api/articles",
  "status_code": 200,
  "response_time_ms": 150.5,
  "user_id": "user123",
  "request_id": "req_456"
}
```

### Service-Specific Loggers
Different services use specialized loggers with contextual information:

- **API Logger**: Request/response tracking, performance metrics
- **Blockchain Logger**: RPC calls, contract interactions, transaction monitoring
- **Scraper Logger**: Source monitoring, content extraction, error tracking
- **Content Intelligence Logger**: Analysis results, confidence scores
- **Market Analytics Logger**: Data processing, trend analysis
- **Database Logger**: Query performance, connection health

## Monitoring and Alerting

### Default Alert Configurations

1. **Critical Errors**: Immediate alert on any CRITICAL level log
2. **High Error Rate**: Alert when >10 errors in 5 minutes
3. **Database Issues**: Alert on database connection failures
4. **Blockchain RPC Failures**: Alert on blockchain connectivity issues
5. **API Performance**: Alert on slow response times (>5 seconds)
6. **Memory Issues**: Alert on memory-related errors
7. **Authentication Failures**: Alert on authentication issues

### Performance Monitoring

The system tracks key performance metrics:

- **Response Times**: API endpoint performance tracking
- **Error Rates**: Service-specific error rate monitoring
- **Resource Usage**: Disk space, memory usage monitoring
- **Throughput**: Request volume and processing rates

### Health Checks

Automated health checks monitor:

- **Disk Space**: Available storage for log files
- **Log File Sizes**: Detection of unusually large log files
- **Error Patterns**: Trending analysis of error types
- **System Performance**: Overall system health indicators

## Emergency Procedures

### Automatic Emergency Response

When critical conditions are detected, the system can automatically:

1. **Compress Old Logs**: Reduce disk usage by compressing rotated logs
2. **Archive Rotated Logs**: Move old logs to archive storage
3. **Cleanup Temp Files**: Remove temporary files to free space
4. **Remove Debug Logs**: Delete debug-level logs to save space

### Manual Emergency Commands

```bash
# Emergency log cleanup
python scripts/log_manager.py maintenance --rotate-size 50 --archive-days 1

# Force compression of all rotated logs
find logs -name "*.log.*" ! -name "*.gz" -exec gzip {} \;

# Emergency disk space check
python scripts/log_manager.py health
```

## Integration with External Systems

### Prometheus Metrics
Export log metrics to Prometheus for monitoring:

```yaml
integrations:
  prometheus:
    enabled: true
    metrics_port: 9090
    metrics_path: "/metrics"
```

### Elasticsearch Export
Send logs to Elasticsearch for advanced analysis:

```yaml
export:
  elasticsearch:
    enabled: true
    host: "localhost"
    port: 9200
    index_prefix: "web3-gaming-logs"
```

### Notification Channels

**Email Notifications**:
```yaml
notifications:
  email:
    enabled: true
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    to_emails: ["<EMAIL>"]
```

**Slack Integration**:
```yaml
notifications:
  slack:
    enabled: true
    webhook_url: "https://hooks.slack.com/..."
    channel: "#alerts"
```

## Best Practices

### Log Level Guidelines

- **DEBUG**: Detailed diagnostic information for development
- **INFO**: General operational information
- **WARNING**: Potentially harmful situations that don't stop operation
- **ERROR**: Error events that don't stop the application
- **CRITICAL**: Very serious error events that may cause termination

### Performance Considerations

1. **Log Rotation**: Keep active log files under 100MB for optimal performance
2. **Retention**: Balance storage costs with compliance requirements
3. **Compression**: Use compression for archived logs to save space
4. **Indexing**: Use structured logging for better searchability

### Security Considerations

1. **Sensitive Data**: Never log passwords, API keys, or personal information
2. **File Permissions**: Restrict log file access to authorized users only
3. **Audit Trail**: Maintain audit logs for security-related events
4. **Encryption**: Consider encrypting archived logs for sensitive data

## Troubleshooting

### Common Issues

**High Disk Usage**:
```bash
# Check log directory size
du -sh logs/

# Run emergency cleanup
python scripts/log_manager.py maintenance --archive-days 1
```

**Missing Log Files**:
```bash
# Check log configuration
python scripts/log_manager.py status

# Verify log directory permissions
ls -la logs/
```

**Alert Fatigue**:
- Adjust alert thresholds in configuration
- Implement alert cooldown periods
- Use alert severity levels appropriately

**Performance Issues**:
- Monitor log file sizes regularly
- Implement proper log rotation
- Use asynchronous logging for high-volume services

### Log Analysis Commands

```bash
# Find error patterns
grep -E "(ERROR|CRITICAL)" logs/web3_gaming_tracker.log | tail -20

# Analyze response times
jq '.response_time_ms' logs/web3_gaming_tracker.log | sort -n | tail -10

# Count log levels
jq -r '.level' logs/web3_gaming_tracker.log | sort | uniq -c

# Find slow API calls
jq 'select(.response_time_ms > 2000)' logs/web3_gaming_tracker.log
```

## Maintenance Schedule

### Daily Tasks (Automated)
- Log rotation for files >100MB
- Health checks every 15 minutes
- Metrics collection every 5 minutes

### Weekly Tasks (Automated)
- Archive logs older than 7 days
- Compress rotated log files
- Generate weekly analysis reports

### Monthly Tasks (Automated)
- Cleanup archived logs based on retention policy
- Generate comprehensive system reports
- Review and optimize alert configurations

### Quarterly Tasks (Manual)
- Review log retention policies
- Update alert thresholds based on trends
- Audit log management configuration
- Test emergency procedures

This comprehensive log management system ensures reliable operation, proactive monitoring, and efficient storage management for the Web3 Gaming News Tracker application.
