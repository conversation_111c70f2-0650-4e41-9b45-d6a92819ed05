components:
  schemas:
    Error:
      properties:
        category:
          type: string
        error:
          type: boolean
        error_id:
          type: string
        message:
          type: string
        severity:
          type: string
        timestamp:
          type: string
      type: object
info:
  contact:
    email: <EMAIL>
    name: Web3 Gaming Tracker API Support
  description: "\nComprehensive API for accessing web3 gaming news, blockchain data,\
    \ content intelligence, and market analytics.\n\n## Features\n\n* **Gaming News**:\
    \ Access to curated gaming articles from multiple sources\n* **Blockchain Integration**:\
    \ Real-time blockchain data from multiple networks\n* **Content Intelligence**:\
    \ Advanced NLP analysis and sentiment scoring\n* **Market Analytics**: Sector\
    \ analysis and investment tracking\n* **Competitive Analysis**: Project comparison\
    \ and market positioning\n\n## Phase 7 Enhancements\n\n* **Advanced NLP Classification**:\
    \ 11-category gaming content classification\n* **Multi-dimensional Sentiment Analysis**:\
    \ Community, market, and technical sentiment\n* **Trend Detection**: Emerging\
    \ and declining trend identification\n* **Market Intelligence**: Investment signals\
    \ and risk assessment\n* **Entity Recognition**: Gaming project and token extraction\n\
    * **Competitive Metrics**: 8-metric competitive analysis framework\n\n## Authentication\n\
    \nCurrently in development mode. Production will include API key authentication\
    \ and rate limiting.\n\n## Rate Limits\n\n* General API: 1000 requests/hour\n\
    * Content Intelligence: 100 requests/hour  \n* Market Analytics: 200 requests/hour\n\
    * Blockchain Data: 500 requests/hour\n            "
  license:
    name: MIT License
    url: https://opensource.org/licenses/MIT
  title: Web3 Gaming News Tracker API
  version: 1.0.0
openapi: 3.0.2
paths:
  /api/v1/content-intelligence/classify:
    post:
      description: Classify gaming content using advanced NLP analysis
      requestBody:
        content:
          application/json:
            schema:
              properties:
                content:
                  type: string
                source:
                  type: string
                summary:
                  type: string
                title:
                  type: string
              required:
              - title
              type: object
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                properties:
                  blockchain_networks:
                    items:
                      type: string
                    type: array
                  category_confidence:
                    type: number
                  gaming_entities:
                    items:
                      type: string
                    type: array
                  primary_category:
                    type: string
                  sentiment_score:
                    type: number
                type: object
          description: Content classification result
      summary: Classify Gaming Content
      tags:
      - Content Intelligence
  /api/v1/content-intelligence/competitive/landscape:
    get:
      description: Analyze competitive landscape of gaming projects
      parameters:
      - description: Filter by game category
        in: query
        name: category
        schema:
          type: string
      responses:
        '200':
          description: Competitive landscape analysis
      summary: Competitive Landscape
      tags:
      - Competitive Analysis
  /api/v1/content-intelligence/market/sector-analysis:
    get:
      description: Analyze gaming sector performance across protocols
      parameters:
      - description: Analysis timeframe
        in: query
        name: timeframe
        schema:
          enum:
          - 24h
          - 7d
          - 30d
          type: string
      responses:
        '200':
          description: Sector analysis results
      summary: Sector Analysis
      tags:
      - Market Analytics
  /api/v1/content-intelligence/sentiment:
    post:
      description: Analyze gaming community sentiment with enhanced scoring
      requestBody:
        content:
          application/json:
            schema:
              properties:
                content:
                  type: string
                summary:
                  type: string
                title:
                  type: string
              required:
              - title
              type: object
        required: true
      responses:
        '200':
          description: Sentiment analysis result
      summary: Analyze Sentiment
      tags:
      - Content Intelligence
  /health:
    get:
      description: Check system health and status
      responses:
        '200':
          content:
            application/json:
              schema:
                properties:
                  status:
                    type: string
                  timestamp:
                    type: string
                  version:
                    type: string
                type: object
          description: System is healthy
      summary: Health Check
      tags:
      - System
servers:
- description: Development server
  url: http://localhost:8000
- description: Production server
  url: https://api.web3gaming-tracker.com
tags:
- description: System health and status endpoints
  name: System
- description: 'Phase 7: Advanced NLP and sentiment analysis'
  name: Content Intelligence
- description: 'Phase 7: Market intelligence and sector analysis'
  name: Market Analytics
- description: 'Phase 7: Project comparison and competitive metrics'
  name: Competitive Analysis
