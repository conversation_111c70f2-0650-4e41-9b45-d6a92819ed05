# Web3 Gaming News Tracker - Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying the Web3 Gaming News Tracker application with all Phase 7 Content Intelligence and Market Analytics features.

## Prerequisites

### System Requirements
- **Python**: 3.8+ with pip
- **Node.js**: 16+ with npm
- **Docker**: Latest version with docker-compose
- **Git**: For version control
- **Operating System**: macOS, Linux, or Windows with WSL2

### Required Services
- **PostgreSQL**: Database (via Docker)
- **Redis**: Caching and session storage (via Docker)
- **Internet Connection**: For blockchain RPC endpoints and API services

## Quick Start Deployment

### 1. Clone and Setup
```bash
git clone <repository-url>
cd webThreeGameScraper

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

**Required Environment Variables**:
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/gaming_tracker

# Redis
REDIS_URL=redis://localhost:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false

# Security
SECRET_KEY=your-secret-key-here
API_KEY_HEADER=X-API-Key
ENABLE_API_KEY_AUTH=true

# Blockchain RPC Endpoints
ETHEREUM_RPC_URL=https://eth.llamarpc.com
POLYGON_RPC_URL=https://polygon.llamarpc.com
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# External API Keys
ETHERSCAN_API_KEY=your-etherscan-key
SOLSCAN_API_KEY=your-solscan-key
COINGECKO_API_KEY=your-coingecko-key
```

### 3. Automated Deployment
```bash
# Run automated deployment script
./deployment/deploy.sh

# Or for production environment
./deployment/deploy.sh production
```

### 4. Validation
```bash
# Run deployment validation
python deployment/validate_deployment.py

# Run comprehensive tests
python -m pytest tests/test_deployment.py -v
```

## Manual Deployment Steps

### 1. Database Setup
```bash
# Start PostgreSQL and Redis containers
docker-compose up -d postgres_gaming redis_gaming

# Wait for services to be ready
sleep 10

# Run database migrations
python -m alembic upgrade head
```

### 2. Backend Deployment
```bash
# Start FastAPI application
uvicorn api.main:app --host 0.0.0.0 --port 8000 &

# Verify backend is running
curl http://localhost:8000/health
```

### 3. Frontend Deployment
```bash
# Navigate to frontend directory
cd dashboard/frontend

# Install dependencies
npm install

# Build production bundle
npm run build

# Start development server (or serve build)
npm start &

# Verify frontend is running
curl http://localhost:3000
```

## Service Management

### Starting Services
```bash
# Start all services
./deployment/deploy.sh

# Start individual services
docker-compose up -d postgres_gaming redis_gaming
uvicorn api.main:app --host 0.0.0.0 --port 8000 &
cd dashboard/frontend && npm start &
```

### Stopping Services
```bash
# Stop all services
./deployment/stop.sh

# Stop individual services
pkill -f "uvicorn api.main:app"
pkill -f "npm start"
docker-compose down
```

### Service Status
```bash
# Check service status
curl http://localhost:8000/health
curl http://localhost:3000

# Check process status
ps aux | grep uvicorn
ps aux | grep npm
docker-compose ps
```

## Configuration

### Environment-Specific Settings

#### Development
```bash
API_DEBUG=true
LOG_LEVEL=DEBUG
ENABLE_CORS=true
CORS_ORIGINS=["http://localhost:3000"]
```

#### Production
```bash
API_DEBUG=false
LOG_LEVEL=INFO
ENABLE_CORS=true
CORS_ORIGINS=["https://yourdomain.com"]
SSL_ENABLED=true
```

### Security Configuration
```bash
# API Security
ENABLE_API_KEY_AUTH=true
RATE_LIMIT_ENABLED=true
MAX_REQUESTS_PER_HOUR=1000

# Security Headers
ENABLE_SECURITY_HEADERS=true
ENABLE_CSRF_PROTECTION=true

# SSL/TLS (Production)
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
```

## Monitoring and Health Checks

### Health Endpoints
- **Backend Health**: `http://localhost:8000/health`
- **API Documentation**: `http://localhost:8000/docs`
- **Metrics**: `http://localhost:8000/metrics`
- **Frontend**: `http://localhost:3000`

### Monitoring Setup
```bash
# Prometheus metrics available at
curl http://localhost:8000/metrics

# Health check with detailed status
curl http://localhost:8000/health | jq
```

### Log Management
```bash
# Application logs
tail -f logs/app.log

# Error logs
tail -f logs/error.log

# Access logs
tail -f logs/access.log
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Errors
```bash
# Check PostgreSQL container
docker-compose ps postgres_gaming

# Check connection
docker-compose exec postgres_gaming psql -U postgres -d gaming_tracker -c "SELECT 1;"

# Restart database
docker-compose restart postgres_gaming
```

#### 2. Redis Connection Errors
```bash
# Check Redis container
docker-compose ps redis_gaming

# Test Redis connection
docker-compose exec redis_gaming redis-cli ping

# Restart Redis
docker-compose restart redis_gaming
```

#### 3. API Startup Failures
```bash
# Check Python dependencies
pip check

# Check environment variables
python -c "from config.settings import get_settings; print(get_settings())"

# Check port availability
lsof -i :8000
```

#### 4. Frontend Build Errors
```bash
# Clear npm cache
npm cache clean --force

# Remove node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# Check Node.js version
node --version
npm --version
```

#### 5. Blockchain RPC Issues
```bash
# Test RPC endpoints
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  https://eth.llamarpc.com

# Check API keys
echo $ETHERSCAN_API_KEY
echo $SOLSCAN_API_KEY
```

### Performance Issues

#### High Memory Usage
```bash
# Monitor memory usage
htop
docker stats

# Optimize database connections
# Reduce connection pool size in settings
```

#### Slow API Responses
```bash
# Check database performance
docker-compose exec postgres_gaming psql -U postgres -d gaming_tracker \
  -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Monitor API response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/health
```

## Backup and Recovery

### Database Backup
```bash
# Create backup
docker-compose exec postgres_gaming pg_dump -U postgres gaming_tracker > backup.sql

# Restore backup
docker-compose exec -T postgres_gaming psql -U postgres gaming_tracker < backup.sql
```

### Configuration Backup
```bash
# Backup configuration
cp .env .env.backup
cp -r deployment/ deployment_backup/
```

## Scaling and Production Considerations

### Load Balancing
```bash
# Run multiple backend instances
uvicorn api.main:app --host 0.0.0.0 --port 8000 &
uvicorn api.main:app --host 0.0.0.0 --port 8001 &
uvicorn api.main:app --host 0.0.0.0 --port 8002 &

# Use nginx for load balancing
```

### Database Optimization
```bash
# Create indexes for better performance
python -c "
from models.base import engine
from sqlalchemy import text
with engine.connect() as conn:
    conn.execute(text('CREATE INDEX IF NOT EXISTS idx_articles_created_at ON articles(created_at);'))
    conn.execute(text('CREATE INDEX IF NOT EXISTS idx_blockchain_data_timestamp ON blockchain_data(timestamp);'))
"
```

### Caching Strategy
```bash
# Redis configuration for production
REDIS_MAX_CONNECTIONS=100
REDIS_CONNECTION_POOL_SIZE=50
CACHE_TTL=3600
```

## Security Hardening

### Production Security Checklist
- [ ] Change default passwords
- [ ] Enable SSL/TLS certificates
- [ ] Configure firewall rules
- [ ] Enable API key authentication
- [ ] Set up rate limiting
- [ ] Configure security headers
- [ ] Enable audit logging
- [ ] Regular security updates

### SSL/TLS Setup
```bash
# Generate SSL certificates (Let's Encrypt)
certbot certonly --standalone -d yourdomain.com

# Configure nginx with SSL
# Update nginx.conf with SSL settings
```

## Maintenance

### Regular Maintenance Tasks
```bash
# Update dependencies
pip install --upgrade -r requirements.txt
npm update

# Clean up logs
find logs/ -name "*.log" -mtime +30 -delete

# Database maintenance
docker-compose exec postgres_gaming psql -U postgres -d gaming_tracker -c "VACUUM ANALYZE;"

# Clear Redis cache
docker-compose exec redis_gaming redis-cli FLUSHDB
```

### Health Monitoring
```bash
# Set up cron job for health checks
echo "*/5 * * * * curl -f http://localhost:8000/health || echo 'Health check failed'" | crontab -
```

## Support and Documentation

### Additional Resources
- **API Documentation**: `http://localhost:8000/docs`
- **Phase 7 Integration Guide**: `docs/PHASE7_DASHBOARD_INTEGRATION.md`
- **Development Notes**: `docs/development_notes.md`
- **Troubleshooting**: `docs/troubleshooting.md`

### Getting Help
1. Check logs in `logs/` directory
2. Review health check endpoints
3. Run deployment validation script
4. Check GitHub issues and documentation

## Deployment Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Dependencies installed
- [ ] Database migrations ready
- [ ] SSL certificates (production)
- [ ] Firewall rules configured

### Deployment
- [ ] Services started successfully
- [ ] Health checks passing
- [ ] API endpoints responding
- [ ] Frontend accessible
- [ ] Database connectivity verified

### Post-Deployment
- [ ] Monitoring configured
- [ ] Backup procedures tested
- [ ] Performance benchmarks met
- [ ] Security validation passed
- [ ] User acceptance testing completed

**Deployment Complete!** 🎉

Your Web3 Gaming News Tracker is now deployed and ready for use.
