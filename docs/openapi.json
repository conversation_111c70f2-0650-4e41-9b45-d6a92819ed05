{"openapi": "3.0.2", "info": {"title": "Web3 Gaming News Tracker API", "description": "\nComprehensive API for accessing web3 gaming news, blockchain data, content intelligence, and market analytics.\n\n## Features\n\n* **Gaming News**: Access to curated gaming articles from multiple sources\n* **Blockchain Integration**: Real-time blockchain data from multiple networks\n* **Content Intelligence**: Advanced NLP analysis and sentiment scoring\n* **Market Analytics**: Sector analysis and investment tracking\n* **Competitive Analysis**: Project comparison and market positioning\n\n## Phase 7 Enhancements\n\n* **Advanced NLP Classification**: 11-category gaming content classification\n* **Multi-dimensional Sentiment Analysis**: Community, market, and technical sentiment\n* **Trend Detection**: Emerging and declining trend identification\n* **Market Intelligence**: Investment signals and risk assessment\n* **Entity Recognition**: Gaming project and token extraction\n* **Competitive Metrics**: 8-metric competitive analysis framework\n\n## Authentication\n\nCurrently in development mode. Production will include API key authentication and rate limiting.\n\n## Rate Limits\n\n* General API: 1000 requests/hour\n* Content Intelligence: 100 requests/hour  \n* Market Analytics: 200 requests/hour\n* Blockchain Data: 500 requests/hour\n            ", "version": "1.0.0", "contact": {"name": "Web3 Gaming Tracker API Support", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}}, "servers": [{"url": "http://localhost:8000", "description": "Development server"}, {"url": "https://api.web3gaming-tracker.com", "description": "Production server"}], "paths": {"/health": {"get": {"tags": ["System"], "summary": "Health Check", "description": "Check system health and status", "responses": {"200": {"description": "System is healthy", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string"}, "timestamp": {"type": "string"}, "version": {"type": "string"}}}}}}}}}, "/api/v1/content-intelligence/classify": {"post": {"tags": ["Content Intelligence"], "summary": "Classify Gaming Content", "description": "Classify gaming content using advanced NLP analysis", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "content": {"type": "string"}, "summary": {"type": "string"}, "source": {"type": "string"}}, "required": ["title"]}}}}, "responses": {"200": {"description": "Content classification result", "content": {"application/json": {"schema": {"type": "object", "properties": {"primary_category": {"type": "string"}, "category_confidence": {"type": "number"}, "sentiment_score": {"type": "number"}, "gaming_entities": {"type": "array", "items": {"type": "string"}}, "blockchain_networks": {"type": "array", "items": {"type": "string"}}}}}}}}}}, "/api/v1/content-intelligence/sentiment": {"post": {"tags": ["Content Intelligence"], "summary": "Analyze Sentiment", "description": "Analyze gaming community sentiment with enhanced scoring", "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"title": {"type": "string"}, "content": {"type": "string"}, "summary": {"type": "string"}}, "required": ["title"]}}}}, "responses": {"200": {"description": "Sentiment analysis result"}}}}, "/api/v1/content-intelligence/market/sector-analysis": {"get": {"tags": ["Market Analytics"], "summary": "Sector Analysis", "description": "Analyze gaming sector performance across protocols", "parameters": [{"name": "timeframe", "in": "query", "schema": {"type": "string", "enum": ["24h", "7d", "30d"]}, "description": "Analysis timeframe"}], "responses": {"200": {"description": "Sector analysis results"}}}}, "/api/v1/content-intelligence/competitive/landscape": {"get": {"tags": ["Competitive Analysis"], "summary": "Competitive Landscape", "description": "Analyze competitive landscape of gaming projects", "parameters": [{"name": "category", "in": "query", "schema": {"type": "string"}, "description": "Filter by game category"}], "responses": {"200": {"description": "Competitive landscape analysis"}}}}}, "components": {"schemas": {"Error": {"type": "object", "properties": {"error": {"type": "boolean"}, "error_id": {"type": "string"}, "category": {"type": "string"}, "severity": {"type": "string"}, "message": {"type": "string"}, "timestamp": {"type": "string"}}}}}, "tags": [{"name": "System", "description": "System health and status endpoints"}, {"name": "Content Intelligence", "description": "Phase 7: Advanced NLP and sentiment analysis"}, {"name": "Market Analytics", "description": "Phase 7: Market intelligence and sector analysis"}, {"name": "Competitive Analysis", "description": "Phase 7: Project comparison and competitive metrics"}]}