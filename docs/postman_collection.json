{"info": {"name": "Web3 Gaming News Tracker API", "description": "API collection for testing Web3 Gaming News Tracker endpoints", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8000", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "Content Classification", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"New Gaming Project Launches on Solana\",\n  \"content\": \"A revolutionary new gaming project has launched...\",\n  \"summary\": \"Gaming project launch announcement\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/content-intelligence/classify", "host": ["{{baseUrl}}"], "path": ["api", "v1", "content-intelligence", "classify"]}}}, {"name": "Sentiment Analysis", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Gaming Community Reaction\",\n  \"content\": \"The gaming community is excited about the new features...\"\n}"}, "url": {"raw": "{{baseUrl}}/api/v1/content-intelligence/sentiment", "host": ["{{baseUrl}}"], "path": ["api", "v1", "content-intelligence", "sentiment"]}}}]}