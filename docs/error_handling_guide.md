# Standardized Error Handling Guide

## Overview

The Web3 Gaming News Tracker implements a comprehensive, standardized error handling system that provides consistent error responses, structured logging, and proper error classification across all services.

## Key Components

### 1. Error Classification System

Errors are automatically classified into categories:

- **VALIDATION**: Invalid input data, type errors
- **AUTHENTICATION**: Login failures, token issues
- **AU<PERSON>OR<PERSON>ZATION**: Permission denied, access control
- **NOT_FOUND**: Resource not found errors
- **RATE_LIMIT**: Too many requests, API limits
- **EXTERNAL_API**: Third-party service failures
- **DATABASE**: Database connection, query errors
- **BLOCKCHAIN**: RPC failures, gas errors, network issues
- **NETWORK**: Connection timeouts, network failures
- **CONFIGURATION**: System configuration errors
- **BUSINESS_LOGIC**: Business rule violations
- **SYSTEM**: Critical system errors
- **UNKNOWN**: Unclassified errors

### 2. Error Severity Levels

- **LOW**: Minor issues, user input errors
- **MEDIUM**: Service degradation, external API issues
- **HIGH**: System component failures, blockchain issues
- **CRITICAL**: Database failures, system-wide issues

### 3. Standardized Error Response

All API errors return a consistent structure:

```json
{
  "error": true,
  "error_id": "ERR_20250707_123456_abc12345",
  "category": "validation",
  "severity": "low",
  "message": "The provided data is invalid. Please check your input and try again.",
  "details": {
    "exception_type": "ValueError",
    "original_message": "Invalid email format",
    "endpoint": "/api/v1/users",
    "method": "POST"
  },
  "timestamp": "2025-07-07T12:34:56.789Z",
  "retry_after": null
}
```

## Usage Examples

### 1. Using the Error Handling Decorator

For API endpoints:

```python
from utils.error_handling import handle_api_errors
from utils.logging_config import get_logger

logger = get_logger('api')

@router.post("/example")
@handle_api_errors("/example", "POST")
async def example_endpoint(request: ExampleRequest):
    """Example endpoint with standardized error handling"""
    
    logger.info("Processing example request", user_id=request.user_id)
    
    # Your business logic here
    result = process_request(request)
    
    logger.info("Example request completed", result_count=len(result))
    
    return result
```

### 2. Manual Error Handling

For service functions:

```python
from utils.error_handling import error_handler, ErrorContext

def process_data(data):
    try:
        # Your processing logic
        return processed_data
    except Exception as e:
        context = ErrorContext(
            additional_data={"data_size": len(data)}
        )
        
        # This will log the error and raise an HTTPException
        error_handler.handle_error(
            e, 
            context, 
            custom_user_message="Failed to process data. Please try again."
        )
```

### 3. Blockchain Error Handling

For blockchain operations, use the existing blockchain error handler:

```python
from blockchain.error_handling import with_error_handling

@with_error_handling('ethereum_rpc', 'rpc_call')
async def get_contract_data(contract_address):
    """Get contract data with automatic retry and circuit breaking"""
    # Your blockchain logic here
    return contract_data
```

## Structured Logging

### Service-Specific Loggers

```python
from utils.logging_config import get_logger

# Get logger for specific service
logger = get_logger('content_intelligence')

# Log with structured context
logger.info("Content analysis started", 
            content_type="article",
            analysis_type="sentiment",
            content_count=50)
```

### Specialized Logging Functions

```python
from utils.logging_config import (
    log_api_request,
    log_blockchain_operation,
    log_content_analysis
)

# Log API requests
log_api_request(
    method="POST",
    endpoint="/api/classify",
    status_code=200,
    response_time=0.150,
    request_id="req_123"
)

# Log blockchain operations
log_blockchain_operation(
    operation="get_contract_info",
    blockchain="ethereum",
    success=True,
    duration=2.5,
    contract_address="0x123..."
)

# Log content analysis
log_content_analysis(
    content_type="gaming_article",
    analysis_type="sentiment",
    content_count=25,
    success=True,
    duration=1.2,
    confidence_score=0.85
)
```

## Configuration

### Logging Configuration

The logging system is automatically configured on startup. You can customize it by modifying `utils/logging_config.py`:

```python
# Initialize with custom settings
setup_logging(
    log_level="DEBUG",
    log_format="json",
    log_file="logs/app.log",
    enable_console=True
)
```

### Error Handler Configuration

The error handler can be customized by modifying the error mappings and user messages in `utils/error_handling.py`.

## Best Practices

### 1. Use Appropriate Error Categories

- Always let the system auto-classify errors when possible
- Only override classification for specific business cases
- Use custom user messages for better UX

### 2. Provide Context

- Include relevant context in error handling
- Add request IDs for traceability
- Include user-relevant information in additional_data

### 3. Log Appropriately

- Use structured logging with context
- Log at appropriate levels (info for normal operations, error for failures)
- Include performance metrics (duration, counts)

### 4. Handle Retries Properly

- Use blockchain error handler for blockchain operations
- Implement exponential backoff for external APIs
- Set appropriate timeout values

## Error Monitoring

### Error Tracking

All errors are automatically:
- Assigned unique error IDs
- Logged with full context
- Classified by category and severity
- Tracked with metrics

### Metrics Integration

Error metrics are automatically collected:
- Error rates by endpoint
- Error distribution by category
- Response time percentiles
- Circuit breaker states

### Alerting

Critical and high-severity errors trigger:
- Immediate logging at ERROR/CRITICAL level
- Metrics updates for monitoring
- Structured data for alerting systems

## Migration Guide

### Updating Existing Endpoints

1. Add imports:
```python
from utils.error_handling import handle_api_errors
from utils.logging_config import get_logger
```

2. Add logger:
```python
logger = get_logger('service_name')
```

3. Add decorator:
```python
@handle_api_errors("/endpoint/path", "METHOD")
```

4. Remove try-catch blocks and add structured logging:
```python
# Before
try:
    result = process()
    return result
except Exception as e:
    raise HTTPException(status_code=500, detail=str(e))

# After
logger.info("Starting process")
result = process()
logger.info("Process completed", result_count=len(result))
return result
```

### Updating Service Functions

1. Replace manual error handling with standardized system
2. Add structured logging with context
3. Use appropriate error categories
4. Provide user-friendly error messages

This standardized approach ensures consistent error handling, better debugging capabilities, and improved user experience across the entire application.
