# Web3 Gaming News Tracker API Documentation

## Overview

The Web3 Gaming News Tracker API provides comprehensive access to gaming news, blockchain data, content intelligence, and market analytics for the web3 gaming ecosystem.

**Base URL**: `http://localhost:8000`  
**API Version**: v1  
**Documentation**: `/docs` (Swagger UI) | `/redoc` (ReDoc)

## Authentication

Currently, the API operates without authentication for development. Production deployment will include:
- API key authentication
- Rate limiting per key
- User-based access controls

## Error Handling

All API endpoints use standardized error responses:

```json
{
  "error": true,
  "error_id": "ERR_20250707_123456_abc12345",
  "category": "validation",
  "severity": "low",
  "message": "User-friendly error message",
  "details": {
    "exception_type": "ValueError",
    "original_message": "Technical error details",
    "endpoint": "/api/v1/endpoint",
    "method": "POST"
  },
  "timestamp": "2025-07-07T12:34:56.789Z",
  "retry_after": null
}
```

## Core Endpoints

### Health & Status

#### GET `/health`
System health check endpoint.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-07-07T12:34:56.789Z",
  "version": "1.0.0"
}
```

#### GET `/stats`
System statistics and metrics.

**Response:**
```json
{
  "articles_count": 2416,
  "sources_count": 12,
  "gaming_projects_count": 11,
  "last_scrape": "2025-07-07T12:00:00.000Z"
}
```

### Articles API

#### GET `/api/v1/articles`
Retrieve gaming articles with filtering and pagination.

**Query Parameters:**
- `skip` (int): Number of records to skip (default: 0)
- `limit` (int): Maximum records to return (default: 100)
- `category` (str): Filter by article category
- `source` (str): Filter by source name
- `date_from` (str): ISO date string for date range start
- `date_to` (str): ISO date string for date range end

**Response:**
```json
{
  "articles": [
    {
      "id": 1,
      "title": "Article Title",
      "content": "Article content...",
      "url": "https://source.com/article",
      "published_at": "2025-07-07T12:00:00.000Z",
      "source": "Source Name",
      "category": "gaming",
      "sentiment_score": 0.75
    }
  ],
  "total": 150,
  "skip": 0,
  "limit": 100
}
```

### Gaming Projects API

#### GET `/api/v1/gaming`
Retrieve gaming projects with blockchain and market data.

**Query Parameters:**
- `blockchain` (str): Filter by blockchain network
- `category` (str): Filter by game category
- `status` (str): Filter by project status

**Response:**
```json
{
  "projects": [
    {
      "id": 1,
      "name": "Axie Infinity",
      "symbol": "AXS",
      "blockchain": "ethereum",
      "category": "pet-battling",
      "status": "active",
      "market_cap": 1500000000,
      "token_price": 15.50,
      "nft_collections": [
        {
          "name": "Axie",
          "floor_price": 0.05,
          "total_supply": 10000000
        }
      ]
    }
  ]
}
```

### Blockchain Data API

#### GET `/api/v1/blockchain/data/test-connections`
Test blockchain API connections and status.

**Response:**
```json
{
  "connections": {
    "ethereum": {
      "status": "connected",
      "last_block": 18500000,
      "response_time": 150
    },
    "solana": {
      "status": "connected", 
      "last_slot": 250000000,
      "response_time": 200
    }
  }
}
```

## Phase 7: Content Intelligence API

### Content Classification

#### POST `/api/v1/content-intelligence/classify`
Classify gaming content using advanced NLP analysis.

**Request Body:**
```json
{
  "title": "New Gaming Project Launches on Solana",
  "content": "Full article content...",
  "summary": "Optional article summary",
  "source": "gaming-news-site"
}
```

**Response:**
```json
{
  "primary_category": "project_launch",
  "category_confidence": 0.92,
  "all_categories": {
    "project_launch": 0.92,
    "blockchain_integration": 0.78,
    "market_analysis": 0.45
  },
  "sentiment_score": 0.75,
  "sentiment_category": "positive",
  "gaming_entities": ["Solana", "NFT", "DeFi"],
  "blockchain_networks": ["solana"],
  "market_signals": {
    "bullish_indicators": 3,
    "bearish_indicators": 1,
    "neutral_indicators": 2
  },
  "trend_indicators": {
    "momentum": "increasing",
    "volume": "high",
    "social_sentiment": "positive"
  },
  "classification_timestamp": "2025-07-07T12:34:56.789Z"
}
```

### Sentiment Analysis

#### POST `/api/v1/content-intelligence/sentiment`
Analyze gaming community sentiment with enhanced scoring.

**Request Body:**
```json
{
  "title": "Gaming Article Title",
  "content": "Article content for sentiment analysis...",
  "summary": "Optional summary"
}
```

**Response:**
```json
{
  "overall_sentiment": "positive",
  "sentiment_score": 0.78,
  "sentiment_breakdown": {
    "community_sentiment": 0.82,
    "market_sentiment": 0.75,
    "technical_sentiment": 0.71,
    "adoption_sentiment": 0.85
  },
  "confidence_score": 0.91,
  "emotional_indicators": {
    "excitement": 0.8,
    "concern": 0.2,
    "optimism": 0.9,
    "skepticism": 0.1
  },
  "gaming_context": {
    "gameplay_sentiment": 0.85,
    "economy_sentiment": 0.70,
    "community_sentiment": 0.88
  }
}
```

### Trend Detection

#### POST `/api/v1/content-intelligence/trends`
Detect gaming trends and market patterns.

**Request Body:**
```json
{
  "content_items": [
    {
      "title": "Article 1",
      "content": "Content 1..."
    },
    {
      "title": "Article 2", 
      "content": "Content 2..."
    }
  ],
  "timeframe": "24h"
}
```

**Response:**
```json
{
  "emerging_trends": [
    {
      "trend_name": "AI Gaming Integration",
      "strength": 0.85,
      "growth_rate": 0.45,
      "related_projects": ["Project A", "Project B"],
      "trend_category": "technology"
    }
  ],
  "declining_trends": [
    {
      "trend_name": "Play-to-Earn Hype",
      "strength": 0.35,
      "decline_rate": -0.25,
      "affected_projects": ["Project C"]
    }
  ],
  "market_momentum": {
    "overall_direction": "bullish",
    "momentum_score": 0.72,
    "volatility": "medium"
  }
}
```

### Market Intelligence

#### POST `/api/v1/content-intelligence/market-intelligence`
Generate market intelligence insights from content analysis.

**Request Body:**
```json
{
  "content_items": [
    {
      "title": "Market Analysis Article",
      "content": "Market content...",
      "source": "crypto-news"
    }
  ],
  "analysis_depth": "comprehensive"
}
```

**Response:**
```json
{
  "market_insights": {
    "sector_performance": {
      "gaming": 0.78,
      "defi": 0.65,
      "nft": 0.82
    },
    "investment_signals": [
      {
        "signal_type": "bullish",
        "strength": 0.85,
        "timeframe": "short_term",
        "reasoning": "Strong community adoption"
      }
    ],
    "risk_assessment": {
      "overall_risk": "medium",
      "market_risk": 0.45,
      "technical_risk": 0.35,
      "regulatory_risk": 0.55
    }
  },
  "competitive_landscape": {
    "market_leaders": ["Axie Infinity", "The Sandbox"],
    "emerging_players": ["New Project A", "New Project B"],
    "market_concentration": 0.65
  }
}
```

### Entity Recognition

#### POST `/api/v1/content-intelligence/entities`
Extract and analyze gaming entities from content.

**Request Body:**
```json
{
  "content": "Text content for entity extraction...",
  "extract_relationships": true
}
```

**Response:**
```json
{
  "gaming_projects": ["Axie Infinity", "The Sandbox"],
  "tokens": ["AXS", "SAND", "ETH"],
  "blockchains": ["ethereum", "polygon"],
  "categories": ["metaverse", "pet-battling"],
  "confidence_scores": {
    "Axie Infinity": 0.95,
    "AXS": 0.92,
    "ethereum": 0.88
  },
  "entity_contexts": {
    "Axie Infinity": ["gaming", "nft", "play-to-earn"],
    "AXS": ["token", "governance", "staking"]
  },
  "entity_relationships": {
    "Axie Infinity": {
      "related_tokens": ["AXS", "SLP"],
      "blockchain": "ethereum",
      "category": "pet-battling"
    }
  },
  "dominant_ecosystem": {
    "name": "ethereum",
    "confidence": 0.85,
    "projects_count": 3
  }
}
```

## Market Analytics API

### Sector Analysis

#### GET `/api/v1/content-intelligence/market/sector-analysis`
Analyze gaming sector performance across protocols.

**Query Parameters:**
- `timeframe` (str): Analysis timeframe (24h, 7d, 30d)
- `protocols` (str): Comma-separated list of protocols

**Response:**
```json
{
  "sector_performance": {
    "ethereum_gaming": {
      "performance_score": 0.78,
      "market_cap_change": 0.15,
      "user_growth": 0.25,
      "transaction_volume": 1500000
    },
    "solana_gaming": {
      "performance_score": 0.82,
      "market_cap_change": 0.22,
      "user_growth": 0.35,
      "transaction_volume": 2100000
    }
  },
  "cross_protocol_analysis": {
    "leading_protocol": "solana",
    "growth_leader": "solana",
    "innovation_score": {
      "ethereum": 0.75,
      "solana": 0.88,
      "polygon": 0.70
    }
  }
}
```

### Portfolio Tracking

#### POST `/api/v1/content-intelligence/market/portfolio-tracking`
Track investment portfolio performance.

**Request Body:**
```json
{
  "portfolio": {
    "AXS": 100,
    "SAND": 500,
    "GALA": 1000
  },
  "benchmark": "gaming_index"
}
```

**Response:**
```json
{
  "portfolio_performance": {
    "total_value": 15750.50,
    "24h_change": 0.05,
    "7d_change": 0.12,
    "30d_change": -0.08
  },
  "asset_breakdown": {
    "AXS": {
      "value": 1550.00,
      "weight": 0.098,
      "performance": 0.03
    },
    "SAND": {
      "value": 7200.50,
      "weight": 0.457,
      "performance": 0.07
    }
  },
  "risk_metrics": {
    "portfolio_beta": 1.25,
    "sharpe_ratio": 0.85,
    "max_drawdown": -0.35,
    "volatility": 0.45
  }
}
```

### Market Alerts

#### GET `/api/v1/content-intelligence/market/alerts`
Retrieve active market alerts and notifications.

**Query Parameters:**
- `alert_type` (str): Filter by alert type
- `severity` (str): Filter by severity level
- `active_only` (bool): Show only active alerts

**Response:**
```json
{
  "alerts": [
    {
      "id": "alert_123",
      "type": "price_movement",
      "severity": "high",
      "title": "Significant Price Movement Detected",
      "message": "AXS token has moved +15% in the last hour",
      "project": "Axie Infinity",
      "token": "AXS",
      "trigger_value": 0.15,
      "threshold": 0.10,
      "created_at": "2025-07-07T12:30:00.000Z",
      "is_active": true
    }
  ],
  "alert_summary": {
    "total_alerts": 5,
    "high_severity": 1,
    "medium_severity": 2,
    "low_severity": 2
  }
}
```

## Competitive Analysis API

### Competitive Landscape

#### GET `/api/v1/content-intelligence/competitive/landscape`
Analyze competitive landscape of gaming projects.

**Query Parameters:**
- `category` (str): Filter by game category
- `blockchain` (str): Filter by blockchain
- `timeframe` (str): Analysis timeframe

**Response:**
```json
{
  "competitive_metrics": {
    "Axie Infinity": {
      "market_share": 0.25,
      "user_adoption_score": 0.88,
      "innovation_score": 0.75,
      "community_strength": 0.92,
      "technical_advancement": 0.70,
      "partnership_score": 0.85,
      "tokenomics_score": 0.80,
      "development_activity": 0.78,
      "overall_score": 0.82
    },
    "The Sandbox": {
      "market_share": 0.18,
      "user_adoption_score": 0.75,
      "innovation_score": 0.88,
      "community_strength": 0.80,
      "technical_advancement": 0.85,
      "partnership_score": 0.90,
      "tokenomics_score": 0.75,
      "development_activity": 0.82,
      "overall_score": 0.81
    }
  },
  "market_leaders": ["Axie Infinity", "The Sandbox", "Gala Games"],
  "emerging_competitors": ["Star Atlas", "Honeyland"],
  "market_trends": {
    "consolidation_index": 0.65,
    "innovation_rate": 0.78,
    "competition_intensity": "high"
  }
}
```

### Project Comparison

#### GET `/api/v1/content-intelligence/competitive/compare/{project_a}/{project_b}`
Compare two gaming projects across multiple metrics.

**Path Parameters:**
- `project_a` (str): First project name
- `project_b` (str): Second project name

**Response:**
```json
{
  "comparison": {
    "project_a": "Axie Infinity",
    "project_b": "The Sandbox",
    "metrics": {
      "market_share": {
        "project_a": 0.25,
        "project_b": 0.18,
        "winner": "project_a"
      },
      "user_adoption": {
        "project_a": 0.88,
        "project_b": 0.75,
        "winner": "project_a"
      },
      "innovation": {
        "project_a": 0.75,
        "project_b": 0.88,
        "winner": "project_b"
      }
    },
    "overall_winner": "project_a",
    "competitive_advantages": {
      "project_a": ["Strong community", "Proven gameplay"],
      "project_b": ["Technical innovation", "Partnership network"]
    }
  }
}
```

## Rate Limits

- **General API**: 1000 requests/hour per IP
- **Content Intelligence**: 100 requests/hour per IP
- **Market Analytics**: 200 requests/hour per IP
- **Blockchain Data**: 500 requests/hour per IP

## Response Codes

- `200` - Success
- `400` - Bad Request (validation error)
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `429` - Too Many Requests (rate limit)
- `500` - Internal Server Error

## SDKs and Libraries

Coming soon:
- Python SDK
- JavaScript/TypeScript SDK
- Go SDK

## Support

For API support and questions:
- Documentation: `/docs`
- Issues: GitHub repository
- Email: <EMAIL>
