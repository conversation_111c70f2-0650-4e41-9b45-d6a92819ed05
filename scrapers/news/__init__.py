"""
News scraping module for gaming content
"""

from .manager import (
    news_manager,
    run_scheduled_scraping,
    scrape_news_now,
    get_scraping_stats
)

from .gaming_sources import (
    scrape_all_sources,
    save_scraped_articles,
    GAMING_SCRAPERS
)

from .base import (
    NewsItem,
    BaseScraper,
    RSSFeedScraper,
    WebScraper
)

__all__ = [
    'news_manager',
    'run_scheduled_scraping',
    'scrape_news_now',
    'get_scraping_stats',
    'scrape_all_sources',
    'save_scraped_articles',
    'GAMING_SCRAPERS',
    'NewsItem',
    'BaseScraper',
    'RSSFeedScraper',
    'WebScraper'
]
