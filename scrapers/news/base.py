"""
Base news scraper classes and utilities
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from urllib.parse import urljoin, urlparse
import hashlib

import aiohttp
import feedparser
from bs4 import BeautifulSoup
from textblob import TextBlob

from models.base import SessionLocal
from models.gaming import Article, Source

logger = logging.getLogger(__name__)


class NewsItem:
    """Represents a scraped news item"""
    
    def __init__(self, **kwargs):
        self.title = kwargs.get('title', '')
        self.content = kwargs.get('content', '')
        self.summary = kwargs.get('summary', '')
        self.url = kwargs.get('url', '')
        self.author = kwargs.get('author', '')
        self.published_at = kwargs.get('published_at')
        self.source_url = kwargs.get('source_url', '')
        self.tags = kwargs.get('tags', [])
        self.keywords = kwargs.get('keywords', [])
        self.raw_data = kwargs.get('raw_data', {})
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'title': self.title,
            'content': self.content,
            'summary': self.summary,
            'url': self.url,
            'author': self.author,
            'published_at': self.published_at,
            'source_url': self.source_url,
            'tags': self.tags,
            'keywords': self.keywords,
            'raw_data': self.raw_data
        }
    
    def generate_hash(self) -> str:
        """Generate unique hash for deduplication"""
        content_for_hash = f"{self.title}{self.url}{self.published_at}"
        return hashlib.md5(content_for_hash.encode()).hexdigest()


class BaseScraper(ABC):
    """Base class for news scrapers"""
    
    def __init__(self, source_id: int, config: Dict[str, Any] = None):
        self.source_id = source_id
        self.config = config or {}
        self.session = None
        self.scraped_items = []
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            headers={
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    @abstractmethod
    async def scrape(self) -> List[NewsItem]:
        """Scrape news items from the source"""
        pass
    
    async def fetch_url(self, url: str) -> Optional[str]:
        """Fetch content from URL"""
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.text()
                else:
                    logger.warning(f"Failed to fetch {url}: {response.status}")
                    return None
        except Exception as e:
            logger.error(f"Error fetching {url}: {e}")
            return None
    
    def extract_text_content(self, html: str) -> str:
        """Extract clean text content from HTML"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Get text and clean it up
            text = soup.get_text()
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            return text
        except Exception as e:
            logger.error(f"Error extracting text content: {e}")
            return ""
    
    def extract_keywords(self, text: str, max_keywords: int = 10) -> List[str]:
        """Extract keywords from text using TextBlob"""
        try:
            blob = TextBlob(text)
            
            # Extract noun phrases as keywords
            keywords = []
            for phrase in blob.noun_phrases:
                if len(phrase.split()) <= 3:  # Keep short phrases
                    keywords.append(phrase.lower())
            
            # Remove duplicates and limit
            keywords = list(set(keywords))[:max_keywords]
            return keywords
        except Exception as e:
            logger.error(f"Error extracting keywords: {e}")
            return []
    
    def calculate_sentiment(self, text: str) -> float:
        """Calculate sentiment score using TextBlob"""
        try:
            blob = TextBlob(text)
            return blob.sentiment.polarity  # Returns -1 to 1
        except Exception as e:
            logger.error(f"Error calculating sentiment: {e}")
            return 0.0
    
    def is_gaming_related(self, text: str) -> bool:
        """Check if content is gaming related"""
        gaming_keywords = [
            'gaming', 'game', 'nft', 'metaverse', 'play-to-earn', 'p2e',
            'blockchain game', 'crypto game', 'virtual world', 'axie',
            'sandbox', 'decentraland', 'enjin', 'immutable', 'polygon',
            'ethereum game', 'defi game', 'gamefi', 'web3 game'
        ]
        
        text_lower = text.lower()
        return any(keyword in text_lower for keyword in gaming_keywords)


class RSSFeedScraper(BaseScraper):
    """RSS feed scraper"""
    
    async def scrape(self) -> List[NewsItem]:
        """Scrape RSS feed"""
        rss_url = self.config.get('rss_url')
        if not rss_url:
            logger.error("RSS URL not configured")
            return []
        
        try:
            # Fetch RSS feed
            content = await self.fetch_url(rss_url)
            if not content:
                return []
            
            # Parse RSS feed
            feed = feedparser.parse(content)
            items = []
            
            for entry in feed.entries:
                # Extract basic info
                title = getattr(entry, 'title', '')
                link = getattr(entry, 'link', '')
                summary = getattr(entry, 'summary', '')
                author = getattr(entry, 'author', '')
                
                # Parse published date
                published_at = None
                if hasattr(entry, 'published_parsed') and entry.published_parsed:
                    published_at = datetime(*entry.published_parsed[:6])
                
                # Skip if not gaming related
                if not self.is_gaming_related(f"{title} {summary}"):
                    continue
                
                # Fetch full content if needed
                content = summary
                if link and self.config.get('fetch_full_content', False):
                    full_html = await self.fetch_url(link)
                    if full_html:
                        content = self.extract_text_content(full_html)
                
                # Extract keywords and calculate sentiment
                keywords = self.extract_keywords(f"{title} {content}")
                sentiment = self.calculate_sentiment(content)
                
                # Create news item
                item = NewsItem(
                    title=title,
                    content=content,
                    summary=summary,
                    url=link,
                    author=author,
                    published_at=published_at,
                    source_url=rss_url,
                    keywords=keywords,
                    raw_data={
                        'sentiment_score': sentiment,
                        'feed_entry': dict(entry)
                    }
                )
                
                items.append(item)
            
            logger.info(f"Scraped {len(items)} gaming articles from RSS feed")
            return items
            
        except Exception as e:
            logger.error(f"Error scraping RSS feed {rss_url}: {e}")
            return []


class WebScraper(BaseScraper):
    """Generic web scraper for news websites"""
    
    async def scrape(self) -> List[NewsItem]:
        """Scrape web pages"""
        base_url = self.config.get('base_url')
        selectors = self.config.get('selectors', {})
        
        if not base_url or not selectors:
            logger.error("Base URL or selectors not configured")
            return []
        
        try:
            # Fetch main page
            content = await self.fetch_url(base_url)
            if not content:
                return []
            
            soup = BeautifulSoup(content, 'html.parser')
            items = []
            
            # Find article links
            article_selector = selectors.get('article_links', 'a')
            article_links = soup.select(article_selector)
            
            for link in article_links[:10]:  # Limit to 10 articles
                href = link.get('href')
                if not href:
                    continue
                
                # Make absolute URL
                article_url = urljoin(base_url, href)
                
                # Fetch article content
                article_html = await self.fetch_url(article_url)
                if not article_html:
                    continue
                
                article_soup = BeautifulSoup(article_html, 'html.parser')
                
                # Extract article data
                title = self._extract_by_selector(article_soup, selectors.get('title', 'h1'))
                content = self._extract_by_selector(article_soup, selectors.get('content', 'article'))
                author = self._extract_by_selector(article_soup, selectors.get('author', '.author'))
                
                # Skip if not gaming related
                if not self.is_gaming_related(f"{title} {content}"):
                    continue
                
                # Extract keywords and calculate sentiment
                keywords = self.extract_keywords(f"{title} {content}")
                sentiment = self.calculate_sentiment(content)
                
                # Create news item
                item = NewsItem(
                    title=title,
                    content=content,
                    summary=content[:500] + "..." if len(content) > 500 else content,
                    url=article_url,
                    author=author,
                    published_at=datetime.now(),  # Could be improved with date extraction
                    source_url=base_url,
                    keywords=keywords,
                    raw_data={
                        'sentiment_score': sentiment
                    }
                )
                
                items.append(item)
            
            logger.info(f"Scraped {len(items)} gaming articles from web")
            return items
            
        except Exception as e:
            logger.error(f"Error scraping web {base_url}: {e}")
            return []
    
    def _extract_by_selector(self, soup: BeautifulSoup, selector: str) -> str:
        """Extract text by CSS selector"""
        try:
            element = soup.select_one(selector)
            return element.get_text(strip=True) if element else ""
        except Exception:
            return ""
