"""
News scraping manager and orchestrator
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import json

from models.base import SessionLocal
from models.gaming import Article, Source
from .gaming_sources import scrape_all_sources, save_scraped_articles

logger = logging.getLogger(__name__)


class NewsScrapingManager:
    """Manages news scraping operations"""
    
    def __init__(self):
        self.is_running = False
        self.last_scrape_time = None
        self.scrape_stats = {
            'total_runs': 0,
            'successful_runs': 0,
            'total_articles_scraped': 0,
            'total_articles_saved': 0,
            'last_error': None
        }
    
    async def run_scraping_cycle(self) -> Dict[str, any]:
        """Run a complete scraping cycle"""
        if self.is_running:
            logger.warning("Scraping cycle already running")
            return {'status': 'already_running'}
        
        self.is_running = True
        start_time = datetime.now()
        
        try:
            logger.info("Starting news scraping cycle")
            
            # Scrape all sources
            scraped_data = await scrape_all_sources()
            
            # Count total articles scraped
            total_scraped = sum(len(articles) for articles in scraped_data.values())
            
            # Save articles to database
            saved_count = await save_scraped_articles(scraped_data)
            
            # Update stats
            self.scrape_stats['total_runs'] += 1
            self.scrape_stats['successful_runs'] += 1
            self.scrape_stats['total_articles_scraped'] += total_scraped
            self.scrape_stats['total_articles_saved'] += saved_count
            self.last_scrape_time = start_time
            
            duration = (datetime.now() - start_time).total_seconds()
            
            result = {
                'status': 'success',
                'duration_seconds': duration,
                'articles_scraped': total_scraped,
                'articles_saved': saved_count,
                'sources_processed': len(scraped_data),
                'timestamp': start_time.isoformat()
            }
            
            logger.info(f"Scraping cycle completed: {total_scraped} scraped, {saved_count} saved")
            return result
            
        except Exception as e:
            self.scrape_stats['last_error'] = str(e)
            logger.error(f"Error in scraping cycle: {e}")
            
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': start_time.isoformat()
            }
        
        finally:
            self.is_running = False
    
    async def scrape_single_source(self, source_slug: str) -> Dict[str, any]:
        """Scrape a single news source"""
        try:
            db = SessionLocal()
            source = db.query(Source).filter(Source.slug == source_slug).first()
            db.close()
            
            if not source:
                return {'status': 'error', 'error': f'Source {source_slug} not found'}
            
            logger.info(f"Scraping single source: {source.name}")
            
            from .gaming_sources import get_scraper_for_source
            
            scraper = get_scraper_for_source(source_slug, source.id)
            
            async with scraper:
                articles = await scraper.scrape()
            
            # Save articles
            saved_count = await save_scraped_articles({source_slug: articles})
            
            return {
                'status': 'success',
                'source': source.name,
                'articles_scraped': len(articles),
                'articles_saved': saved_count
            }
            
        except Exception as e:
            logger.error(f"Error scraping source {source_slug}: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def get_stats(self) -> Dict[str, any]:
        """Get scraping statistics"""
        return {
            **self.scrape_stats,
            'is_running': self.is_running,
            'last_scrape_time': self.last_scrape_time.isoformat() if self.last_scrape_time else None
        }
    
    async def get_recent_articles(self, hours: int = 24) -> List[Dict[str, any]]:
        """Get recently scraped articles"""
        db = SessionLocal()
        
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            articles = db.query(Article).filter(
                Article.created_at >= cutoff_time
            ).order_by(Article.created_at.desc()).limit(50).all()
            
            result = []
            for article in articles:
                result.append({
                    'id': article.id,
                    'title': article.title,
                    'summary': article.summary,
                    'url': article.url,
                    'author': article.author,
                    'published_at': article.published_at.isoformat() if article.published_at else None,
                    'created_at': article.created_at.isoformat(),
                    'source_id': article.source_id,
                    'keywords': article.keywords,
                    'sentiment_score': article.sentiment_score
                })
            
            return result
            
        finally:
            db.close()
    
    async def cleanup_old_articles(self, days: int = 30) -> int:
        """Clean up old articles"""
        db = SessionLocal()
        
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            
            # Count articles to be deleted
            count = db.query(Article).filter(
                Article.created_at < cutoff_time
            ).count()
            
            # Delete old articles
            db.query(Article).filter(
                Article.created_at < cutoff_time
            ).delete()
            
            db.commit()
            logger.info(f"Cleaned up {count} old articles")
            
            return count
            
        except Exception as e:
            logger.error(f"Error cleaning up articles: {e}")
            db.rollback()
            return 0
        finally:
            db.close()
    
    async def update_source_configs(self) -> Dict[str, any]:
        """Update source configurations"""
        db = SessionLocal()
        
        try:
            # Default configurations for known sources
            source_configs = {
                'coindesk-gaming': {
                    'scrape_frequency': 3600,  # 1 hour
                    'max_articles_per_run': 20,
                    'content_filters': ['gaming', 'nft', 'metaverse']
                },
                'decrypt-gaming': {
                    'scrape_frequency': 1800,  # 30 minutes
                    'max_articles_per_run': 15,
                    'content_filters': ['gaming', 'web3', 'blockchain']
                },
                'theblock-gaming': {
                    'scrape_frequency': 3600,  # 1 hour
                    'max_articles_per_run': 25,
                    'content_filters': ['gaming', 'nft', 'defi']
                }
            }
            
            updated_count = 0
            
            for slug, config in source_configs.items():
                source = db.query(Source).filter(Source.slug == slug).first()
                if source:
                    # Update source metadata
                    if not source.extra_metadata:
                        source.extra_metadata = {}
                    
                    source.extra_metadata.update({
                        'scraper_config': config,
                        'last_config_update': datetime.now().isoformat()
                    })
                    
                    updated_count += 1
            
            db.commit()
            logger.info(f"Updated configurations for {updated_count} sources")
            
            return {
                'status': 'success',
                'updated_sources': updated_count
            }
            
        except Exception as e:
            logger.error(f"Error updating source configs: {e}")
            db.rollback()
            return {'status': 'error', 'error': str(e)}
        finally:
            db.close()


# Global news scraping manager instance
news_manager = NewsScrapingManager()


async def run_scheduled_scraping():
    """Run scheduled news scraping (for use with task scheduler)"""
    return await news_manager.run_scraping_cycle()


async def scrape_news_now():
    """Immediate news scraping (for manual triggers)"""
    return await news_manager.run_scraping_cycle()


def get_scraping_stats():
    """Get current scraping statistics"""
    return news_manager.get_stats()
