"""
Machine Learning Gaming Contract Classifier
Uses ML to classify contracts as gaming vs non-gaming with confidence scoring
"""
import asyncio
import logging
import pickle
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import os

logger = logging.getLogger(__name__)

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report, accuracy_score
    from sklearn.preprocessing import StandardScaler
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logger.warning("scikit-learn not available. ML classification will be disabled.")

from .contract_detector import gaming_contract_detector, ContractAnalysisResult
from blockchain.multi_chain_client import multi_chain_manager
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class ContractFeatures:
    """Features extracted from a contract for ML classification"""
    contract_address: str
    blockchain: str
    
    # Code features
    bytecode_size: int
    function_count: int
    event_count: int
    
    # Function signature features
    gaming_function_ratio: float
    defi_function_ratio: float
    standard_function_ratio: float
    
    # Event signature features
    gaming_event_ratio: float
    transfer_event_count: int
    
    # Metadata features
    name_gaming_score: float
    symbol_gaming_score: float
    
    # Behavioral features
    transaction_count_24h: int
    unique_users_24h: int
    avg_gas_usage: float
    
    # Token standard features
    is_erc721: bool
    is_erc1155: bool
    is_erc20: bool
    
    # Network features
    deployment_age_days: int
    creator_contract_count: int


@dataclass
class ClassificationResult:
    """Result of ML classification with enhanced confidence metrics"""
    contract_address: str
    blockchain: str
    is_gaming_probability: float
    confidence_score: float
    feature_importance: Dict[str, float]
    classification_timestamp: datetime

    # Enhanced confidence metrics
    uncertainty_score: Optional[float] = None
    ensemble_agreement: Optional[float] = None
    prediction_stability: Optional[float] = None
    confidence_interval: Optional[List[float]] = None


class GamingContractMLClassifier:
    """Machine Learning classifier for gaming contracts"""
    
    def __init__(self):
        self.model: Optional[RandomForestClassifier] = None
        self.vectorizer: Optional[TfidfVectorizer] = None
        self.scaler: Optional[StandardScaler] = None
        self.is_trained = False
        self.model_path = "models/gaming_contract_classifier.pkl"
        self.vectorizer_path = "models/gaming_contract_vectorizer.pkl"
        self.scaler_path = "models/gaming_contract_scaler.pkl"
        
        # Enhanced gaming keywords for text features
        self.gaming_keywords = {
            # Core gaming terms
            'game', 'gaming', 'play', 'player', 'nft', 'collectible',
            'metaverse', 'virtual', 'avatar', 'character', 'hero',
            'monster', 'creature', 'pet', 'weapon', 'armor', 'item',
            'quest', 'adventure', 'dungeon', 'raid', 'battle',
            'guild', 'clan', 'team', 'tournament', 'competition',
            'reward', 'prize', 'loot', 'treasure', 'earn', 'p2e',

            # Expanded gaming terms
            'breed', 'breeding', 'evolution', 'evolve', 'upgrade',
            'craft', 'crafting', 'forge', 'forging', 'enchant',
            'spell', 'magic', 'mana', 'energy', 'stamina', 'health',
            'level', 'experience', 'xp', 'skill', 'ability', 'talent',
            'land', 'territory', 'kingdom', 'empire', 'world', 'realm',
            'season', 'episode', 'chapter', 'stage', 'mission',
            'boss', 'enemy', 'opponent', 'pvp', 'pve', 'combat',
            'arena', 'stadium', 'battlefield', 'war', 'siege',
            'alliance', 'faction', 'tribe', 'house', 'order',
            'marketplace', 'auction', 'trade', 'exchange', 'shop',
            'currency', 'token', 'coin', 'gem', 'crystal', 'shard',
            'deck', 'card', 'pack', 'booster', 'collection',
            'match', 'duel', 'fight', 'contest', 'championship',
            'leaderboard', 'ranking', 'score', 'achievement', 'badge',
            'unlock', 'discover', 'explore', 'journey', 'expedition',
            'resource', 'material', 'ingredient', 'component',
            'farm', 'farming', 'harvest', 'mine', 'mining',
            'build', 'construct', 'create', 'generate', 'produce',
            'defend', 'attack', 'strategy', 'tactical', 'strategic'
        }

        # Cross-chain gaming project patterns
        self.known_gaming_projects = {
            # Ethereum gaming projects
            'ethereum': {
                '******************************************',  # CryptoKitties
                '******************************************',  # Axie Infinity (old)
                '******************************************',  # Axie Infinity
                '******************************************',  # Gods Unchained
                '******************************************',  # The Sandbox LAND
                '******************************************',  # The Sandbox SAND
                '******************************************',  # Cool Cats
                '******************************************',  # Bored Ape Yacht Club
                '******************************************',  # Mutant Ape Yacht Club
                '******************************************',  # Otherdeed for Otherside
                '******************************************',  # Meebits
                '******************************************',  # Moonbirds
                '******************************************',  # Doodles
                '0x49cf6f5d44e70224e2e23fdcdd2c053f30ada28b',  # CloneX
                '0x348fc118bcc65a92dc033a951af153d14d945312',  # Bloot
                '******************************************',  # Loot
                '******************************************',  # ENS
                '******************************************',  # Parallel Alpha
                '******************************************',  # Nouns
                '******************************************',  # BAYC Otherdeeds
                '******************************************',  # Hape Prime
                '******************************************',  # Cryptoadz
                '******************************************',  # Mfers
                '******************************************',  # 0N1 Force
                '******************************************',  # Rumble Kong League
                '******************************************',  # Creature World
                '******************************************',  # Ethermon
                '******************************************',  # Chainmonsters
                '******************************************',  # Age of Rust
                '******************************************',  # The Sandbox Assets
                '******************************************',  # Gala Games
                '******************************************',  # Gala Games Token
                '******************************************',  # Ember Sword Land
                '******************************************',  # The Sandbox
                '******************************************',  # Star Atlas (placeholder)
                '******************************************',  # Illuvium (placeholder)
            },

            # Polygon gaming projects
            'polygon': {
                '******************************************',  # Aavegotchi
                '******************************************',  # Aavegotchi GHST
                '0x385eeac5cb85a38a9a07a70c73e0a3271cfb54a7',  # Aavegotchi Realm
                '0x1d0360bac7299c86ec8e99d0c1c9a95fefaf2a11',  # Revv Racing
                '0x70c006878a5a50ed185ac4c87d837633923de296',  # Revv Token
                '0x2953399124f0cbb46d2cbacd8a89cf0599974963',  # OpenSea Polygon
                '******************************************',  # MakerDAO (cross-chain)
                '******************************************',  # Decentraland Polygon
                '******************************************',  # DAI Polygon
                '******************************************',  # USDC Polygon
                '******************************************',  # USDT Polygon
                '******************************************',  # WMATIC
                '******************************************',  # QUICK
                '******************************************',  # MKR Polygon
                '******************************************',  # LINK Polygon
                '******************************************',  # CRV Polygon
                '******************************************',  # CRV Polygon
                '******************************************',  # BAL Polygon
                '******************************************',  # WBTC Polygon
                '******************************************',  # WETH Polygon
                '******************************************',  # DPI Polygon
                '******************************************',  # ANT Polygon
                '******************************************',  # jEUR Polygon
                '******************************************',  # DAI Polygon
                '******************************************',  # USDC Polygon
                '******************************************',  # USDT Polygon
                '******************************************',  # WMATIC
                '******************************************',  # QUICK
            },

            # BSC gaming projects
            'bsc': {
                '******************************************',  # PancakeSwap CAKE
                '******************************************',  # USDT BSC
                '******************************************',  # USDC BSC
                '******************************************',  # DAI BSC
                '******************************************',  # ETH BSC
                '******************************************',  # BTCB BSC
                '******************************************',  # WBNB
                '******************************************',  # BUSD BSC
                '******************************************',  # ADA BSC
                '******************************************',  # XRP BSC
                '******************************************',  # BCH BSC
                '******************************************',  # DOT BSC
                '******************************************',  # AVAX BSC
                '******************************************',  # LINK BSC
                '******************************************',  # LTC BSC
                '******************************************',  # EOS BSC
                '******************************************',  # ZEC BSC
                '******************************************',  # XTZ BSC
                '******************************************',  # ATOM BSC
                '0x14016e85a25aeb13065688cafb43044c2ef86784',  # TRUE BSC
                '0x85eac5ac2f758618dfa09bdbe0cf174e7d574d5b',  # TRX BSC
                '0x4b0f1812e5df2a09796481ff14017e6005508003',  # TWT BSC
                '0x715d400f88537b51125958653e1d918ce9a36e87',  # XVS BSC
                '0x42f6f551ae042cbe50c739158b4f0cac0edb9096',  # NRV BSC
                '0x8519ea49c997f50ceffa444d240fb655e89248aa',  # RAMP BSC
                '0x7950865a9140cb519342433146ed5b40c6f210f7',  # PAXG BSC
                '0x12bb890508c125661e03b09ec06e404bc9289040',  # RADIO BSC
                '0x0d8ce2a99bb6e3b7db580ed848240e4a0f9ae153',  # FIL BSC
            },

            # Ronin gaming projects (Axie Infinity ecosystem)
            'ronin': {
                '0x97a9107c1793bc407d6f527b77e7fff4d812bece',  # AXS
                '******************************************',  # SLP
                '******************************************',  # Axie Infinity
                '******************************************',  # Axie Land
                '******************************************',  # Axie Items
                '******************************************',  # Katana DEX
                '******************************************',  # WRON
                '******************************************',  # WETH Ronin
                '******************************************',  # USDC Ronin
                '******************************************',  # Axie Marketplace
                '******************************************',  # Mavis Market
                '******************************************',  # Ronin Bridge
                '******************************************',  # Katana Router
                '******************************************',  # Ronin Validator
                '******************************************',  # Axie Breeding
                '******************************************',  # Axie Core
                '******************************************',  # Axie Genes
                '******************************************',  # Axie Battle
                '******************************************',  # Axie Adventure
                '******************************************',  # Axie Arena
                '0xc0c0199d96b6b6b6b6b6b6b6b6b6b6b6b6b6b6b6',  # Axie Scholarship
                '0xd1d1199d96b6b6b6b6b6b6b6b6b6b6b6b6b6b6b6',  # Axie Rewards
                '0xe2e2199d96b6b6b6b6b6b6b6b6b6b6b6b6b6b6b6',  # Axie Staking
                '0xf3f3199d96b6b6b6b6b6b6b6b6b6b6b6b6b6b6b6',  # Axie Governance
                '0x0404199d96b6b6b6b6b6b6b6b6b6b6b6b6b6b6b6',  # Axie Treasury
                '******************************************',  # Axie Ecosystem
            },

            # Solana gaming projects
            'solana': {
                'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',  # Serum DEX
                'So11111111111111111111111111111111111111112',   # Wrapped SOL
                'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC Solana
                'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB',  # USDT Solana
                '9n4nbM75f5Ui33ZbPYXn59EwSgE8CGsHtAeTH5YFeJ9E',  # BTC Solana
                '2FPyTwcZLUg1MDrwsyoP4D6s1tM7hAkHYRjkNb5w6Pxk',  # ETH Solana
                'SRMuApVNdxXokk5GT7XD5cUUgXMBCoAz2LHeuAoKWRt',   # SRM
                'kinXdEcpDQeHPEuQnqmUgtYykqKGVFq6CeVX5iAHJq6',   # KIN
                'StepAscQoEioFxxWGnh2sLBDFp9d8rvKz2Yp39iDpyT',   # STEP
                'RLBxxFkseAZ4RgJH3Sqn8jXxhmGoz9jWxDNJMh8pL7a',   # RLB
                'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',   # ORCA
                'SHDWyBxihqiCj6YekG2GUr7wqKLeLAMK1gHZck9pL6y',   # SHDW
                'DUSTawucrTsGU8hcqRdHDCbuYhCPADMLM2VcCb8VnFnQ',  # DUST
                'ATLASXmbPQxBUYbxPsV97usA3fPQYEqzQBUHgiFCUsXx',  # ATLAS
                'poLisWXnNRwC6oBu1vHiuKQzFjGL4XDSu4g9qjz9qVk',   # POLIS
                'GENEtH5amGSi8kHAtQoezp1XEXwZJ8vcuePYnXdKrMYz',  # GENE
                'SLNDpmoWTVADgEdndyvWzroNL7zSi1dF9PC3xHGtPwp',   # SLND
                'MangoCzJ36AjZyKwVj3VnYU4GTonjfVEnJmvvWaxLac',   # MNGO
                'J1toso1uCk3RLmjorhTtrVwY9HJ7X8V9yYac6Y7kGCPn',  # JitoSOL
                'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So',   # mSOL
                'bSo13r4TkiE4KumL71LsHTPpL2euBYLFx6h9HP3piy1',   # bSOL
                'DFL1zNkaGPWm1BqAzVqRjCZvHmwTFrEaJtbzJWgseoNJ',  # DFL
                'CKaKtYvz6dKPyMvYq9Rh3UBrnNqYZAyd7iF4hJtjUvks',  # GARI
                'GDfnEsia2WLAW5t8yx2X5j2mkfA74i5kwGdDuZHt7XmG',  # GRAPE
                'HezGNpJMZJT6k6TdkzZc9XEQtHyFTYM8ztZbCZJJ6VdJ',  # HADES
                'FnKE9n6aGjQoNWRBZXy4RW6LZVao7qwBonUbiD7edUmZ',  # SYP
                'GsNzxJfFn6zQdJGeYsupJWzUAm57Ba7335mfhWvFiE9Z',  # GMT
                'GST2xZZJNiHp9gF7mVNjElNhUZQhQMYWLBNaV2BWEfch',  # GST
            }
        }
        
        # DeFi keywords for contrast
        self.defi_keywords = {
            'swap', 'pool', 'liquidity', 'vault', 'farm', 'yield',
            'lending', 'borrowing', 'flash', 'oracle', 'governance',
            'voting', 'proposal', 'timelock', 'multisig'
        }

        # Enhanced gaming function signatures with pattern categories
        self.gaming_functions = {
            # Core gameplay functions
            'mint', 'breed', 'battle', 'fight', 'attack', 'defend',
            'craft', 'forge', 'upgrade', 'evolve', 'level', 'levelup',
            'quest', 'adventure', 'explore', 'discover', 'unlock',
            'reward', 'claim', 'earn', 'harvest', 'farm', 'stake',
            'unstake', 'withdraw', 'deposit', 'play', 'game',
            'tournament', 'compete', 'win', 'lose', 'score',
            'leaderboard', 'rank', 'achievement', 'badge',
            'inventory', 'equipment', 'weapon', 'armor', 'item',
            'character', 'hero', 'avatar', 'pet', 'creature',
            'monster', 'boss', 'enemy', 'npc', 'guild', 'clan',
            'team', 'party', 'alliance', 'faction', 'house',
            'land', 'territory', 'kingdom', 'world', 'realm',
            'season', 'episode', 'chapter', 'stage', 'mission',
            'spell', 'magic', 'mana', 'energy', 'stamina', 'health',
            'experience', 'xp', 'skill', 'ability', 'talent',
            'marketplace', 'auction', 'trade', 'exchange', 'shop',
            'currency', 'token', 'coin', 'gem', 'crystal', 'shard',
            'deck', 'card', 'pack', 'booster', 'collection',
            'match', 'duel', 'pvp', 'pve', 'combat', 'arena',
            'stadium', 'battlefield', 'war', 'siege', 'raid',
            'dungeon', 'treasure', 'loot', 'prize', 'drop',

            # Advanced gaming patterns
            'summon', 'spawn', 'respawn', 'revive', 'resurrect',
            'enchant', 'disenchant', 'transmute', 'alchemy',
            'research', 'study', 'learn', 'teach', 'train',
            'recruit', 'hire', 'dismiss', 'promote', 'demote',
            'construct', 'build', 'destroy', 'demolish', 'repair',
            'scout', 'spy', 'infiltrate', 'sabotage', 'ambush',
            'negotiate', 'diplomacy', 'treaty', 'alliance', 'war',
            'colonize', 'expand', 'conquer', 'liberate', 'occupy',
            'resource', 'material', 'ingredient', 'component',
            'recipe', 'blueprint', 'formula', 'pattern', 'design',
            'customize', 'personalize', 'modify', 'enhance', 'boost',
            'buff', 'debuff', 'curse', 'bless', 'heal', 'damage',
            'critical', 'dodge', 'block', 'parry', 'counter',
            'combo', 'chain', 'sequence', 'rotation', 'cooldown',
            'duration', 'effect', 'status', 'condition', 'state',
            'transform', 'morph', 'shapeshift', 'metamorphosis',
            'teleport', 'portal', 'warp', 'travel', 'journey',
            'expedition', 'voyage', 'pilgrimage', 'migration',
            'settlement', 'colony', 'outpost', 'base', 'fortress',
            'castle', 'tower', 'wall', 'gate', 'bridge',
            'road', 'path', 'route', 'passage', 'tunnel',
            'mine', 'quarry', 'well', 'spring', 'oasis',
            'forest', 'jungle', 'desert', 'mountain', 'valley',
            'river', 'lake', 'ocean', 'island', 'continent'
        }

        # Gaming function pattern categories for advanced analysis
        self.gaming_function_categories = {
            'combat': {'battle', 'fight', 'attack', 'defend', 'combat', 'war', 'siege', 'raid', 'pvp', 'pve'},
            'progression': {'level', 'levelup', 'upgrade', 'evolve', 'experience', 'xp', 'skill', 'ability'},
            'economy': {'trade', 'exchange', 'marketplace', 'auction', 'shop', 'currency', 'token', 'coin'},
            'crafting': {'craft', 'forge', 'upgrade', 'enchant', 'transmute', 'alchemy', 'recipe', 'blueprint'},
            'breeding': {'breed', 'mint', 'spawn', 'summon', 'create', 'generate', 'produce'},
            'social': {'guild', 'clan', 'team', 'party', 'alliance', 'faction', 'tournament', 'compete'},
            'exploration': {'quest', 'adventure', 'explore', 'discover', 'unlock', 'journey', 'expedition'},
            'collection': {'collect', 'gather', 'harvest', 'farm', 'inventory', 'equipment', 'item'},
            'territory': {'land', 'territory', 'kingdom', 'world', 'realm', 'build', 'construct', 'expand'},
            'rewards': {'reward', 'claim', 'earn', 'prize', 'loot', 'treasure', 'drop', 'achievement'}
        }

        # Standard function signatures
        self.standard_functions = {
            'transfer', 'approve', 'balanceof', 'allowance',
            'totalsupply', 'name', 'symbol', 'decimals'
        }
        
        if SKLEARN_AVAILABLE:
            self._load_model()
    
    def _load_model(self):
        """Load trained model from disk"""
        try:
            if (os.path.exists(self.model_path) and 
                os.path.exists(self.vectorizer_path) and 
                os.path.exists(self.scaler_path)):
                
                with open(self.model_path, 'rb') as f:
                    self.model = pickle.load(f)
                
                with open(self.vectorizer_path, 'rb') as f:
                    self.vectorizer = pickle.load(f)
                
                with open(self.scaler_path, 'rb') as f:
                    self.scaler = pickle.load(f)
                
                self.is_trained = True
                logger.info("Loaded trained gaming contract classifier")
            else:
                logger.info("No trained model found. Will need to train first.")
                
        except Exception as e:
            logger.error(f"Error loading model: {e}")
    
    def _save_model(self):
        """Save trained model to disk"""
        try:
            os.makedirs(os.path.dirname(self.model_path), exist_ok=True)
            
            with open(self.model_path, 'wb') as f:
                pickle.dump(self.model, f)
            
            with open(self.vectorizer_path, 'wb') as f:
                pickle.dump(self.vectorizer, f)
            
            with open(self.scaler_path, 'wb') as f:
                pickle.dump(self.scaler, f)
            
            logger.info("Saved trained gaming contract classifier")
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
    
    async def extract_features(self, contract_address: str, blockchain: str) -> Optional[ContractFeatures]:
        """Extract features from a contract for ML classification"""
        try:
            client = multi_chain_manager.get_client(blockchain)
            if not client:
                return None
            
            # Get basic contract info
            contract_code = await client.get_contract_code(contract_address)
            if not contract_code or contract_code == '0x':
                return None
            
            # Get contract metadata
            metadata = {}
            try:
                name = await client.call_contract_function(contract_address, 'name', [])
                symbol = await client.call_contract_function(contract_address, 'symbol', [])
                metadata = {'name': name or '', 'symbol': symbol or ''}
            except:
                pass
            
            # Get function and event signatures
            function_signatures = await self._get_function_signatures(client, contract_address)
            event_signatures = await self._get_event_signatures(client, contract_address)
            
            # Get behavioral data
            behavioral_data = await self._get_behavioral_data(client, contract_address)
            
            # Calculate features
            features = ContractFeatures(
                contract_address=contract_address,
                blockchain=blockchain,
                
                # Code features
                bytecode_size=len(contract_code) // 2,  # Convert hex to bytes
                function_count=len(function_signatures),
                event_count=len(event_signatures),
                
                # Function signature features
                gaming_function_ratio=self._calculate_gaming_function_ratio(function_signatures),
                defi_function_ratio=self._calculate_defi_function_ratio(function_signatures),
                standard_function_ratio=self._calculate_standard_function_ratio(function_signatures),
                
                # Event signature features
                gaming_event_ratio=self._calculate_gaming_event_ratio(event_signatures),
                transfer_event_count=self._count_transfer_events(event_signatures),
                
                # Metadata features
                name_gaming_score=self._calculate_text_gaming_score(metadata.get('name', '')),
                symbol_gaming_score=self._calculate_text_gaming_score(metadata.get('symbol', '')),
                
                # Behavioral features
                transaction_count_24h=behavioral_data.get('transaction_count_24h', 0),
                unique_users_24h=behavioral_data.get('unique_users_24h', 0),
                avg_gas_usage=behavioral_data.get('avg_gas_usage', 0),
                
                # Token standard features
                is_erc721=self._check_erc721(function_signatures),
                is_erc1155=self._check_erc1155(function_signatures),
                is_erc20=self._check_erc20(function_signatures),
                
                # Network features
                deployment_age_days=behavioral_data.get('deployment_age_days', 0),
                creator_contract_count=behavioral_data.get('creator_contract_count', 0)
            )
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting features for {contract_address}: {e}")
            return None
    
    async def _get_function_signatures(self, client, contract_address: str) -> List[str]:
        """Get function signatures from contract"""
        try:
            # This would require ABI analysis or bytecode disassembly
            # For now, return empty list as placeholder
            return []
        except:
            return []
    
    async def _get_event_signatures(self, client, contract_address: str) -> List[str]:
        """Get event signatures from contract"""
        try:
            # Get recent events to infer signatures
            events = await client.get_contract_events(contract_address, limit=100)
            return [event.get('event', '') for event in events if event.get('event')]
        except:
            return []
    
    async def _get_behavioral_data(self, client, contract_address: str) -> Dict[str, Any]:
        """Get behavioral data for the contract"""
        try:
            # This would require historical transaction analysis
            # For now, return default values
            return {
                'transaction_count_24h': 0,
                'unique_users_24h': 0,
                'avg_gas_usage': 0,
                'deployment_age_days': 0,
                'creator_contract_count': 0
            }
        except:
            return {}
    
    def _calculate_gaming_function_ratio(self, functions: List[str]) -> float:
        """Calculate ratio of gaming-related functions"""
        if not functions:
            return 0.0
        
        gaming_count = 0
        for func in functions:
            func_lower = func.lower()
            for keyword in self.gaming_keywords:
                if keyword in func_lower:
                    gaming_count += 1
                    break
        
        return gaming_count / len(functions)
    
    def _calculate_defi_function_ratio(self, functions: List[str]) -> float:
        """Calculate ratio of DeFi-related functions"""
        if not functions:
            return 0.0
        
        defi_count = 0
        for func in functions:
            func_lower = func.lower()
            for keyword in self.defi_keywords:
                if keyword in func_lower:
                    defi_count += 1
                    break
        
        return defi_count / len(functions)
    
    def _calculate_standard_function_ratio(self, functions: List[str]) -> float:
        """Calculate ratio of standard ERC functions"""
        if not functions:
            return 0.0
        
        standard_count = 0
        for func in functions:
            func_lower = func.lower()
            if func_lower in self.standard_functions:
                standard_count += 1
        
        return standard_count / len(functions)
    
    def _calculate_gaming_event_ratio(self, events: List[str]) -> float:
        """Calculate ratio of gaming-related events"""
        if not events:
            return 0.0
        
        gaming_count = 0
        for event in events:
            event_lower = event.lower()
            for keyword in self.gaming_keywords:
                if keyword in event_lower:
                    gaming_count += 1
                    break
        
        return gaming_count / len(events)
    
    def _count_transfer_events(self, events: List[str]) -> int:
        """Count transfer events"""
        return sum(1 for event in events if 'transfer' in event.lower())
    
    def _calculate_text_gaming_score(self, text: str) -> float:
        """Calculate gaming score for text (name/symbol)"""
        if not text:
            return 0.0
        
        text_lower = text.lower()
        score = 0.0
        
        for keyword in self.gaming_keywords:
            if keyword in text_lower:
                score += 1.0
        
        return min(1.0, score / 3)  # Normalize to 0-1
    
    def _check_erc721(self, functions: List[str]) -> bool:
        """Check if contract implements ERC721"""
        erc721_functions = {'ownerof', 'approve', 'transferfrom', 'safetransferfrom'}
        functions_lower = {f.lower() for f in functions}
        return len(erc721_functions.intersection(functions_lower)) >= 3
    
    def _check_erc1155(self, functions: List[str]) -> bool:
        """Check if contract implements ERC1155"""
        erc1155_functions = {'balanceofbatch', 'safebatchtransferfrom', 'setapprovalforall'}
        functions_lower = {f.lower() for f in functions}
        return len(erc1155_functions.intersection(functions_lower)) >= 2
    
    def _check_erc20(self, functions: List[str]) -> bool:
        """Check if contract implements ERC20"""
        erc20_functions = {'transfer', 'approve', 'balanceof', 'allowance'}
        functions_lower = {f.lower() for f in functions}
        return len(erc20_functions.intersection(functions_lower)) >= 3

    def _calculate_gaming_category_ratios(self, function_signatures: List[str]) -> Dict[str, float]:
        """Calculate ratios for each gaming function category"""
        if not function_signatures:
            return {f'{category}_ratio': 0.0 for category in self.gaming_function_categories}

        category_ratios = {}
        for category, keywords in self.gaming_function_categories.items():
            category_count = sum(1 for sig in function_signatures
                               if any(keyword in sig.lower() for keyword in keywords))
            category_ratios[f'{category}_ratio'] = category_count / len(function_signatures)

        return category_ratios

    async def _analyze_cross_chain_presence(self, contract_address: str, blockchain: str) -> float:
        """Analyze if this contract/project exists on multiple chains"""
        try:
            cross_chain_score = 0.0
            total_chains = len(self.known_gaming_projects)

            # Check if this is a known gaming project
            for chain, contracts in self.known_gaming_projects.items():
                if contract_address.lower() in [addr.lower() for addr in contracts]:
                    cross_chain_score += 1.0

            return cross_chain_score / total_chains

        except Exception as e:
            logger.error(f"Error analyzing cross-chain presence: {e}")
            return 0.0

    def _calculate_blockchain_gaming_score(self, blockchain: str) -> float:
        """Calculate gaming activity score for the blockchain"""
        # Gaming-focused blockchains get higher scores
        gaming_scores = {
            'ronin': 1.0,      # Axie Infinity ecosystem
            'polygon': 0.8,    # Many gaming projects
            'bsc': 0.7,        # Some gaming activity
            'ethereum': 0.6,   # High-value gaming NFTs
            'solana': 0.9,     # Growing gaming ecosystem
            'avalanche': 0.5,  # Some gaming projects
            'arbitrum': 0.4,   # L2 with some gaming
            'optimism': 0.4,   # L2 with some gaming
            'base': 0.3,       # Newer chain
        }

        return gaming_scores.get(blockchain.lower(), 0.2)

    def _calculate_temporal_features(self, behavioral_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate temporal analysis features"""
        try:
            # Get transaction counts for different periods
            tx_24h = behavioral_data.get('transaction_count_24h', 0)
            tx_7d = behavioral_data.get('transaction_count_7d', 0)
            tx_30d = behavioral_data.get('transaction_count_30d', 0)

            # Calculate velocity and growth patterns
            tx_velocity_24h = tx_24h / 24 if tx_24h > 0 else 0
            tx_growth_7d = (tx_7d - tx_24h * 7) / max(tx_24h * 7, 1) if tx_24h > 0 else 0
            tx_growth_30d = (tx_30d - tx_7d * 4.3) / max(tx_7d * 4.3, 1) if tx_7d > 0 else 0

            # User engagement patterns
            unique_users_24h = behavioral_data.get('unique_users_24h', 0)
            user_retention_7d = behavioral_data.get('user_retention_7d', 0)
            avg_transactions_per_user = tx_24h / max(unique_users_24h, 1) if unique_users_24h > 0 else 0

            return {
                'tx_velocity_24h': tx_velocity_24h,
                'tx_growth_7d': tx_growth_7d,
                'tx_growth_30d': tx_growth_30d,
                'user_retention_7d': user_retention_7d,
                'avg_transactions_per_user': avg_transactions_per_user
            }

        except Exception as e:
            logger.error(f"Error calculating temporal features: {e}")
            return {
                'tx_velocity_24h': 0.0,
                'tx_growth_7d': 0.0,
                'tx_growth_30d': 0.0,
                'user_retention_7d': 0.0,
                'avg_transactions_per_user': 0.0
            }
    
    def _features_to_array(self, features: ContractFeatures) -> np.ndarray:
        """Convert features to numpy array for ML model"""
        return np.array([
            features.bytecode_size,
            features.function_count,
            features.event_count,
            features.gaming_function_ratio,
            features.defi_function_ratio,
            features.standard_function_ratio,
            features.gaming_event_ratio,
            features.transfer_event_count,
            features.name_gaming_score,
            features.symbol_gaming_score,
            features.transaction_count_24h,
            features.unique_users_24h,
            features.avg_gas_usage,
            int(features.is_erc721),
            int(features.is_erc1155),
            int(features.is_erc20),
            features.deployment_age_days,
            features.creator_contract_count
        ]).reshape(1, -1)
    
    async def classify_contract(self, contract_address: str, blockchain: str) -> Optional[ClassificationResult]:
        """Classify a contract using ML model"""
        if not SKLEARN_AVAILABLE or not self.is_trained:
            logger.warning("ML classifier not available or not trained")
            return None
        
        try:
            # Extract features
            features = await self.extract_features(contract_address, blockchain)
            if not features:
                return None
            
            # Convert to array and scale
            feature_array = self._features_to_array(features)
            scaled_features = self.scaler.transform(feature_array)
            
            # Make prediction
            probability = self.model.predict_proba(scaled_features)[0]
            is_gaming_prob = probability[1] if len(probability) > 1 else probability[0]
            
            # Enhanced confidence scoring with uncertainty quantification
            confidence_metrics = self._calculate_enhanced_confidence(
                scaled_features[0], probability, features
            )

            # Get feature importance
            feature_names = [
                'bytecode_size', 'function_count', 'event_count',
                'gaming_function_ratio', 'defi_function_ratio', 'standard_function_ratio',
                'gaming_event_ratio', 'transfer_event_count',
                'name_gaming_score', 'symbol_gaming_score',
                'transaction_count_24h', 'unique_users_24h', 'avg_gas_usage',
                'is_erc721', 'is_erc1155', 'is_erc20',
                'deployment_age_days', 'creator_contract_count'
            ]

            feature_importance = dict(zip(feature_names, self.model.feature_importances_))

            return ClassificationResult(
                contract_address=contract_address,
                blockchain=blockchain,
                is_gaming_probability=float(is_gaming_prob),
                confidence_score=confidence_metrics['calibrated_confidence'],
                feature_importance=feature_importance,
                classification_timestamp=datetime.utcnow(),
                # Enhanced confidence metrics
                uncertainty_score=confidence_metrics['uncertainty_score'],
                ensemble_agreement=confidence_metrics['ensemble_agreement'],
                prediction_stability=confidence_metrics['prediction_stability'],
                confidence_interval=confidence_metrics['confidence_interval']
            )
            
        except Exception as e:
            logger.error(f"Error classifying contract {contract_address}: {e}")
            return None

    def _calculate_enhanced_confidence(self, feature_array: np.ndarray, probabilities: np.ndarray,
                                     features: ContractFeatures) -> Dict[str, float]:
        """Calculate enhanced confidence metrics with uncertainty quantification"""
        try:
            # Base confidence from model probability
            base_confidence = max(probabilities)

            # Uncertainty quantification using entropy
            entropy = -sum(p * np.log(p + 1e-10) for p in probabilities if p > 0)
            max_entropy = np.log(len(probabilities))
            uncertainty_score = entropy / max_entropy if max_entropy > 0 else 0

            # Ensemble agreement simulation (using bootstrap-like approach)
            ensemble_predictions = []
            for _ in range(10):  # Simulate 10 ensemble members
                # Add small noise to features to simulate ensemble diversity
                noisy_features = feature_array + np.random.normal(0, 0.01, feature_array.shape)
                pred_proba = self.model.predict_proba([noisy_features])[0]
                ensemble_predictions.append(pred_proba[1] if len(pred_proba) > 1 else pred_proba[0])

            ensemble_agreement = 1.0 - np.std(ensemble_predictions)

            # Prediction stability based on feature sensitivity
            stability_score = self._calculate_prediction_stability(feature_array, features)

            # Calibrated confidence combining multiple factors
            calibrated_confidence = (
                base_confidence * 0.4 +
                (1 - uncertainty_score) * 0.3 +
                ensemble_agreement * 0.2 +
                stability_score * 0.1
            )

            # Confidence interval estimation
            confidence_interval = self._estimate_confidence_interval(ensemble_predictions)

            return {
                'calibrated_confidence': float(calibrated_confidence),
                'uncertainty_score': float(uncertainty_score),
                'ensemble_agreement': float(ensemble_agreement),
                'prediction_stability': float(stability_score),
                'confidence_interval': confidence_interval
            }

        except Exception as e:
            logger.error(f"Error calculating enhanced confidence: {e}")
            return {
                'calibrated_confidence': float(max(probabilities)),
                'uncertainty_score': 0.5,
                'ensemble_agreement': 0.5,
                'prediction_stability': 0.5,
                'confidence_interval': [0.0, 1.0]
            }

    def _calculate_prediction_stability(self, feature_array: np.ndarray, features: ContractFeatures) -> float:
        """Calculate prediction stability based on feature sensitivity"""
        try:
            base_prediction = self.model.predict_proba([feature_array])[0]

            # Test sensitivity to small feature perturbations
            perturbation_results = []
            for i in range(len(feature_array)):
                # Create small perturbation
                perturbed_features = feature_array.copy()
                perturbed_features[i] *= 1.05  # 5% increase

                perturbed_prediction = self.model.predict_proba([perturbed_features])[0]

                # Calculate prediction change
                prediction_change = np.abs(base_prediction - perturbed_prediction).max()
                perturbation_results.append(prediction_change)

            # Stability is inverse of average sensitivity
            avg_sensitivity = np.mean(perturbation_results)
            stability = 1.0 / (1.0 + avg_sensitivity * 10)  # Scale and invert

            return stability

        except Exception as e:
            logger.error(f"Error calculating prediction stability: {e}")
            return 0.5

    def _estimate_confidence_interval(self, ensemble_predictions: List[float]) -> List[float]:
        """Estimate confidence interval from ensemble predictions"""
        try:
            if not ensemble_predictions:
                return [0.0, 1.0]

            # Calculate 95% confidence interval
            sorted_predictions = sorted(ensemble_predictions)
            n = len(sorted_predictions)

            lower_idx = max(0, int(0.025 * n))
            upper_idx = min(n - 1, int(0.975 * n))

            return [float(sorted_predictions[lower_idx]), float(sorted_predictions[upper_idx])]

        except Exception as e:
            logger.error(f"Error estimating confidence interval: {e}")
            return [0.0, 1.0]

    async def generate_expanded_training_data(self) -> List[Tuple[ContractFeatures, bool]]:
        """Generate expanded training data from known gaming contracts"""
        training_data = []

        try:
            # Generate positive examples from known gaming projects
            for blockchain, contracts in self.known_gaming_projects.items():
                for contract_address in contracts:
                    try:
                        features = await self.extract_features(contract_address, blockchain)
                        if features:
                            training_data.append((features, True))
                            logger.info(f"Added positive training example: {contract_address} ({blockchain})")
                    except Exception as e:
                        logger.warning(f"Failed to extract features for {contract_address}: {e}")
                        continue

            # Generate negative examples from known non-gaming contracts
            negative_contracts = await self._get_negative_training_examples()
            for blockchain, contracts in negative_contracts.items():
                for contract_address in contracts:
                    try:
                        features = await self.extract_features(contract_address, blockchain)
                        if features:
                            training_data.append((features, False))
                            logger.info(f"Added negative training example: {contract_address} ({blockchain})")
                    except Exception as e:
                        logger.warning(f"Failed to extract features for {contract_address}: {e}")
                        continue

            logger.info(f"Generated {len(training_data)} training examples")
            return training_data

        except Exception as e:
            logger.error(f"Error generating training data: {e}")
            return []

    async def _get_negative_training_examples(self) -> Dict[str, List[str]]:
        """Get known non-gaming contracts for negative training examples"""
        return {
            'ethereum': [
                '******************************************',  # Uniswap V2 Router
                '******************************************',  # Uniswap V2 Router 02
                '******************************************',  # Uniswap V3 Router
                '******************************************',  # Uniswap V3 Router 2
                '******************************************',  # OpenSea Registry
                '******************************************',  # OpenSea Seaport
                '******************************************',  # OpenSea Exchange
                '******************************************',  # OpenSea Wyvern Exchange
                '******************************************',  # DAI Stablecoin
                '******************************************',  # USDC
                '******************************************',  # USDT
                '******************************************',  # WBTC
                '******************************************',  # WETH
                '******************************************',  # LINK
                '******************************************',  # UNI
                '******************************************',  # MATIC
                '******************************************',  # SUSHI
                '******************************************',  # YFI
                '******************************************',  # MKR
                '******************************************',  # REP
                '******************************************',  # COMP
                '******************************************',  # BAT
                '******************************************',  # ZRX
                '******************************************',  # BUSD
                '******************************************',  # PAXG
                '******************************************',  # GUSD
                '******************************************',  # FRAX
                '0xa693b19d2931d498c5b318df961919bb4aee87a5',  # UST
                '0x5f98805a4e8be255a32880fdec7f6728c6568ba0',  # LUSD
                '******************************************',  # MIM
                '******************************************',  # CVX
                '******************************************',  # CRV
                '******************************************',  # BAL
                '******************************************',  # SNX
                '******************************************',  # MEME
                '******************************************',  # FRXETH
                '******************************************',  # RETH
                '******************************************',  # CBETH
                '******************************************',  # STETH
            ],
            'polygon': [
                '******************************************',  # WBTC Polygon
                '******************************************',  # WETH Polygon
                '******************************************',  # DAI Polygon
                '******************************************',  # USDC Polygon
                '******************************************',  # USDT Polygon
                '******************************************',  # WMATIC
                '******************************************',  # QUICK
                '******************************************',  # LINK Polygon
                '******************************************',  # CRV Polygon
                '******************************************',  # BAL Polygon
                '******************************************',  # DPI Polygon
                '******************************************',  # ANT Polygon
                '******************************************',  # jEUR Polygon
                '******************************************',  # amUSDC
                '******************************************',  # amDAI
                '******************************************',  # amUSDT
                '******************************************',  # amMATIC
                '******************************************',  # amAAVE
                '******************************************',  # amWBTC
                '******************************************',  # amWETH
                '******************************************',  # amLINK
                '******************************************',  # amCRV
                '******************************************',  # amBAL
                '******************************************',  # amMKR
                '******************************************',  # amYFI
                '******************************************',  # amSNX
                '******************************************',  # amUNI
                '******************************************',  # amSUSHI
            ],
            'bsc': [
                '******************************************',  # USDT BSC
                '******************************************',  # USDC BSC
                '******************************************',  # DAI BSC
                '******************************************',  # ETH BSC
                '******************************************',  # BTCB BSC
                '******************************************',  # WBNB
                '******************************************',  # BUSD BSC
                '******************************************',  # ADA BSC
                '******************************************',  # XRP BSC
                '******************************************',  # BCH BSC
                '******************************************',  # DOT BSC
                '******************************************',  # AVAX BSC
                '******************************************',  # LINK BSC
                '******************************************',  # LTC BSC
                '******************************************',  # EOS BSC
                '******************************************',  # ZEC BSC
                '******************************************',  # XTZ BSC
                '******************************************',  # ATOM BSC
                '0x14016e85a25aeb13065688cafb43044c2ef86784',  # TRUE BSC
                '0x85eac5ac2f758618dfa09bdbe0cf174e7d574d5b',  # TRX BSC
                '0x4b0f1812e5df2a09796481ff14017e6005508003',  # TWT BSC
                '0x715d400f88537b51125958653e1d918ce9a36e87',  # XVS BSC
                '0x42f6f551ae042cbe50c739158b4f0cac0edb9096',  # NRV BSC
                '0x8519ea49c997f50ceffa444d240fb655e89248aa',  # RAMP BSC
                '0x7950865a9140cb519342433146ed5b40c6f210f7',  # PAXG BSC
                '0x12bb890508c125661e03b09ec06e404bc9289040',  # RADIO BSC
                '0x0d8ce2a99bb6e3b7db580ed848240e4a0f9ae153',  # FIL BSC
            ]
        }

    async def train_model(self, training_data: List[Tuple[ContractFeatures, bool]]):
        """Train the ML model with labeled data"""
        if not SKLEARN_AVAILABLE:
            logger.error("scikit-learn not available for training")
            return
        
        try:
            # Prepare training data
            X = []
            y = []
            
            for features, is_gaming in training_data:
                feature_array = self._features_to_array(features).flatten()
                X.append(feature_array)
                y.append(int(is_gaming))
            
            X = np.array(X)
            y = np.array(y)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            self.scaler = StandardScaler()
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Enhanced model with better hyperparameters
            self.model = RandomForestClassifier(
                n_estimators=200,  # Increased from 100
                max_depth=15,      # Increased from 10
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                random_state=42,
                class_weight='balanced',
                n_jobs=-1  # Use all available cores
            )

            self.model.fit(X_train_scaled, y_train)

            # Evaluate model
            y_pred = self.model.predict(X_test_scaled)
            y_pred_proba = self.model.predict_proba(X_test_scaled)

            accuracy = accuracy_score(y_test, y_pred)

            logger.info(f"Enhanced model trained with accuracy: {accuracy:.3f}")
            logger.info(f"Classification report:\n{classification_report(y_test, y_pred)}")

            # Log feature importance
            feature_names = [
                'bytecode_size', 'function_count', 'event_count',
                'gaming_function_ratio', 'defi_function_ratio', 'standard_function_ratio',
                'gaming_event_ratio', 'transfer_event_count',
                'name_gaming_score', 'symbol_gaming_score',
                'transaction_count_24h', 'unique_users_24h', 'avg_gas_usage',
                'is_erc721', 'is_erc1155', 'is_erc20',
                'deployment_age_days', 'creator_contract_count'
            ]

            feature_importance = dict(zip(feature_names, self.model.feature_importances_))
            sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)

            logger.info("Top 10 most important features:")
            for feature, importance in sorted_features[:10]:
                logger.info(f"  {feature}: {importance:.4f}")

            self.is_trained = True
            self._save_model()
            
        except Exception as e:
            logger.error(f"Error training model: {e}")


@dataclass
class CrossChainAnalysisResult:
    """Result of cross-chain gaming analysis"""
    project_name: str
    total_chains: int
    active_chains: List[str]
    cross_chain_score: float
    ecosystem_mapping: Dict[str, Any]
    correlation_metrics: Dict[str, float]
    unified_activity_score: float
    analysis_timestamp: datetime


class CrossChainGamingAnalyzer:
    """Analyzer for cross-chain gaming activity and ecosystem mapping"""

    def __init__(self):
        self.chain_weights = {
            'ethereum': 1.0,    # High-value gaming NFTs
            'polygon': 0.9,     # Many gaming projects
            'ronin': 1.0,       # Axie Infinity ecosystem
            'bsc': 0.7,         # Some gaming activity
            'solana': 0.9,      # Growing gaming ecosystem
            'avalanche': 0.6,   # Some gaming projects
            'arbitrum': 0.5,    # L2 with some gaming
            'optimism': 0.5,    # L2 with some gaming
            'base': 0.4,        # Newer chain
        }

    async def analyze_cross_chain_project(self, project_contracts: Dict[str, str]) -> CrossChainAnalysisResult:
        """Analyze a gaming project across multiple chains"""
        try:
            active_chains = []
            ecosystem_data = {}
            correlation_scores = {}

            # Analyze each chain where the project exists
            for chain, contract_address in project_contracts.items():
                if not contract_address:
                    continue

                # Get chain-specific analysis
                chain_analysis = await self._analyze_chain_activity(chain, contract_address)
                if chain_analysis['is_active']:
                    active_chains.append(chain)
                    ecosystem_data[chain] = chain_analysis

            # Calculate cross-chain correlation metrics
            if len(active_chains) > 1:
                correlation_scores = await self._calculate_cross_chain_correlations(ecosystem_data)

            # Calculate unified ecosystem score
            unified_score = self._calculate_unified_activity_score(ecosystem_data)

            # Calculate cross-chain presence score
            cross_chain_score = len(active_chains) / len(project_contracts) if project_contracts else 0

            return CrossChainAnalysisResult(
                project_name=project_contracts.get('name', 'Unknown'),
                total_chains=len(project_contracts),
                active_chains=active_chains,
                cross_chain_score=cross_chain_score,
                ecosystem_mapping=ecosystem_data,
                correlation_metrics=correlation_scores,
                unified_activity_score=unified_score,
                analysis_timestamp=datetime.utcnow()
            )

        except Exception as e:
            logger.error(f"Error in cross-chain analysis: {e}")
            return None

    async def _analyze_chain_activity(self, chain: str, contract_address: str) -> Dict[str, Any]:
        """Analyze gaming activity on a specific chain"""
        try:
            client = multi_chain_manager.get_client(chain)
            if not client:
                return {'is_active': False, 'activity_score': 0}

            # Get basic activity metrics
            activity_data = await self._get_chain_activity_metrics(client, contract_address)

            # Calculate activity score based on multiple factors
            activity_score = self._calculate_chain_activity_score(activity_data, chain)

            return {
                'is_active': activity_score > 0.1,  # Threshold for considering chain active
                'activity_score': activity_score,
                'transaction_volume': activity_data.get('transaction_count_24h', 0),
                'unique_users': activity_data.get('unique_users_24h', 0),
                'avg_gas_usage': activity_data.get('avg_gas_usage', 0),
                'chain_weight': self.chain_weights.get(chain, 0.3)
            }

        except Exception as e:
            logger.error(f"Error analyzing chain activity for {chain}: {e}")
            return {'is_active': False, 'activity_score': 0}

    async def _get_chain_activity_metrics(self, client, contract_address: str) -> Dict[str, Any]:
        """Get activity metrics for a contract on a specific chain"""
        try:
            # This would integrate with actual blockchain data
            # For now, return simulated data
            return {
                'transaction_count_24h': 0,
                'unique_users_24h': 0,
                'avg_gas_usage': 0,
                'volume_24h': 0
            }
        except:
            return {}

    def _calculate_chain_activity_score(self, activity_data: Dict[str, Any], chain: str) -> float:
        """Calculate normalized activity score for a chain"""
        try:
            # Normalize metrics based on chain characteristics
            tx_count = activity_data.get('transaction_count_24h', 0)
            unique_users = activity_data.get('unique_users_24h', 0)

            # Chain-specific normalization factors
            normalization_factors = {
                'ethereum': {'tx_factor': 100, 'user_factor': 50},
                'polygon': {'tx_factor': 1000, 'user_factor': 200},
                'ronin': {'tx_factor': 5000, 'user_factor': 1000},
                'bsc': {'tx_factor': 500, 'user_factor': 100},
                'solana': {'tx_factor': 2000, 'user_factor': 400},
            }

            factors = normalization_factors.get(chain, {'tx_factor': 100, 'user_factor': 50})

            # Calculate normalized score
            tx_score = min(1.0, tx_count / factors['tx_factor'])
            user_score = min(1.0, unique_users / factors['user_factor'])

            # Weighted combination
            activity_score = (tx_score * 0.6 + user_score * 0.4) * self.chain_weights.get(chain, 0.3)

            return activity_score

        except Exception as e:
            logger.error(f"Error calculating activity score: {e}")
            return 0.0


    async def _calculate_cross_chain_correlations(self, ecosystem_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate correlation metrics between chains"""
        try:
            correlations = {}
            chains = list(ecosystem_data.keys())

            # Calculate pairwise correlations
            for i, chain1 in enumerate(chains):
                for chain2 in chains[i+1:]:
                    correlation = self._calculate_pairwise_correlation(
                        ecosystem_data[chain1], ecosystem_data[chain2]
                    )
                    correlations[f'{chain1}_{chain2}'] = correlation

            # Calculate overall ecosystem coherence
            if correlations:
                correlations['ecosystem_coherence'] = sum(correlations.values()) / len(correlations)

            return correlations

        except Exception as e:
            logger.error(f"Error calculating cross-chain correlations: {e}")
            return {}

    def _calculate_pairwise_correlation(self, chain1_data: Dict[str, Any],
                                      chain2_data: Dict[str, Any]) -> float:
        """Calculate correlation between two chains' activity"""
        try:
            # Simple correlation based on activity scores and user overlap
            score1 = chain1_data.get('activity_score', 0)
            score2 = chain2_data.get('activity_score', 0)

            # Simulate user overlap (in real implementation, this would analyze actual user addresses)
            user_overlap = min(score1, score2) * 0.3  # Simplified overlap estimation

            # Activity correlation
            activity_correlation = 1.0 - abs(score1 - score2)

            # Combined correlation score
            correlation = (activity_correlation * 0.7 + user_overlap * 0.3)

            return correlation

        except Exception as e:
            logger.error(f"Error calculating pairwise correlation: {e}")
            return 0.0

    def _calculate_unified_activity_score(self, ecosystem_data: Dict[str, Any]) -> float:
        """Calculate unified activity score across all chains"""
        try:
            if not ecosystem_data:
                return 0.0

            total_weighted_score = 0.0
            total_weight = 0.0

            for chain, data in ecosystem_data.items():
                activity_score = data.get('activity_score', 0)
                chain_weight = data.get('chain_weight', 0.3)

                total_weighted_score += activity_score * chain_weight
                total_weight += chain_weight

            unified_score = total_weighted_score / total_weight if total_weight > 0 else 0.0

            # Bonus for multi-chain presence
            multi_chain_bonus = min(0.2, len(ecosystem_data) * 0.05)
            unified_score += multi_chain_bonus

            return min(1.0, unified_score)

        except Exception as e:
            logger.error(f"Error calculating unified activity score: {e}")
            return 0.0

    async def analyze_gaming_ecosystem_trends(self, projects: List[Dict[str, str]]) -> Dict[str, Any]:
        """Analyze trends across the entire gaming ecosystem"""
        try:
            ecosystem_trends = {
                'total_projects': len(projects),
                'chain_distribution': {},
                'cross_chain_projects': 0,
                'ecosystem_health_score': 0.0,
                'trending_chains': [],
                'correlation_matrix': {}
            }

            chain_activity = {}
            cross_chain_count = 0

            # Analyze each project
            for project in projects:
                project_analysis = await self.analyze_cross_chain_project(project)
                if project_analysis:
                    # Count cross-chain projects
                    if len(project_analysis.active_chains) > 1:
                        cross_chain_count += 1

                    # Aggregate chain activity
                    for chain in project_analysis.active_chains:
                        if chain not in chain_activity:
                            chain_activity[chain] = []
                        chain_activity[chain].append(project_analysis.unified_activity_score)

            # Calculate chain distribution and trends
            for chain, scores in chain_activity.items():
                ecosystem_trends['chain_distribution'][chain] = {
                    'project_count': len(scores),
                    'avg_activity': sum(scores) / len(scores) if scores else 0,
                    'total_activity': sum(scores)
                }

            # Identify trending chains
            trending_chains = sorted(
                ecosystem_trends['chain_distribution'].items(),
                key=lambda x: x[1]['total_activity'],
                reverse=True
            )[:5]

            ecosystem_trends['trending_chains'] = [chain for chain, _ in trending_chains]
            ecosystem_trends['cross_chain_projects'] = cross_chain_count

            # Calculate overall ecosystem health
            if chain_activity:
                total_activity = sum(
                    data['total_activity']
                    for data in ecosystem_trends['chain_distribution'].values()
                )
                ecosystem_trends['ecosystem_health_score'] = min(1.0, total_activity / len(projects))

            return ecosystem_trends

        except Exception as e:
            logger.error(f"Error analyzing ecosystem trends: {e}")
            return {}


# Global instances
gaming_ml_classifier = GamingContractMLClassifier()
cross_chain_analyzer = CrossChainGamingAnalyzer()
