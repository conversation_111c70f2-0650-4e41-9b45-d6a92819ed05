"""
Redis caching service for Web3 Gaming News Tracker
Optimized caching strategy for gaming data and dashboard metrics
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
import redis
import asyncio
from functools import wraps
import hashlib
import pickle
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class GamingRedisCache:
    """Redis cache manager optimized for gaming data patterns"""
    
    def __init__(self):
        self.redis_client = None
        self.is_connected = False
        self._connect()
    
    def _connect(self):
        """Initialize Redis connection with error handling"""
        try:
            self.redis_client = redis.Redis.from_url(
                settings.redis.url,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )
            # Test connection
            self.redis_client.ping()
            self.is_connected = True
            logger.info("Redis cache connected successfully")
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            self.is_connected = False
    
    def _ensure_connection(self):
        """Ensure Redis connection is active"""
        if not self.is_connected:
            self._connect()
        
        if not self.is_connected:
            raise ConnectionError("Redis cache is not available")
    
    def _generate_key(self, prefix: str, *args, **kwargs) -> str:
        """Generate consistent cache key from parameters"""
        key_parts = [prefix]
        
        # Add positional arguments
        for arg in args:
            if isinstance(arg, (dict, list)):
                key_parts.append(hashlib.md5(json.dumps(arg, sort_keys=True).encode()).hexdigest()[:8])
            else:
                key_parts.append(str(arg))
        
        # Add keyword arguments
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            kwargs_str = json.dumps(sorted_kwargs, sort_keys=True)
            key_parts.append(hashlib.md5(kwargs_str.encode()).hexdigest()[:8])
        
        return ":".join(key_parts)
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache with error handling"""
        try:
            self._ensure_connection()
            value = self.redis_client.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            logger.warning(f"Cache get error for key {key}: {e}")
            return None
    
    def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """Set value in cache with TTL"""
        try:
            self._ensure_connection()
            serialized_value = json.dumps(value, default=str)
            return self.redis_client.setex(key, ttl, serialized_value)
        except Exception as e:
            logger.warning(f"Cache set error for key {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """Delete key from cache"""
        try:
            self._ensure_connection()
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logger.warning(f"Cache delete error for key {key}: {e}")
            return False
    
    def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern"""
        try:
            self._ensure_connection()
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.warning(f"Cache delete pattern error for {pattern}: {e}")
            return 0
    
    def exists(self, key: str) -> bool:
        """Check if key exists in cache"""
        try:
            self._ensure_connection()
            return bool(self.redis_client.exists(key))
        except Exception as e:
            logger.warning(f"Cache exists error for key {key}: {e}")
            return False
    
    def get_ttl(self, key: str) -> int:
        """Get TTL for key"""
        try:
            self._ensure_connection()
            return self.redis_client.ttl(key)
        except Exception as e:
            logger.warning(f"Cache TTL error for key {key}: {e}")
            return -1
    
    # Gaming-specific cache methods
    
    def cache_dashboard_overview(self, data: Dict[str, Any], hours: int = 24) -> bool:
        """Cache dashboard overview data"""
        key = self._generate_key("dashboard:overview", hours)
        return self.set(key, data, ttl=300)  # 5 minutes TTL
    
    def get_dashboard_overview(self, hours: int = 24) -> Optional[Dict[str, Any]]:
        """Get cached dashboard overview data"""
        key = self._generate_key("dashboard:overview", hours)
        return self.get(key)
    
    def cache_gaming_projects(self, projects: List[Dict[str, Any]]) -> bool:
        """Cache gaming projects list"""
        key = "gaming:projects:all"
        return self.set(key, projects, ttl=1800)  # 30 minutes TTL
    
    def get_gaming_projects(self) -> Optional[List[Dict[str, Any]]]:
        """Get cached gaming projects"""
        key = "gaming:projects:all"
        return self.get(key)
    
    def cache_articles_by_category(self, category: str, articles: List[Dict[str, Any]], hours: int = 24) -> bool:
        """Cache articles by gaming category"""
        key = self._generate_key("articles:category", category, hours)
        return self.set(key, articles, ttl=600)  # 10 minutes TTL
    
    def get_articles_by_category(self, category: str, hours: int = 24) -> Optional[List[Dict[str, Any]]]:
        """Get cached articles by category"""
        key = self._generate_key("articles:category", category, hours)
        return self.get(key)
    
    def cache_blockchain_analytics(self, blockchain: str, data: Dict[str, Any], hours: int = 24) -> bool:
        """Cache blockchain analytics data"""
        key = self._generate_key("blockchain:analytics", blockchain, hours)
        return self.set(key, data, ttl=900)  # 15 minutes TTL
    
    def get_blockchain_analytics(self, blockchain: str, hours: int = 24) -> Optional[Dict[str, Any]]:
        """Get cached blockchain analytics"""
        key = self._generate_key("blockchain:analytics", blockchain, hours)
        return self.get(key)
    
    def cache_token_prices(self, prices: Dict[str, Any]) -> bool:
        """Cache gaming token prices"""
        key = "gaming:token_prices"
        return self.set(key, prices, ttl=300)  # 5 minutes TTL
    
    def get_token_prices(self) -> Optional[Dict[str, Any]]:
        """Get cached token prices"""
        key = "gaming:token_prices"
        return self.get(key)
    
    def cache_nft_floor_prices(self, collection_address: str, price_data: Dict[str, Any]) -> bool:
        """Cache NFT floor prices"""
        key = self._generate_key("nft:floor_price", collection_address)
        return self.set(key, price_data, ttl=600)  # 10 minutes TTL
    
    def get_nft_floor_prices(self, collection_address: str) -> Optional[Dict[str, Any]]:
        """Get cached NFT floor prices"""
        key = self._generate_key("nft:floor_price", collection_address)
        return self.get(key)
    
    def cache_user_activity_metrics(self, project_id: str, metrics: Dict[str, Any]) -> bool:
        """Cache user activity metrics for gaming projects"""
        key = self._generate_key("metrics:user_activity", project_id)
        return self.set(key, metrics, ttl=1800)  # 30 minutes TTL
    
    def get_user_activity_metrics(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Get cached user activity metrics"""
        key = self._generate_key("metrics:user_activity", project_id)
        return self.get(key)
    
    def invalidate_gaming_data(self, project_name: Optional[str] = None):
        """Invalidate gaming-related cache entries"""
        patterns = [
            "dashboard:*",
            "gaming:*",
            "articles:*",
            "blockchain:*",
            "metrics:*"
        ]
        
        if project_name:
            patterns.append(f"*{project_name}*")
        
        total_deleted = 0
        for pattern in patterns:
            deleted = self.delete_pattern(pattern)
            total_deleted += deleted
            logger.info(f"Invalidated {deleted} cache entries for pattern: {pattern}")
        
        return total_deleted
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        try:
            self._ensure_connection()
            info = self.redis_client.info()
            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory_human", "0B"),
                "keyspace_hits": info.get("keyspace_hits", 0),
                "keyspace_misses": info.get("keyspace_misses", 0),
                "hit_rate": info.get("keyspace_hits", 0) / max(1, info.get("keyspace_hits", 0) + info.get("keyspace_misses", 0)),
                "total_commands_processed": info.get("total_commands_processed", 0)
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}


# Global cache instance
gaming_cache = GamingRedisCache()


def cache_result(ttl: int = 3600, key_prefix: str = ""):
    """Decorator for caching function results"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = gaming_cache._generate_key(
                key_prefix or f"func:{func.__name__}",
                *args,
                **kwargs
            )

            # Try to get from cache
            cached_result = gaming_cache.get(cache_key)
            if cached_result is not None:
                return cached_result

            # Execute function and cache result
            result = func(*args, **kwargs)
            gaming_cache.set(cache_key, result, ttl)
            return result

        return wrapper
    return decorator


def cache_with_ttl(ttl_seconds: int = 3600, key_prefix: str = ""):
    """Alias for cache_result with ttl_seconds parameter name"""
    return cache_result(ttl=ttl_seconds, key_prefix=key_prefix)
