"""
Competitive Analysis Framework for Gaming Projects
Phase 7: Gaming project comparison metrics and competitive intelligence
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio

try:
    import numpy as np
    ANALYTICS_AVAILABLE = True
except ImportError:
    ANALYTICS_AVAILABLE = False

logger = logging.getLogger(__name__)


class CompetitiveMetric(Enum):
    """Types of competitive metrics"""
    MARKET_SHARE = "market_share"
    USER_ADOPTION = "user_adoption"
    INNOVATION_SCORE = "innovation_score"
    COMMUNITY_STRENGTH = "community_strength"
    TECHNICAL_ADVANCEMENT = "technical_advancement"
    PARTNERSHIP_NETWORK = "partnership_network"
    TOKENOMICS_HEALTH = "tokenomics_health"
    DEVELOPMENT_ACTIVITY = "development_activity"


@dataclass
class CompetitivePosition:
    """Competitive position analysis result"""
    project_name: str
    overall_rank: int
    market_position_score: float
    competitive_advantages: List[str]
    competitive_weaknesses: List[str]
    threat_level: str  # low, medium, high
    opportunity_score: float
    strategic_recommendations: List[str]
    analysis_timestamp: datetime


@dataclass
class MarketComparison:
    """Market comparison between projects"""
    project_a: str
    project_b: str
    comparison_metrics: Dict[str, float]
    winner: str
    win_margin: float
    key_differentiators: List[str]
    comparison_timestamp: datetime


class CompetitiveAnalysisEngine:
    """Advanced competitive analysis for gaming projects"""
    
    def __init__(self):
        self.gaming_projects = {
            'axie-infinity': {
                'name': 'Axie Infinity',
                'category': 'p2e',
                'blockchain': 'ronin',
                'launch_date': '2018-03-01',
                'market_cap_rank': 1,
                'user_base': 2800000,
                'daily_active_users': 1200000,
                'token_price': 65.0,
                'partnerships': ['Samsung', 'Ubisoft', 'Binance'],
                'key_features': ['breeding', 'battles', 'land', 'scholarships'],
                'development_team_size': 150
            },
            'star-atlas': {
                'name': 'Star Atlas',
                'category': 'metaverse',
                'blockchain': 'solana',
                'launch_date': '2021-01-01',
                'market_cap_rank': 2,
                'user_base': 500000,
                'daily_active_users': 150000,
                'token_price': 0.08,
                'partnerships': ['Solana Labs', 'FTX', 'Serum'],
                'key_features': ['space_exploration', 'nft_ships', 'dao_governance', 'defi_integration'],
                'development_team_size': 80
            },
            'decentraland': {
                'name': 'Decentraland',
                'category': 'metaverse',
                'blockchain': 'ethereum',
                'launch_date': '2017-08-01',
                'market_cap_rank': 3,
                'user_base': 800000,
                'daily_active_users': 300000,
                'token_price': 0.85,
                'partnerships': ['Samsung', 'Atari', 'Polygon'],
                'key_features': ['virtual_land', 'events', 'marketplace', 'builder_tools'],
                'development_team_size': 60
            },
            'the-sandbox': {
                'name': 'The Sandbox',
                'category': 'metaverse',
                'blockchain': 'ethereum',
                'launch_date': '2018-05-01',
                'market_cap_rank': 4,
                'user_base': 1000000,
                'daily_active_users': 400000,
                'token_price': 0.75,
                'partnerships': ['Adidas', 'Snoop Dogg', 'Warner Music'],
                'key_features': ['voxel_creation', 'game_maker', 'land_ownership', 'avatar_system'],
                'development_team_size': 100
            },
            'gala-games': {
                'name': 'Gala Games',
                'category': 'gaming-platform',
                'blockchain': 'ethereum',
                'launch_date': '2019-07-01',
                'market_cap_rank': 5,
                'user_base': 1300000,
                'daily_active_users': 500000,
                'token_price': 0.04,
                'partnerships': ['Epic Games', 'Brave', 'Flare Networks'],
                'key_features': ['multiple_games', 'node_network', 'nft_marketplace', 'governance'],
                'development_team_size': 120
            }
        }
        
        self.competitive_weights = {
            CompetitiveMetric.MARKET_SHARE: 0.20,
            CompetitiveMetric.USER_ADOPTION: 0.18,
            CompetitiveMetric.INNOVATION_SCORE: 0.15,
            CompetitiveMetric.COMMUNITY_STRENGTH: 0.12,
            CompetitiveMetric.TECHNICAL_ADVANCEMENT: 0.10,
            CompetitiveMetric.PARTNERSHIP_NETWORK: 0.10,
            CompetitiveMetric.TOKENOMICS_HEALTH: 0.08,
            CompetitiveMetric.DEVELOPMENT_ACTIVITY: 0.07
        }
    
    async def analyze_competitive_landscape(self, focus_projects: Optional[List[str]] = None) -> Dict[str, Any]:
        """Analyze the competitive landscape of gaming projects"""
        
        try:
            projects_to_analyze = focus_projects or list(self.gaming_projects.keys())
            
            # Calculate competitive scores for each project
            competitive_scores = {}
            for project_id in projects_to_analyze:
                scores = await self._calculate_competitive_scores(project_id)
                competitive_scores[project_id] = scores
            
            # Rank projects
            rankings = self._rank_projects(competitive_scores)
            
            # Identify market leaders and challengers
            market_dynamics = self._analyze_market_dynamics(competitive_scores)
            
            # Generate competitive insights
            insights = self._generate_competitive_insights(competitive_scores, rankings)
            
            return {
                'competitive_scores': competitive_scores,
                'rankings': rankings,
                'market_dynamics': market_dynamics,
                'competitive_insights': insights,
                'analysis_timestamp': datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"Error in competitive landscape analysis: {e}")
            return self._get_fallback_competitive_data()
    
    async def _calculate_competitive_scores(self, project_id: str) -> Dict[str, float]:
        """Calculate competitive scores for a project"""
        
        project_data = self.gaming_projects[project_id]
        scores = {}
        
        # Market Share Score (based on market cap rank and user base)
        market_share_score = self._calculate_market_share_score(project_data)
        scores[CompetitiveMetric.MARKET_SHARE.value] = market_share_score
        
        # User Adoption Score
        user_adoption_score = self._calculate_user_adoption_score(project_data)
        scores[CompetitiveMetric.USER_ADOPTION.value] = user_adoption_score
        
        # Innovation Score
        innovation_score = self._calculate_innovation_score(project_data)
        scores[CompetitiveMetric.INNOVATION_SCORE.value] = innovation_score
        
        # Community Strength Score
        community_score = self._calculate_community_strength_score(project_data)
        scores[CompetitiveMetric.COMMUNITY_STRENGTH.value] = community_score
        
        # Technical Advancement Score
        technical_score = self._calculate_technical_advancement_score(project_data)
        scores[CompetitiveMetric.TECHNICAL_ADVANCEMENT.value] = technical_score
        
        # Partnership Network Score
        partnership_score = self._calculate_partnership_score(project_data)
        scores[CompetitiveMetric.PARTNERSHIP_NETWORK.value] = partnership_score
        
        # Tokenomics Health Score
        tokenomics_score = self._calculate_tokenomics_score(project_data)
        scores[CompetitiveMetric.TOKENOMICS_HEALTH.value] = tokenomics_score
        
        # Development Activity Score
        development_score = self._calculate_development_activity_score(project_data)
        scores[CompetitiveMetric.DEVELOPMENT_ACTIVITY.value] = development_score
        
        return scores
    
    def _calculate_market_share_score(self, project_data: Dict[str, Any]) -> float:
        """Calculate market share competitive score"""
        
        # Inverse rank scoring (rank 1 = highest score)
        rank_score = (6 - project_data['market_cap_rank']) / 5
        
        # User base relative scoring
        max_users = max(p['user_base'] for p in self.gaming_projects.values())
        user_score = project_data['user_base'] / max_users
        
        # Combined score
        return (rank_score * 0.6 + user_score * 0.4)
    
    def _calculate_user_adoption_score(self, project_data: Dict[str, Any]) -> float:
        """Calculate user adoption competitive score"""
        
        # Daily active users scoring
        max_dau = max(p['daily_active_users'] for p in self.gaming_projects.values())
        dau_score = project_data['daily_active_users'] / max_dau
        
        # User retention estimate (DAU/Total Users ratio)
        retention_ratio = project_data['daily_active_users'] / project_data['user_base']
        max_retention = max(
            p['daily_active_users'] / p['user_base'] 
            for p in self.gaming_projects.values()
        )
        retention_score = retention_ratio / max_retention
        
        return (dau_score * 0.7 + retention_score * 0.3)
    
    def _calculate_innovation_score(self, project_data: Dict[str, Any]) -> float:
        """Calculate innovation competitive score"""
        
        # Feature uniqueness scoring
        unique_features = {
            'breeding': 0.8,
            'scholarships': 0.9,
            'space_exploration': 0.85,
            'dao_governance': 0.7,
            'defi_integration': 0.75,
            'voxel_creation': 0.8,
            'game_maker': 0.85,
            'node_network': 0.9,
            'multiple_games': 0.7
        }
        
        feature_score = 0
        for feature in project_data['key_features']:
            feature_score += unique_features.get(feature, 0.5)
        
        # Normalize by number of features
        normalized_feature_score = feature_score / len(project_data['key_features'])
        
        # Blockchain innovation bonus
        blockchain_bonus = {
            'ronin': 0.9,  # Custom sidechain
            'solana': 0.8,  # High performance
            'ethereum': 0.6  # Established but slower
        }
        
        blockchain_score = blockchain_bonus.get(project_data['blockchain'], 0.5)
        
        return (normalized_feature_score * 0.7 + blockchain_score * 0.3)
    
    def _calculate_community_strength_score(self, project_data: Dict[str, Any]) -> float:
        """Calculate community strength competitive score"""
        
        # User engagement (DAU/Total ratio)
        engagement_ratio = project_data['daily_active_users'] / project_data['user_base']
        
        # Partnership quality scoring
        high_value_partners = ['Samsung', 'Ubisoft', 'Epic Games', 'Adidas', 'Snoop Dogg']
        partnership_quality = sum(
            1 for partner in project_data['partnerships'] 
            if partner in high_value_partners
        ) / len(project_data['partnerships'])
        
        # Community size relative scoring
        max_users = max(p['user_base'] for p in self.gaming_projects.values())
        size_score = project_data['user_base'] / max_users
        
        return (engagement_ratio * 0.4 + partnership_quality * 0.3 + size_score * 0.3)
    
    def _calculate_technical_advancement_score(self, project_data: Dict[str, Any]) -> float:
        """Calculate technical advancement competitive score"""
        
        # Blockchain technology scoring
        blockchain_scores = {
            'ronin': 0.9,    # Custom optimized chain
            'solana': 0.85,  # High performance
            'ethereum': 0.7  # Established but limitations
        }
        
        blockchain_score = blockchain_scores.get(project_data['blockchain'], 0.5)
        
        # Development team size scoring
        max_team_size = max(p['development_team_size'] for p in self.gaming_projects.values())
        team_score = project_data['development_team_size'] / max_team_size
        
        # Feature complexity scoring
        complex_features = ['dao_governance', 'defi_integration', 'space_exploration', 'node_network']
        complexity_score = sum(
            1 for feature in project_data['key_features'] 
            if feature in complex_features
        ) / len(complex_features)
        
        return (blockchain_score * 0.4 + team_score * 0.3 + complexity_score * 0.3)
    
    def _calculate_partnership_score(self, project_data: Dict[str, Any]) -> float:
        """Calculate partnership network competitive score"""
        
        # Partnership quantity
        partnership_count = len(project_data['partnerships'])
        max_partnerships = max(len(p['partnerships']) for p in self.gaming_projects.values())
        quantity_score = partnership_count / max_partnerships
        
        # Partnership quality (based on partner reputation)
        tier_1_partners = ['Samsung', 'Ubisoft', 'Epic Games', 'Binance', 'FTX']
        tier_2_partners = ['Adidas', 'Atari', 'Polygon', 'Solana Labs', 'Brave']
        
        quality_score = 0
        for partner in project_data['partnerships']:
            if partner in tier_1_partners:
                quality_score += 1.0
            elif partner in tier_2_partners:
                quality_score += 0.7
            else:
                quality_score += 0.4
        
        normalized_quality = quality_score / partnership_count if partnership_count > 0 else 0
        
        return (quantity_score * 0.3 + normalized_quality * 0.7)
    
    def _calculate_tokenomics_score(self, project_data: Dict[str, Any]) -> float:
        """Calculate tokenomics health competitive score"""
        
        # Price stability (inverse volatility proxy)
        # Higher prices generally indicate better tokenomics (simplified)
        max_price = max(p['token_price'] for p in self.gaming_projects.values())
        price_score = min(1.0, project_data['token_price'] / max_price)
        
        # Market cap rank scoring
        rank_score = (6 - project_data['market_cap_rank']) / 5
        
        # Utility scoring based on features
        utility_features = ['governance', 'staking', 'marketplace', 'breeding']
        utility_score = sum(
            1 for feature in project_data['key_features'] 
            if any(util in feature for util in utility_features)
        ) / len(utility_features)
        
        return (price_score * 0.4 + rank_score * 0.4 + utility_score * 0.2)
    
    def _calculate_development_activity_score(self, project_data: Dict[str, Any]) -> float:
        """Calculate development activity competitive score"""
        
        # Team size scoring
        max_team_size = max(p['development_team_size'] for p in self.gaming_projects.values())
        team_score = project_data['development_team_size'] / max_team_size
        
        # Project maturity (time since launch)
        launch_date = datetime.strptime(project_data['launch_date'], '%Y-%m-%d')
        project_age = (datetime.now() - launch_date).days
        
        # Optimal age is around 2-3 years (mature but not stagnant)
        optimal_age = 365 * 2.5
        age_score = 1 - abs(project_age - optimal_age) / optimal_age
        age_score = max(0, min(1, age_score))
        
        # Feature count as proxy for development activity
        feature_count = len(project_data['key_features'])
        max_features = max(len(p['key_features']) for p in self.gaming_projects.values())
        feature_score = feature_count / max_features
        
        return (team_score * 0.5 + age_score * 0.2 + feature_score * 0.3)
    
    def _rank_projects(self, competitive_scores: Dict[str, Dict[str, float]]) -> List[Tuple[str, float]]:
        """Rank projects based on weighted competitive scores"""
        
        overall_scores = {}
        
        for project_id, scores in competitive_scores.items():
            weighted_score = 0
            for metric, weight in self.competitive_weights.items():
                weighted_score += scores.get(metric.value, 0) * weight
            
            overall_scores[project_id] = weighted_score
        
        # Sort by score (descending)
        ranked_projects = sorted(
            overall_scores.items(), 
            key=lambda x: x[1], 
            reverse=True
        )
        
        return ranked_projects
    
    def _analyze_market_dynamics(self, competitive_scores: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """Analyze market dynamics and competitive positioning"""
        
        # Identify market leaders (top 2)
        rankings = self._rank_projects(competitive_scores)
        market_leaders = [self.gaming_projects[proj_id]['name'] for proj_id, _ in rankings[:2]]
        
        # Identify challengers (ranks 3-4)
        challengers = [self.gaming_projects[proj_id]['name'] for proj_id, _ in rankings[2:4]]
        
        # Identify niche players (rank 5+)
        niche_players = [self.gaming_projects[proj_id]['name'] for proj_id, _ in rankings[4:]]
        
        # Category analysis
        category_leaders = {}
        for project_id, project_data in self.gaming_projects.items():
            category = project_data['category']
            if category not in category_leaders:
                category_leaders[category] = []
            
            overall_score = sum(
                competitive_scores[project_id].get(metric.value, 0) * weight
                for metric, weight in self.competitive_weights.items()
            )
            category_leaders[category].append((project_data['name'], overall_score))
        
        # Sort category leaders
        for category in category_leaders:
            category_leaders[category].sort(key=lambda x: x[1], reverse=True)
            category_leaders[category] = category_leaders[category][0][0]  # Keep only the leader
        
        return {
            'market_leaders': market_leaders,
            'challengers': challengers,
            'niche_players': niche_players,
            'category_leaders': category_leaders,
            'market_concentration': self._calculate_market_concentration(rankings),
            'competitive_intensity': self._calculate_competitive_intensity(competitive_scores)
        }
    
    def _calculate_market_concentration(self, rankings: List[Tuple[str, float]]) -> float:
        """Calculate market concentration (HHI-like metric)"""
        
        total_score = sum(score for _, score in rankings)
        if total_score == 0:
            return 0
        
        concentration = sum((score / total_score) ** 2 for _, score in rankings)
        return concentration
    
    def _calculate_competitive_intensity(self, competitive_scores: Dict[str, Dict[str, float]]) -> float:
        """Calculate competitive intensity based on score variance"""
        
        overall_scores = []
        for project_id, scores in competitive_scores.items():
            weighted_score = sum(
                scores.get(metric.value, 0) * weight
                for metric, weight in self.competitive_weights.items()
            )
            overall_scores.append(weighted_score)
        
        if len(overall_scores) < 2:
            return 0
        
        if ANALYTICS_AVAILABLE:
            # Lower variance = higher competitive intensity
            variance = np.var(overall_scores)
            intensity = 1 / (1 + variance)  # Inverse relationship
        else:
            # Simple range-based intensity
            score_range = max(overall_scores) - min(overall_scores)
            intensity = 1 / (1 + score_range)
        
        return intensity
    
    def _generate_competitive_insights(self, competitive_scores: Dict[str, Dict[str, float]], 
                                     rankings: List[Tuple[str, float]]) -> List[str]:
        """Generate competitive insights and recommendations"""
        
        insights = []
        
        # Market leader insights
        leader_id, leader_score = rankings[0]
        leader_name = self.gaming_projects[leader_id]['name']
        insights.append(f"{leader_name} leads the market with a competitive score of {leader_score:.2f}")
        
        # Identify strongest competitive advantages
        leader_scores = competitive_scores[leader_id]
        strongest_metric = max(leader_scores.items(), key=lambda x: x[1])
        insights.append(f"{leader_name}'s strongest advantage is {strongest_metric[0].replace('_', ' ')}")
        
        # Identify closest competitor
        if len(rankings) > 1:
            challenger_id, challenger_score = rankings[1]
            challenger_name = self.gaming_projects[challenger_id]['name']
            gap = leader_score - challenger_score
            insights.append(f"{challenger_name} is the closest competitor with a gap of {gap:.2f}")
        
        # Category insights
        category_performance = {}
        for project_id, scores in competitive_scores.items():
            category = self.gaming_projects[project_id]['category']
            overall_score = sum(
                scores.get(metric.value, 0) * weight
                for metric, weight in self.competitive_weights.items()
            )
            
            if category not in category_performance:
                category_performance[category] = []
            category_performance[category].append(overall_score)
        
        # Find strongest category
        avg_category_scores = {
            cat: sum(scores) / len(scores) 
            for cat, scores in category_performance.items()
        }
        strongest_category = max(avg_category_scores.items(), key=lambda x: x[1])
        insights.append(f"The {strongest_category[0]} category shows the strongest competitive performance")
        
        return insights
    
    def _get_fallback_competitive_data(self) -> Dict[str, Any]:
        """Fallback competitive data when analysis fails"""
        
        return {
            'competitive_scores': {},
            'rankings': [('axie-infinity', 0.8), ('star-atlas', 0.7), ('decentraland', 0.6)],
            'market_dynamics': {
                'market_leaders': ['Axie Infinity', 'Star Atlas'],
                'challengers': ['Decentraland', 'The Sandbox'],
                'niche_players': ['Gala Games'],
                'category_leaders': {'p2e': 'Axie Infinity', 'metaverse': 'Star Atlas'},
                'market_concentration': 0.3,
                'competitive_intensity': 0.7
            },
            'competitive_insights': [
                'Competitive analysis unavailable - using fallback data',
                'Axie Infinity maintains market leadership',
                'Metaverse category shows strong growth potential'
            ],
            'analysis_timestamp': datetime.utcnow()
        }


# Global instance
competitive_analysis_engine = CompetitiveAnalysisEngine()
