"""
SQL Query Optimization Service
Replaces Python-heavy operations with efficient SQL joins and aggregations
"""
import logging
import time
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy import text, func, and_, or_, desc, asc
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy.sql import select

from models.gaming import (
    Article, Source, GamingProject, NFTCollection, BlockchainData,
    TwitterPost, RedditPost, SocialMediaFilter
)
from models.base import get_db

logger = logging.getLogger(__name__)


class SQLOptimizer:
    """Optimized SQL queries to replace inefficient Python operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_dashboard_overview_optimized(self, hours: int = 24) -> Dict[str, Any]:
        """Single optimized query for dashboard overview instead of multiple separate queries"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            # Single comprehensive query with all counts and aggregations
            overview_query = text("""
                WITH overview_stats AS (
                    SELECT
                        (SELECT COUNT(*) FROM articles) as total_articles,
                        (SELECT COUNT(*) FROM sources WHERE is_active = true) as total_sources,
                        (SELECT COUNT(*) FROM gaming_projects WHERE is_active = true) as total_gaming_projects,
                        (SELECT COUNT(*) FROM nft_collections WHERE is_active = true) as total_nft_collections,
                        (SELECT COUNT(*) FROM articles WHERE published_at >= :since) as articles_recent,
                        (SELECT COUNT(DISTINCT blockchain_network) FROM articles WHERE blockchain_network IS NOT NULL) as active_networks,
                        (SELECT AVG(sentiment_score) FROM articles WHERE published_at >= :since AND sentiment_score IS NOT NULL) as avg_sentiment
                ),
                top_categories AS (
                    SELECT 
                        gaming_category,
                        COUNT(*) as category_count
                    FROM articles 
                    WHERE published_at >= :since 
                        AND gaming_category IS NOT NULL
                    GROUP BY gaming_category
                    ORDER BY COUNT(*) DESC
                    LIMIT 5
                ),
                network_activity AS (
                    SELECT 
                        blockchain_network,
                        COUNT(*) as network_articles,
                        AVG(sentiment_score) as network_sentiment
                    FROM articles 
                    WHERE published_at >= :since 
                        AND blockchain_network IS NOT NULL
                    GROUP BY blockchain_network
                    ORDER BY COUNT(*) DESC
                )
                SELECT 
                    os.*,
                    COALESCE(
                        json_agg(
                            json_build_object(
                                'category', tc.gaming_category,
                                'count', tc.category_count
                            )
                        ) FILTER (WHERE tc.gaming_category IS NOT NULL),
                        '[]'::json
                    ) as top_categories,
                    COALESCE(
                        json_agg(
                            json_build_object(
                                'network', na.blockchain_network,
                                'articles', na.network_articles,
                                'sentiment', na.network_sentiment
                            )
                        ) FILTER (WHERE na.blockchain_network IS NOT NULL),
                        '[]'::json
                    ) as network_activity
                FROM overview_stats os
                LEFT JOIN top_categories tc ON true
                LEFT JOIN network_activity na ON true
                GROUP BY os.total_articles, os.total_sources, os.total_gaming_projects, 
                         os.total_nft_collections, os.articles_recent, os.active_networks, os.avg_sentiment
            """)
            
            result = self.db.execute(overview_query, {"since": since}).fetchone()
            
            return {
                "total_articles": result.total_articles,
                "total_sources": result.total_sources,
                "total_gaming_projects": result.total_gaming_projects,
                "total_nft_collections": result.total_nft_collections,
                "articles_last_24h": result.articles_recent,
                "active_blockchain_networks": result.active_networks,
                "average_sentiment": float(result.avg_sentiment) if result.avg_sentiment else 0.0,
                "top_gaming_categories": result.top_categories,
                "network_activity": result.network_activity
            }
            
        except Exception as e:
            logger.error(f"Error in optimized dashboard overview query: {e}")
            raise
    
    def get_gaming_projects_with_metrics_optimized(self, 
                                                  blockchain: Optional[str] = None,
                                                  category: Optional[str] = None,
                                                  hours: int = 24) -> List[Dict[str, Any]]:
        """Optimized query for gaming projects with article metrics using joins"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            # Build dynamic WHERE conditions
            where_conditions = ["gp.is_active = true"]
            params = {"since": since}
            
            if blockchain:
                where_conditions.append("gp.blockchain_network = :blockchain")
                params["blockchain"] = blockchain
            
            if category:
                where_conditions.append("gp.category = :category")
                params["category"] = category
            
            where_clause = " AND ".join(where_conditions)
            
            # Single optimized query with LEFT JOINs for all metrics
            projects_query = text(f"""
                SELECT 
                    gp.id,
                    gp.project_name,
                    gp.name,
                    gp.blockchain_network,
                    gp.category,
                    gp.token_symbol,
                    gp.token_address,
                    gp.is_active,
                    gp.daily_active_users,
                    gp.monthly_active_users,
                    gp.total_value_locked,
                    
                    -- Article metrics
                    COUNT(DISTINCT a.id) as total_articles,
                    COUNT(DISTINCT CASE WHEN a.published_at >= :since THEN a.id END) as recent_articles,
                    AVG(a.sentiment_score) as avg_sentiment,
                    MAX(a.published_at) as latest_article_date,
                    
                    -- NFT metrics
                    COUNT(DISTINCT nft.id) as nft_collections_count,
                    AVG(nft.floor_price) as avg_floor_price,
                    SUM(nft.volume_24h) as total_nft_volume_24h,
                    
                    -- Social media metrics
                    COUNT(DISTINCT tp.id) as twitter_mentions,
                    COUNT(DISTINCT rp.id) as reddit_mentions,
                    AVG(tp.engagement_score) as avg_twitter_engagement,
                    AVG(rp.score) as avg_reddit_score
                    
                FROM gaming_projects gp
                LEFT JOIN articles a ON (
                    a.gaming_projects ? gp.project_name 
                    OR a.gaming_projects ? gp.name
                    OR LOWER(a.title) LIKE LOWER('%' || gp.project_name || '%')
                    OR LOWER(a.content) LIKE LOWER('%' || gp.project_name || '%')
                )
                LEFT JOIN nft_collections nft ON (
                    nft.gaming_project_id = gp.id
                    OR nft.project_name = gp.project_name
                )
                LEFT JOIN twitter_posts tp ON (
                    tp.gaming_projects ? gp.project_name
                    OR tp.gaming_projects ? gp.name
                    AND tp.created_at >= :since
                )
                LEFT JOIN reddit_posts rp ON (
                    rp.gaming_projects ? gp.project_name
                    OR rp.gaming_projects ? gp.name
                    AND rp.created_utc >= :since
                )
                WHERE {where_clause}
                GROUP BY gp.id, gp.project_name, gp.name, gp.blockchain_network, 
                         gp.category, gp.token_symbol, gp.token_address, gp.is_active,
                         gp.daily_active_users, gp.monthly_active_users, gp.total_value_locked
                ORDER BY recent_articles DESC, total_articles DESC, gp.daily_active_users DESC NULLS LAST
            """)
            
            results = self.db.execute(projects_query, params).fetchall()
            
            return [
                {
                    "id": row.id,
                    "project_name": row.project_name,
                    "name": row.name,
                    "blockchain_network": row.blockchain_network,
                    "category": row.category,
                    "token_symbol": row.token_symbol,
                    "token_address": row.token_address,
                    "is_active": row.is_active,
                    "daily_active_users": row.daily_active_users,
                    "monthly_active_users": row.monthly_active_users,
                    "total_value_locked": float(row.total_value_locked) if row.total_value_locked else 0.0,
                    "metrics": {
                        "total_articles": row.total_articles,
                        "recent_articles": row.recent_articles,
                        "avg_sentiment": float(row.avg_sentiment) if row.avg_sentiment else 0.0,
                        "latest_article_date": row.latest_article_date.isoformat() if row.latest_article_date else None,
                        "nft_collections_count": row.nft_collections_count,
                        "avg_floor_price": float(row.avg_floor_price) if row.avg_floor_price else 0.0,
                        "total_nft_volume_24h": float(row.total_nft_volume_24h) if row.total_nft_volume_24h else 0.0,
                        "twitter_mentions": row.twitter_mentions,
                        "reddit_mentions": row.reddit_mentions,
                        "avg_twitter_engagement": float(row.avg_twitter_engagement) if row.avg_twitter_engagement else 0.0,
                        "avg_reddit_score": float(row.avg_reddit_score) if row.avg_reddit_score else 0.0
                    }
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"Error in optimized gaming projects query: {e}")
            raise
    
    def get_social_media_with_gaming_context_optimized(self, 
                                                      platform: str = "both",
                                                      hours: int = 24,
                                                      min_engagement: int = 0,
                                                      gaming_only: bool = True) -> Dict[str, Any]:
        """Optimized social media query with gaming project context using joins"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            results = {}
            
            if platform in ["twitter", "both"]:
                # Optimized Twitter query with gaming project joins
                twitter_query = text("""
                    SELECT 
                        tp.id,
                        tp.twitter_id,
                        tp.text,
                        tp.author_username,
                        tp.author_name,
                        tp.created_at,
                        tp.url,
                        tp.like_count,
                        tp.retweet_count,
                        tp.reply_count,
                        tp.gaming_projects,
                        tp.gaming_influencers,
                        tp.hashtags,
                        tp.sentiment_score,
                        tp.relevance_score,
                        tp.engagement_score,
                        
                        -- Gaming project details via join
                        COALESCE(
                            json_agg(
                                DISTINCT json_build_object(
                                    'project_name', gp.project_name,
                                    'name', gp.name,
                                    'blockchain_network', gp.blockchain_network,
                                    'category', gp.category,
                                    'token_symbol', gp.token_symbol
                                )
                            ) FILTER (WHERE gp.id IS NOT NULL),
                            '[]'::json
                        ) as project_details
                        
                    FROM twitter_posts tp
                    LEFT JOIN gaming_projects gp ON (
                        tp.gaming_projects ? gp.project_name
                        OR tp.gaming_projects ? gp.name
                    )
                    WHERE tp.created_at >= :since
                        AND (:gaming_only = false OR tp.is_gaming_related = true)
                        AND (tp.like_count + tp.retweet_count) >= :min_engagement
                    GROUP BY tp.id, tp.twitter_id, tp.text, tp.author_username, tp.author_name,
                             tp.created_at, tp.url, tp.like_count, tp.retweet_count, tp.reply_count,
                             tp.gaming_projects, tp.gaming_influencers, tp.hashtags,
                             tp.sentiment_score, tp.relevance_score, tp.engagement_score
                    ORDER BY tp.created_at DESC, tp.engagement_score DESC
                    LIMIT 100
                """)
                
                twitter_results = self.db.execute(twitter_query, {
                    "since": since,
                    "gaming_only": gaming_only,
                    "min_engagement": min_engagement
                }).fetchall()
                
                results["twitter"] = [
                    {
                        "id": row.twitter_id,
                        "text": row.text,
                        "author_username": row.author_username,
                        "author_name": row.author_name,
                        "created_at": row.created_at.isoformat(),
                        "url": row.url,
                        "engagement": {
                            "likes": row.like_count,
                            "retweets": row.retweet_count,
                            "replies": row.reply_count,
                            "total_score": row.engagement_score
                        },
                        "gaming_projects": row.gaming_projects or [],
                        "gaming_influencers": row.gaming_influencers or [],
                        "hashtags": row.hashtags or [],
                        "sentiment_score": row.sentiment_score,
                        "relevance_score": row.relevance_score,
                        "project_details": row.project_details
                    }
                    for row in twitter_results
                ]
            
            if platform in ["reddit", "both"]:
                # Similar optimized Reddit query
                reddit_query = text("""
                    SELECT 
                        rp.id,
                        rp.reddit_id,
                        rp.title,
                        rp.selftext,
                        rp.author,
                        rp.created_utc,
                        rp.url,
                        rp.score,
                        rp.num_comments,
                        rp.upvote_ratio,
                        rp.subreddit,
                        rp.gaming_projects,
                        rp.gaming_keywords,
                        rp.sentiment_score,
                        rp.relevance_score,
                        
                        -- Gaming project details via join
                        COALESCE(
                            json_agg(
                                DISTINCT json_build_object(
                                    'project_name', gp.project_name,
                                    'name', gp.name,
                                    'blockchain_network', gp.blockchain_network,
                                    'category', gp.category,
                                    'token_symbol', gp.token_symbol
                                )
                            ) FILTER (WHERE gp.id IS NOT NULL),
                            '[]'::json
                        ) as project_details
                        
                    FROM reddit_posts rp
                    LEFT JOIN gaming_projects gp ON (
                        rp.gaming_projects ? gp.project_name
                        OR rp.gaming_projects ? gp.name
                    )
                    WHERE rp.created_utc >= :since
                        AND (:gaming_only = false OR rp.is_gaming_related = true)
                        AND rp.score >= :min_engagement
                        AND rp.meets_quality_threshold = true
                    GROUP BY rp.id, rp.reddit_id, rp.title, rp.selftext, rp.author,
                             rp.created_utc, rp.url, rp.score, rp.num_comments, rp.upvote_ratio,
                             rp.subreddit, rp.gaming_projects, rp.gaming_keywords,
                             rp.sentiment_score, rp.relevance_score
                    ORDER BY rp.created_utc DESC, rp.score DESC
                    LIMIT 100
                """)
                
                reddit_results = self.db.execute(reddit_query, {
                    "since": since,
                    "gaming_only": gaming_only,
                    "min_engagement": min_engagement
                }).fetchall()
                
                results["reddit"] = [
                    {
                        "id": row.reddit_id,
                        "title": row.title,
                        "selftext": row.selftext,
                        "author": row.author,
                        "created_utc": row.created_utc.isoformat(),
                        "url": row.url,
                        "score": row.score,
                        "num_comments": row.num_comments,
                        "upvote_ratio": row.upvote_ratio,
                        "subreddit": row.subreddit,
                        "gaming_projects": row.gaming_projects or [],
                        "gaming_keywords": row.gaming_keywords or [],
                        "sentiment_score": row.sentiment_score,
                        "relevance_score": row.relevance_score,
                        "project_details": row.project_details
                    }
                    for row in reddit_results
                ]
            
            return results
            
        except Exception as e:
            logger.error(f"Error in optimized social media query: {e}")
            raise
    
    def get_blockchain_activity_with_projects_optimized(self, 
                                                       hours: int = 24,
                                                       networks: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Optimized blockchain activity query with gaming project context"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            # Build network filter
            network_filter = ""
            params = {"since": since}
            
            if networks:
                network_filter = "AND bd.blockchain_network = ANY(:networks)"
                params["networks"] = networks
            
            # Optimized query with gaming project joins
            activity_query = text(f"""
                SELECT 
                    bd.id,
                    bd.blockchain_network,
                    bd.contract_address,
                    bd.event_type,
                    bd.event_name,
                    bd.block_number,
                    bd.transaction_hash,
                    bd.block_timestamp,
                    bd.player_address,
                    bd.token_id,
                    bd.amount,
                    bd.game_action,
                    bd.gas_used,
                    bd.decoded_data,
                    
                    -- Gaming project details
                    gp.project_name,
                    gp.name as project_display_name,
                    gp.category as project_category,
                    gp.token_symbol,
                    
                    -- NFT collection details
                    nft.collection_name,
                    nft.floor_price,
                    nft.gaming_category as nft_category
                    
                FROM blockchain_data bd
                LEFT JOIN gaming_projects gp ON (
                    bd.contract_address = gp.token_address
                    OR bd.contract_address = ANY(
                        SELECT nft_inner.contract_address 
                        FROM nft_collections nft_inner 
                        WHERE nft_inner.gaming_project_id = gp.id
                    )
                )
                LEFT JOIN nft_collections nft ON (
                    bd.contract_address = nft.contract_address
                )
                WHERE bd.block_timestamp >= :since
                    {network_filter}
                ORDER BY bd.block_timestamp DESC, bd.block_number DESC
                LIMIT 500
            """)
            
            results = self.db.execute(activity_query, params).fetchall()
            
            return [
                {
                    "id": row.id,
                    "blockchain_network": row.blockchain_network,
                    "contract_address": row.contract_address,
                    "event_type": row.event_type,
                    "event_name": row.event_name,
                    "block_number": row.block_number,
                    "transaction_hash": row.transaction_hash,
                    "block_timestamp": row.block_timestamp.isoformat(),
                    "player_address": row.player_address,
                    "token_id": row.token_id,
                    "amount": row.amount,
                    "game_action": row.game_action,
                    "gas_used": row.gas_used,
                    "decoded_data": row.decoded_data,
                    "gaming_context": {
                        "project_name": row.project_name,
                        "project_display_name": row.project_display_name,
                        "project_category": row.project_category,
                        "token_symbol": row.token_symbol,
                        "nft_collection_name": row.collection_name,
                        "nft_floor_price": float(row.floor_price) if row.floor_price else None,
                        "nft_category": row.nft_category
                    }
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"Error in optimized blockchain activity query: {e}")
            raise

    def get_news_analytics_optimized(self, hours: int = 24, network: Optional[str] = None) -> Dict[str, Any]:
        """Optimized news analytics query with comprehensive metrics"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)

            # Build dynamic WHERE conditions
            where_conditions = ["a.published_at >= :since"]
            params = {"since": since}

            if network:
                where_conditions.append("bd.blockchain = :network")
                params["network"] = network

            where_clause = " AND ".join(where_conditions)

            # Add network join if needed
            network_join = ""
            if network:
                network_join = "INNER JOIN blockchain_data bd ON a.id = bd.article_id"

            # Single comprehensive query for all news analytics
            news_analytics_query = text(f"""
                WITH hourly_articles AS (
                    SELECT
                        date_trunc('hour', a.published_at) as hour,
                        COUNT(*) as article_count,
                        AVG(a.sentiment_score) as avg_sentiment,
                        COUNT(DISTINCT a.source_id) as unique_sources
                    FROM articles a
                    {network_join}
                    WHERE {where_clause}
                    GROUP BY date_trunc('hour', a.published_at)
                ),
                source_metrics AS (
                    SELECT
                        s.name as source_name,
                        s.url as source_url,
                        COUNT(a.id) as article_count,
                        AVG(a.sentiment_score) as avg_sentiment,
                        MAX(a.published_at) as latest_article
                    FROM articles a
                    INNER JOIN sources s ON a.source_id = s.id
                    {network_join}
                    WHERE {where_clause}
                    GROUP BY s.id, s.name, s.url
                    ORDER BY COUNT(a.id) DESC
                    LIMIT 10
                ),
                network_metrics AS (
                    SELECT
                        bd.blockchain,
                        COUNT(DISTINCT a.id) as article_count,
                        AVG(a.sentiment_score) as avg_sentiment,
                        COUNT(DISTINCT CASE WHEN a.gaming_projects IS NOT NULL THEN 1 END) as gaming_projects_mentioned
                    FROM articles a
                    INNER JOIN blockchain_data bd ON a.id = bd.article_id
                    WHERE {where_clause} AND bd.blockchain IS NOT NULL
                    GROUP BY bd.blockchain
                    ORDER BY COUNT(DISTINCT a.id) DESC
                ),
                gaming_category_metrics AS (
                    SELECT
                        a.gaming_category,
                        COUNT(*) as article_count,
                        AVG(a.sentiment_score) as avg_sentiment
                    FROM articles a
                    {network_join}
                    WHERE {where_clause} AND a.gaming_category IS NOT NULL
                    GROUP BY a.gaming_category
                    ORDER BY COUNT(*) DESC
                    LIMIT 10
                )
                SELECT
                    'hourly_data' as metric_type,
                    COALESCE(
                        json_agg(
                            json_build_object(
                                'hour', ha.hour,
                                'article_count', ha.article_count,
                                'avg_sentiment', ha.avg_sentiment,
                                'unique_sources', ha.unique_sources
                            )
                            ORDER BY ha.hour
                        ) FILTER (WHERE ha.hour IS NOT NULL),
                        '[]'::json
                    ) as data
                FROM hourly_articles ha

                UNION ALL

                SELECT
                    'source_metrics' as metric_type,
                    COALESCE(
                        json_agg(
                            json_build_object(
                                'source_name', sm.source_name,
                                'source_url', sm.source_url,
                                'article_count', sm.article_count,
                                'avg_sentiment', sm.avg_sentiment,
                                'latest_article', sm.latest_article
                            )
                            ORDER BY sm.article_count DESC
                        ) FILTER (WHERE sm.source_name IS NOT NULL),
                        '[]'::json
                    ) as data
                FROM source_metrics sm

                UNION ALL

                SELECT
                    'network_metrics' as metric_type,
                    COALESCE(
                        json_agg(
                            json_build_object(
                                'blockchain', nm.blockchain,
                                'article_count', nm.article_count,
                                'avg_sentiment', nm.avg_sentiment,
                                'gaming_projects_mentioned', nm.gaming_projects_mentioned
                            )
                            ORDER BY nm.article_count DESC
                        ) FILTER (WHERE nm.blockchain IS NOT NULL),
                        '[]'::json
                    ) as data
                FROM network_metrics nm

                UNION ALL

                SELECT
                    'gaming_category_metrics' as metric_type,
                    COALESCE(
                        json_agg(
                            json_build_object(
                                'gaming_category', gcm.gaming_category,
                                'article_count', gcm.article_count,
                                'avg_sentiment', gcm.avg_sentiment
                            )
                            ORDER BY gcm.article_count DESC
                        ) FILTER (WHERE gcm.gaming_category IS NOT NULL),
                        '[]'::json
                    ) as data
                FROM gaming_category_metrics gcm
            """)

            results = self.db.execute(news_analytics_query, params).fetchall()

            # Process results
            analytics_data = {}
            for row in results:
                analytics_data[row.metric_type] = row.data

            return {
                "hourly_data": analytics_data.get("hourly_data", []),
                "source_metrics": analytics_data.get("source_metrics", []),
                "network_metrics": analytics_data.get("network_metrics", []),
                "gaming_category_metrics": analytics_data.get("gaming_category_metrics", []),
                "analysis_period_hours": hours,
                "network_filter": network,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in optimized news analytics query: {e}")
            raise

    def get_cross_chain_comparison_optimized(self, days: int = 7) -> Dict[str, Any]:
        """Optimized cross-chain comparison with comprehensive metrics"""
        try:
            since = datetime.utcnow() - timedelta(days=days)

            # Comprehensive cross-chain analysis query
            cross_chain_query = text("""
                WITH network_activity AS (
                    SELECT
                        bd.blockchain,
                        COUNT(DISTINCT a.id) as article_count,
                        COUNT(DISTINCT a.source_id) as unique_sources,
                        AVG(a.sentiment_score) as avg_sentiment,
                        COUNT(DISTINCT DATE(a.published_at)) as active_days,
                        COUNT(DISTINCT CASE WHEN a.gaming_projects IS NOT NULL THEN 1 END) as gaming_projects_mentioned
                    FROM articles a
                    INNER JOIN blockchain_data bd ON a.id = bd.article_id
                    WHERE a.published_at >= :since
                        AND bd.blockchain IS NOT NULL
                    GROUP BY bd.blockchain
                ),
                gaming_project_distribution AS (
                    SELECT
                        gp.blockchain,
                        COUNT(DISTINCT gp.id) as total_projects,
                        COUNT(DISTINCT CASE WHEN gp.is_active = true THEN gp.id END) as active_projects,
                        COUNT(DISTINCT gp.category) as unique_categories,
                        COALESCE(SUM(gp.daily_active_users), 0) as total_daily_users
                    FROM gaming_projects gp
                    GROUP BY gp.blockchain
                ),
                blockchain_activity AS (
                    SELECT
                        bd.blockchain,
                        COUNT(DISTINCT bd.id) as transaction_count,
                        COUNT(DISTINCT bd.from_address) as unique_players,
                        COUNT(DISTINCT bd.contract_address) as unique_contracts,
                        AVG(bd.gas_used) as avg_gas_used,
                        COUNT(DISTINCT DATE(bd.block_timestamp)) as active_days
                    FROM blockchain_data bd
                    WHERE bd.block_timestamp >= :since
                    GROUP BY bd.blockchain
                ),
                nft_activity AS (
                    SELECT
                        nft.blockchain,
                        COUNT(DISTINCT nft.id) as collection_count,
                        AVG(nft.floor_price) as avg_floor_price,
                        SUM(nft.volume_24h) as total_volume_24h,
                        COUNT(DISTINCT nft.gaming_project_id) as gaming_projects_with_nfts
                    FROM nft_collections nft
                    WHERE nft.is_active = true
                    GROUP BY nft.blockchain
                )
                SELECT
                    COALESCE(na.blockchain, gpd.blockchain, ba.blockchain, nfta.blockchain) as network,

                    -- Article metrics
                    COALESCE(na.article_count, 0) as article_count,
                    COALESCE(na.unique_sources, 0) as unique_sources,
                    COALESCE(na.avg_sentiment, 0) as avg_sentiment,
                    COALESCE(na.active_days, 0) as article_active_days,
                    COALESCE(na.gaming_projects_mentioned, 0) as gaming_projects_mentioned,

                    -- Gaming project metrics
                    COALESCE(gpd.total_projects, 0) as total_projects,
                    COALESCE(gpd.active_projects, 0) as active_projects,
                    COALESCE(gpd.unique_categories, 0) as unique_categories,
                    COALESCE(gpd.total_daily_users, 0) as total_daily_users,

                    -- Blockchain activity metrics
                    COALESCE(ba.transaction_count, 0) as transaction_count,
                    COALESCE(ba.unique_players, 0) as unique_players,
                    COALESCE(ba.unique_contracts, 0) as unique_contracts,
                    COALESCE(ba.avg_gas_used, 0) as avg_gas_used,
                    COALESCE(ba.active_days, 0) as blockchain_active_days,

                    -- NFT metrics
                    COALESCE(nfta.collection_count, 0) as nft_collection_count,
                    COALESCE(nfta.avg_floor_price, 0) as avg_nft_floor_price,
                    COALESCE(nfta.total_volume_24h, 0) as nft_volume_24h,
                    COALESCE(nfta.gaming_projects_with_nfts, 0) as gaming_projects_with_nfts

                FROM network_activity na
                FULL OUTER JOIN gaming_project_distribution gpd ON na.blockchain = gpd.blockchain
                FULL OUTER JOIN blockchain_activity ba ON COALESCE(na.blockchain, gpd.blockchain) = ba.blockchain
                FULL OUTER JOIN nft_activity nfta ON COALESCE(na.blockchain, gpd.blockchain, ba.blockchain) = nfta.blockchain
                WHERE COALESCE(na.blockchain, gpd.blockchain, ba.blockchain, nfta.blockchain) IS NOT NULL
                ORDER BY
                    (COALESCE(na.article_count, 0) + COALESCE(ba.transaction_count, 0) + COALESCE(gpd.active_projects, 0)) DESC
            """)

            results = self.db.execute(cross_chain_query, {"since": since}).fetchall()

            # Process results into structured format
            networks = []
            for row in results:
                network_data = {
                    "network": row.network,
                    "article_metrics": {
                        "article_count": row.article_count,
                        "unique_sources": row.unique_sources,
                        "avg_sentiment": float(row.avg_sentiment),
                        "active_days": row.article_active_days,
                        "gaming_projects_mentioned": row.gaming_projects_mentioned
                    },
                    "gaming_metrics": {
                        "total_projects": row.total_projects,
                        "active_projects": row.active_projects,
                        "unique_categories": row.unique_categories,
                        "total_daily_users": row.total_daily_users
                    },
                    "blockchain_metrics": {
                        "transaction_count": row.transaction_count,
                        "unique_players": row.unique_players,
                        "unique_contracts": row.unique_contracts,
                        "avg_gas_used": float(row.avg_gas_used) if row.avg_gas_used else 0,
                        "active_days": row.blockchain_active_days
                    },
                    "nft_metrics": {
                        "collection_count": row.nft_collection_count,
                        "avg_floor_price": float(row.avg_nft_floor_price),
                        "volume_24h": float(row.nft_volume_24h),
                        "gaming_projects_with_nfts": row.gaming_projects_with_nfts
                    }
                }
                networks.append(network_data)

            return {
                "networks": networks,
                "analysis_period_days": days,
                "total_networks": len(networks),
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in optimized cross-chain comparison query: {e}")
            raise

    def get_time_series_metrics_optimized(self,
                                        days: int = 30,
                                        interval: str = "day",
                                        networks: Optional[List[str]] = None) -> Dict[str, Any]:
        """Optimized time-series metrics with efficient aggregations"""
        try:
            since = datetime.utcnow() - timedelta(days=days)

            # Validate interval
            valid_intervals = ["hour", "day", "week"]
            if interval not in valid_intervals:
                interval = "day"

            # Build network filter
            network_filter = ""
            params = {"since": since}

            if networks:
                network_filter = "AND bd.blockchain = ANY(:networks)"
                params["networks"] = networks

            # Time-series aggregation query
            time_series_query = text(f"""
                WITH time_series AS (
                    SELECT
                        date_trunc('{interval}', a.published_at) as time_bucket,
                        bd.blockchain,
                        COUNT(DISTINCT a.id) as article_count,
                        COUNT(DISTINCT a.source_id) as unique_sources,
                        AVG(a.sentiment_score) as avg_sentiment,
                        COUNT(DISTINCT CASE WHEN a.gaming_projects IS NOT NULL THEN 1 END) as gaming_projects_mentioned,
                        COUNT(DISTINCT a.gaming_category) as unique_categories
                    FROM articles a
                    INNER JOIN blockchain_data bd ON a.id = bd.article_id
                    WHERE a.published_at >= :since {network_filter}
                    GROUP BY date_trunc('{interval}', a.published_at), bd.blockchain
                ),
                blockchain_time_series AS (
                    SELECT
                        date_trunc('{interval}', bd.block_timestamp) as time_bucket,
                        bd.blockchain,
                        COUNT(DISTINCT bd.id) as transaction_count,
                        COUNT(DISTINCT bd.from_address) as unique_players,
                        COUNT(DISTINCT bd.contract_address) as unique_contracts,
                        AVG(bd.gas_used) as avg_gas_used
                    FROM blockchain_data bd
                    WHERE bd.block_timestamp >= :since {network_filter}
                    GROUP BY date_trunc('{interval}', bd.block_timestamp), bd.blockchain
                ),
                gaming_project_time_series AS (
                    SELECT
                        date_trunc('{interval}', gp.created_at) as time_bucket,
                        gp.blockchain,
                        COUNT(DISTINCT gp.id) as new_projects,
                        COALESCE(SUM(gp.daily_active_users), 0) as total_daily_users
                    FROM gaming_projects gp
                    WHERE gp.created_at >= :since
                    GROUP BY date_trunc('{interval}', gp.created_at), gp.blockchain
                )
                SELECT
                    COALESCE(ts.time_bucket, bts.time_bucket, gpts.time_bucket) as time_bucket,
                    COALESCE(ts.blockchain, bts.blockchain, gpts.blockchain) as blockchain_network,

                    -- Article metrics
                    COALESCE(ts.article_count, 0) as article_count,
                    COALESCE(ts.unique_sources, 0) as unique_sources,
                    COALESCE(ts.avg_sentiment, 0) as avg_sentiment,
                    COALESCE(ts.gaming_projects_mentioned, 0) as gaming_projects_mentioned,
                    COALESCE(ts.unique_categories, 0) as unique_categories,

                    -- Blockchain metrics
                    COALESCE(bts.transaction_count, 0) as transaction_count,
                    COALESCE(bts.unique_players, 0) as unique_players,
                    COALESCE(bts.unique_contracts, 0) as unique_contracts,
                    COALESCE(bts.avg_gas_used, 0) as avg_gas_used,

                    -- Gaming project metrics
                    COALESCE(gpts.new_projects, 0) as new_projects,
                    COALESCE(gpts.total_daily_users, 0) as total_daily_users

                FROM time_series ts
                FULL OUTER JOIN blockchain_time_series bts ON ts.time_bucket = bts.time_bucket AND ts.blockchain = bts.blockchain
                FULL OUTER JOIN gaming_project_time_series gpts ON COALESCE(ts.time_bucket, bts.time_bucket) = gpts.time_bucket
                    AND COALESCE(ts.blockchain, bts.blockchain) = gpts.blockchain
                WHERE COALESCE(ts.time_bucket, bts.time_bucket, gpts.time_bucket) IS NOT NULL
                ORDER BY time_bucket DESC, blockchain_network
            """)

            results = self.db.execute(time_series_query, params).fetchall()

            # Process results into time-series format
            time_series_data = {}
            for row in results:
                time_key = row.time_bucket.isoformat()
                network = row.blockchain_network or "unknown"

                if time_key not in time_series_data:
                    time_series_data[time_key] = {}

                time_series_data[time_key][network] = {
                    "article_metrics": {
                        "article_count": row.article_count,
                        "unique_sources": row.unique_sources,
                        "avg_sentiment": float(row.avg_sentiment),
                        "gaming_projects_mentioned": row.gaming_projects_mentioned,
                        "unique_categories": row.unique_categories
                    },
                    "blockchain_metrics": {
                        "transaction_count": row.transaction_count,
                        "unique_players": row.unique_players,
                        "unique_contracts": row.unique_contracts,
                        "avg_gas_used": float(row.avg_gas_used) if row.avg_gas_used else 0
                    },
                    "gaming_metrics": {
                        "new_projects": row.new_projects,
                        "total_daily_users": row.total_daily_users
                    }
                }

            return {
                "time_series": time_series_data,
                "analysis_period_days": days,
                "interval": interval,
                "networks_filter": networks,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in optimized time-series metrics query: {e}")
            raise

    def get_performance_monitoring_data(self) -> Dict[str, Any]:
        """Get database performance monitoring metrics"""
        try:
            # Query performance monitoring data
            performance_query = text("""
                SELECT
                    -- Database size metrics
                    pg_size_pretty(pg_database_size(current_database())) as database_size,

                    -- Table sizes
                    (SELECT COUNT(*) FROM articles) as articles_count,
                    (SELECT COUNT(*) FROM gaming_projects) as gaming_projects_count,
                    (SELECT COUNT(*) FROM blockchain_data) as blockchain_data_count,
                    (SELECT COUNT(*) FROM nft_collections) as nft_collections_count,
                    (SELECT COUNT(*) FROM twitter_posts) as twitter_posts_count,
                    (SELECT COUNT(*) FROM reddit_posts) as reddit_posts_count,

                    -- Recent activity
                    (SELECT COUNT(*) FROM articles WHERE published_at >= NOW() - INTERVAL '24 hours') as articles_24h,
                    (SELECT COUNT(*) FROM blockchain_data WHERE block_timestamp >= NOW() - INTERVAL '24 hours') as blockchain_data_24h,
                    (SELECT COUNT(*) FROM twitter_posts WHERE created_at >= NOW() - INTERVAL '24 hours') as twitter_posts_24h,
                    (SELECT COUNT(*) FROM reddit_posts WHERE created_utc >= NOW() - INTERVAL '24 hours') as reddit_posts_24h,

                    -- Performance indicators
                    (SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active') as active_connections,
                    (SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'idle') as idle_connections
            """)

            result = self.db.execute(performance_query).fetchone()

            # Get slow queries if available
            slow_queries_query = text("""
                SELECT
                    query,
                    calls,
                    total_time,
                    mean_time,
                    rows
                FROM pg_stat_statements
                WHERE mean_time > 100
                ORDER BY mean_time DESC
                LIMIT 10
            """)

            try:
                slow_queries = self.db.execute(slow_queries_query).fetchall()
                slow_queries_data = [
                    {
                        "query": row.query[:200] + "..." if len(row.query) > 200 else row.query,
                        "calls": row.calls,
                        "total_time": float(row.total_time),
                        "mean_time": float(row.mean_time),
                        "rows": row.rows
                    }
                    for row in slow_queries
                ]
            except Exception:
                # pg_stat_statements might not be enabled
                slow_queries_data = []

            return {
                "database_metrics": {
                    "database_size": result.database_size,
                    "active_connections": result.active_connections,
                    "idle_connections": result.idle_connections
                },
                "table_counts": {
                    "articles": result.articles_count,
                    "gaming_projects": result.gaming_projects_count,
                    "blockchain_data": result.blockchain_data_count,
                    "nft_collections": result.nft_collections_count,
                    "twitter_posts": result.twitter_posts_count,
                    "reddit_posts": result.reddit_posts_count
                },
                "recent_activity_24h": {
                    "articles": result.articles_24h,
                    "blockchain_data": result.blockchain_data_24h,
                    "twitter_posts": result.twitter_posts_24h,
                    "reddit_posts": result.reddit_posts_24h
                },
                "slow_queries": slow_queries_data,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in performance monitoring query: {e}")
            raise

    # ============================================================================
    # PHASE 4.3: ADVANCED PERFORMANCE MONITORING & DATABASE OPTIMIZATION
    # ============================================================================

    def analyze_query_performance(self, query: str, params: Dict = None) -> Dict[str, Any]:
        """Analyze query execution plan and performance metrics"""
        try:
            # Get query execution plan
            explain_query = text(f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}")

            start_time = time.time()
            if params:
                plan_result = self.db.execute(explain_query, params).fetchone()
            else:
                plan_result = self.db.execute(explain_query).fetchone()
            execution_time = time.time() - start_time

            plan_data = plan_result[0][0] if plan_result else {}

            # Extract key performance metrics
            execution_plan = plan_data.get('Plan', {})
            planning_time = plan_data.get('Planning Time', 0)
            execution_time_db = plan_data.get('Execution Time', 0)

            # Analyze plan for optimization opportunities
            optimization_suggestions = self._analyze_execution_plan(execution_plan)

            return {
                "query_hash": hashlib.md5(query.encode()).hexdigest()[:8],
                "execution_time_ms": execution_time * 1000,
                "planning_time_ms": planning_time,
                "execution_time_db_ms": execution_time_db,
                "total_cost": execution_plan.get('Total Cost', 0),
                "actual_rows": execution_plan.get('Actual Rows', 0),
                "plan_rows": execution_plan.get('Plan Rows', 0),
                "shared_hit_blocks": execution_plan.get('Shared Hit Blocks', 0),
                "shared_read_blocks": execution_plan.get('Shared Read Blocks', 0),
                "optimization_suggestions": optimization_suggestions,
                "full_plan": plan_data,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error analyzing query performance: {e}")
            return {
                "error": str(e),
                "query_hash": hashlib.md5(query.encode()).hexdigest()[:8],
                "timestamp": datetime.utcnow().isoformat()
            }

    def _analyze_execution_plan(self, plan: Dict) -> List[str]:
        """Analyze execution plan and suggest optimizations"""
        suggestions = []

        if not plan:
            return suggestions

        node_type = plan.get('Node Type', '')
        actual_rows = plan.get('Actual Rows', 0)
        plan_rows = plan.get('Plan Rows', 0)

        # Check for sequential scans on large tables
        if node_type == 'Seq Scan' and actual_rows > 1000:
            relation_name = plan.get('Relation Name', 'unknown')
            suggestions.append(f"Consider adding index on {relation_name} for sequential scan")

        # Check for row estimation issues
        if plan_rows > 0 and actual_rows > 0:
            estimation_ratio = actual_rows / plan_rows
            if estimation_ratio > 10 or estimation_ratio < 0.1:
                suggestions.append(f"Row estimation off by {estimation_ratio:.1f}x - consider ANALYZE")

        # Check for expensive sorts
        if node_type == 'Sort' and plan.get('Actual Total Time', 0) > 100:
            suggestions.append("Expensive sort operation - consider adding index for ORDER BY")

        # Check for nested loops with high cost
        if node_type == 'Nested Loop' and plan.get('Total Cost', 0) > 1000:
            suggestions.append("High-cost nested loop - consider hash join or merge join")

        # Recursively analyze child plans
        for child_plan in plan.get('Plans', []):
            suggestions.extend(self._analyze_execution_plan(child_plan))

        return suggestions

    def get_database_index_analysis(self) -> Dict[str, Any]:
        """Analyze database indexes and suggest optimizations"""
        try:
            # Get index usage statistics
            index_usage_query = text("""
                SELECT
                    schemaname,
                    relname as tablename,
                    indexrelname as indexname,
                    idx_tup_read,
                    idx_tup_fetch,
                    idx_scan,
                    CASE
                        WHEN idx_scan = 0 THEN 'UNUSED'
                        WHEN idx_scan < 10 THEN 'LOW_USAGE'
                        WHEN idx_scan < 100 THEN 'MODERATE_USAGE'
                        ELSE 'HIGH_USAGE'
                    END as usage_category,
                    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
                FROM pg_stat_user_indexes
                ORDER BY idx_scan DESC
            """)

            index_stats = self.db.execute(index_usage_query).fetchall()

            # Get missing index suggestions based on query patterns
            missing_indexes_query = text("""
                WITH table_scans AS (
                    SELECT
                        schemaname,
                        relname as tablename,
                        seq_scan,
                        seq_tup_read,
                        idx_scan,
                        n_tup_ins + n_tup_upd + n_tup_del as write_activity
                    FROM pg_stat_user_tables
                    WHERE seq_scan > idx_scan AND seq_tup_read > 1000
                )
                SELECT
                    tablename,
                    seq_scan,
                    seq_tup_read,
                    idx_scan,
                    write_activity,
                    CASE
                        WHEN seq_tup_read > 100000 THEN 'HIGH_PRIORITY'
                        WHEN seq_tup_read > 10000 THEN 'MEDIUM_PRIORITY'
                        ELSE 'LOW_PRIORITY'
                    END as index_priority
                FROM table_scans
                ORDER BY seq_tup_read DESC
            """)

            missing_indexes = self.db.execute(missing_indexes_query).fetchall()

            # Analyze specific gaming-related index opportunities
            gaming_index_analysis = self._analyze_gaming_specific_indexes()

            return {
                "index_usage": [
                    {
                        "schema": row.schemaname,
                        "table": row.tablename,
                        "index": row.indexname,
                        "scans": row.idx_scan,
                        "tuples_read": row.idx_tup_read,
                        "tuples_fetched": row.idx_tup_fetch,
                        "usage_category": row.usage_category,
                        "size": row.index_size
                    }
                    for row in index_stats
                ],
                "missing_index_candidates": [
                    {
                        "table": row.tablename,
                        "sequential_scans": row.seq_scan,
                        "tuples_read": row.seq_tup_read,
                        "index_scans": row.idx_scan,
                        "write_activity": row.write_activity,
                        "priority": row.index_priority
                    }
                    for row in missing_indexes
                ],
                "gaming_specific_recommendations": gaming_index_analysis,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in database index analysis: {e}")
            raise

    def _analyze_gaming_specific_indexes(self) -> List[Dict[str, Any]]:
        """Analyze gaming-specific indexing opportunities"""
        recommendations = []

        try:
            # Check for common gaming query patterns
            gaming_patterns = [
                {
                    "table": "articles",
                    "columns": ["published_at", "gaming_category"],
                    "reason": "Time-based gaming category filtering",
                    "query_pattern": "WHERE published_at >= ? AND gaming_category = ?"
                },
                {
                    "table": "articles",
                    "columns": ["gaming_projects", "sentiment_score"],
                    "reason": "Gaming project sentiment analysis",
                    "query_pattern": "WHERE gaming_projects @> ? ORDER BY sentiment_score"
                },
                {
                    "table": "blockchain_data",
                    "columns": ["blockchain", "block_timestamp", "contract_address"],
                    "reason": "Multi-chain contract activity tracking",
                    "query_pattern": "WHERE blockchain = ? AND block_timestamp >= ? AND contract_address = ?"
                },
                {
                    "table": "blockchain_data",
                    "columns": ["from_address", "blockchain"],
                    "reason": "Cross-chain player activity analysis",
                    "query_pattern": "WHERE from_address = ? AND blockchain IN (?)"
                },
                {
                    "table": "gaming_projects",
                    "columns": ["blockchain", "is_active", "category"],
                    "reason": "Active gaming projects by blockchain and category",
                    "query_pattern": "WHERE blockchain = ? AND is_active = true AND category = ?"
                },
                {
                    "table": "nft_collections",
                    "columns": ["gaming_project_id", "floor_price"],
                    "reason": "Gaming NFT price analysis",
                    "query_pattern": "WHERE gaming_project_id = ? ORDER BY floor_price"
                },
                {
                    "table": "nft_collections",
                    "columns": ["blockchain", "is_active", "volume_24h"],
                    "reason": "Active NFT collections by volume",
                    "query_pattern": "WHERE blockchain = ? AND is_active = true ORDER BY volume_24h DESC"
                }
            ]

            for pattern in gaming_patterns:
                # Check if similar index exists
                index_exists = self._check_index_exists(pattern["table"], pattern["columns"])

                if not index_exists:
                    recommendations.append({
                        "table": pattern["table"],
                        "suggested_columns": pattern["columns"],
                        "index_type": "btree",
                        "reason": pattern["reason"],
                        "query_pattern": pattern["query_pattern"],
                        "priority": "HIGH" if "blockchain" in pattern["columns"] else "MEDIUM",
                        "estimated_benefit": "Significant performance improvement for gaming analytics"
                    })

            return recommendations

        except Exception as e:
            logger.error(f"Error analyzing gaming-specific indexes: {e}")
            return []

    def _check_index_exists(self, table_name: str, columns: List[str]) -> bool:
        """Check if an index exists for the given table and columns"""
        try:
            # Simple check - in production, this would be more sophisticated
            check_query = text("""
                SELECT COUNT(*) as index_count
                FROM pg_indexes
                WHERE tablename = :table_name
                AND indexdef LIKE :column_pattern
            """)

            column_pattern = f"%{columns[0]}%" if columns else "%"
            result = self.db.execute(check_query, {
                "table_name": table_name,
                "column_pattern": column_pattern
            }).fetchone()

            return result.index_count > 0

        except Exception:
            return False

    def create_gaming_materialized_views(self) -> Dict[str, Any]:
        """Create materialized views for complex gaming analytics aggregations"""
        try:
            results = {}

            # 1. Gaming Project Summary View
            gaming_summary_view = text("""
                CREATE MATERIALIZED VIEW IF NOT EXISTS mv_gaming_project_summary AS
                WITH project_metrics AS (
                    SELECT
                        gp.id,
                        gp.project_name,
                        gp.blockchain,
                        gp.category,
                        gp.is_active,
                        gp.daily_active_users,

                        -- Article metrics
                        COUNT(DISTINCT a.id) as total_articles,
                        AVG(a.sentiment_score) as avg_sentiment,
                        COUNT(DISTINCT a.source_id) as unique_sources,
                        MAX(a.published_at) as latest_article_date,

                        -- Blockchain activity
                        COUNT(DISTINCT bd.id) as total_transactions,
                        COUNT(DISTINCT bd.from_address) as unique_players,
                        COUNT(DISTINCT bd.contract_address) as unique_contracts,
                        AVG(bd.gas_used) as avg_gas_used,

                        -- NFT metrics
                        COUNT(DISTINCT nft.id) as nft_collections_count,
                        AVG(nft.floor_price) as avg_nft_floor_price,
                        SUM(nft.volume_24h) as total_nft_volume_24h,

                        -- Social media presence
                        COUNT(DISTINCT tp.id) as twitter_mentions,
                        COUNT(DISTINCT rp.id) as reddit_mentions

                    FROM gaming_projects gp
                    LEFT JOIN articles a ON a.gaming_projects::jsonb @> CONCAT('"', gp.project_name, '"')::jsonb
                    LEFT JOIN blockchain_data bd ON bd.gaming_project_id = gp.id
                    LEFT JOIN nft_collections nft ON nft.gaming_project_id = gp.id
                    LEFT JOIN twitter_posts tp ON tp.text ILIKE CONCAT('%', gp.project_name, '%')
                    LEFT JOIN reddit_posts rp ON rp.title ILIKE CONCAT('%', gp.project_name, '%')
                        OR rp.selftext ILIKE CONCAT('%', gp.project_name, '%')
                    GROUP BY gp.id, gp.project_name, gp.blockchain, gp.category, gp.is_active, gp.daily_active_users
                )
                SELECT
                    *,
                    -- Calculated engagement score
                    (
                        COALESCE(total_articles, 0) * 0.3 +
                        COALESCE(twitter_mentions, 0) * 0.2 +
                        COALESCE(reddit_mentions, 0) * 0.2 +
                        COALESCE(unique_players, 0) * 0.3
                    ) as engagement_score,

                    -- Activity classification
                    CASE
                        WHEN total_transactions > 1000 AND unique_players > 100 THEN 'HIGH_ACTIVITY'
                        WHEN total_transactions > 100 AND unique_players > 10 THEN 'MEDIUM_ACTIVITY'
                        WHEN total_transactions > 0 OR unique_players > 0 THEN 'LOW_ACTIVITY'
                        ELSE 'NO_ACTIVITY'
                    END as activity_level,

                    NOW() as last_updated
                FROM project_metrics;

                CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_gaming_project_summary_id
                ON mv_gaming_project_summary(id);

                CREATE INDEX IF NOT EXISTS idx_mv_gaming_project_summary_blockchain
                ON mv_gaming_project_summary(blockchain);

                CREATE INDEX IF NOT EXISTS idx_mv_gaming_project_summary_activity
                ON mv_gaming_project_summary(activity_level);
            """)

            self.db.execute(gaming_summary_view)
            results["gaming_project_summary"] = "Created successfully"

            # 2. Cross-Chain Activity Summary View
            cross_chain_view = text("""
                CREATE MATERIALIZED VIEW IF NOT EXISTS mv_cross_chain_activity AS
                WITH blockchain_metrics AS (
                    SELECT
                        bd.blockchain,
                        DATE_TRUNC('day', bd.block_timestamp) as activity_date,

                        -- Transaction metrics
                        COUNT(DISTINCT bd.id) as daily_transactions,
                        COUNT(DISTINCT bd.from_address) as daily_unique_players,
                        COUNT(DISTINCT bd.contract_address) as daily_unique_contracts,
                        AVG(bd.gas_used) as avg_daily_gas,

                        -- Gaming project activity
                        COUNT(DISTINCT bd.gaming_project_id) as active_gaming_projects,

                        -- Token activity
                        COUNT(DISTINCT bd.token_address) as unique_tokens_traded,
                        SUM(bd.token_amount) as total_token_volume,

                        -- NFT activity
                        COUNT(DISTINCT CASE WHEN bd.nft_collection IS NOT NULL THEN bd.id END) as nft_transactions

                    FROM blockchain_data bd
                    WHERE bd.block_timestamp >= NOW() - INTERVAL '90 days'
                    GROUP BY bd.blockchain, DATE_TRUNC('day', bd.block_timestamp)
                ),
                article_metrics AS (
                    SELECT
                        bd.blockchain,
                        DATE_TRUNC('day', a.published_at) as activity_date,
                        COUNT(DISTINCT a.id) as daily_articles,
                        AVG(a.sentiment_score) as avg_daily_sentiment,
                        COUNT(DISTINCT a.source_id) as unique_sources
                    FROM articles a
                    INNER JOIN blockchain_data bd ON a.id = bd.article_id
                    WHERE a.published_at >= NOW() - INTERVAL '90 days'
                    GROUP BY bd.blockchain, DATE_TRUNC('day', a.published_at)
                )
                SELECT
                    COALESCE(bm.blockchain, am.blockchain) as blockchain,
                    COALESCE(bm.activity_date, am.activity_date) as activity_date,

                    -- Blockchain metrics
                    COALESCE(bm.daily_transactions, 0) as daily_transactions,
                    COALESCE(bm.daily_unique_players, 0) as daily_unique_players,
                    COALESCE(bm.daily_unique_contracts, 0) as daily_unique_contracts,
                    COALESCE(bm.avg_daily_gas, 0) as avg_daily_gas,
                    COALESCE(bm.active_gaming_projects, 0) as active_gaming_projects,
                    COALESCE(bm.unique_tokens_traded, 0) as unique_tokens_traded,
                    COALESCE(bm.total_token_volume, 0) as total_token_volume,
                    COALESCE(bm.nft_transactions, 0) as nft_transactions,

                    -- Article metrics
                    COALESCE(am.daily_articles, 0) as daily_articles,
                    COALESCE(am.avg_daily_sentiment, 0) as avg_daily_sentiment,
                    COALESCE(am.unique_sources, 0) as unique_sources,

                    NOW() as last_updated

                FROM blockchain_metrics bm
                FULL OUTER JOIN article_metrics am ON bm.blockchain = am.blockchain
                    AND bm.activity_date = am.activity_date
                ORDER BY activity_date DESC, blockchain;

                CREATE INDEX IF NOT EXISTS idx_mv_cross_chain_activity_blockchain_date
                ON mv_cross_chain_activity(blockchain, activity_date);

                CREATE INDEX IF NOT EXISTS idx_mv_cross_chain_activity_date
                ON mv_cross_chain_activity(activity_date);
            """)

            self.db.execute(cross_chain_view)
            results["cross_chain_activity"] = "Created successfully"

            self.db.commit()

            return {
                "materialized_views": results,
                "refresh_schedule": "Recommended: Every 4 hours for optimal performance",
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating materialized views: {e}")
            raise

    def refresh_materialized_views(self, concurrent: bool = True) -> Dict[str, Any]:
        """Refresh materialized views for updated analytics data"""
        try:
            results = {}
            refresh_option = "CONCURRENTLY" if concurrent else ""

            # Refresh gaming project summary view
            gaming_refresh = text(f"REFRESH MATERIALIZED VIEW {refresh_option} mv_gaming_project_summary")
            start_time = time.time()
            self.db.execute(gaming_refresh)
            gaming_time = time.time() - start_time
            results["gaming_project_summary"] = f"Refreshed in {gaming_time:.2f}s"

            # Refresh cross-chain activity view
            cross_chain_refresh = text(f"REFRESH MATERIALIZED VIEW {refresh_option} mv_cross_chain_activity")
            start_time = time.time()
            self.db.execute(cross_chain_refresh)
            cross_chain_time = time.time() - start_time
            results["cross_chain_activity"] = f"Refreshed in {cross_chain_time:.2f}s"

            self.db.commit()

            return {
                "refresh_results": results,
                "total_time": f"{gaming_time + cross_chain_time:.2f}s",
                "concurrent_refresh": concurrent,
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error refreshing materialized views: {e}")
            raise

    def get_materialized_view_stats(self) -> Dict[str, Any]:
        """Get statistics and metadata for materialized views"""
        try:
            # Get materialized view information
            mv_stats_query = text("""
                SELECT
                    schemaname,
                    matviewname,
                    matviewowner,
                    tablespace,
                    hasindexes,
                    ispopulated,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||matviewname)) as size
                FROM pg_matviews
                WHERE schemaname = 'public'
                ORDER BY matviewname
            """)

            mv_stats = self.db.execute(mv_stats_query).fetchall()

            # Get row counts for each materialized view
            view_counts = {}
            for view in mv_stats:
                try:
                    count_query = text(f"SELECT COUNT(*) as row_count FROM {view.matviewname}")
                    count_result = self.db.execute(count_query).fetchone()
                    view_counts[view.matviewname] = count_result.row_count
                except Exception:
                    view_counts[view.matviewname] = 0

            return {
                "materialized_views": [
                    {
                        "name": row.matviewname,
                        "schema": row.schemaname,
                        "owner": row.matviewowner,
                        "has_indexes": row.hasindexes,
                        "is_populated": row.ispopulated,
                        "size": row.size,
                        "row_count": view_counts.get(row.matviewname, 0)
                    }
                    for row in mv_stats
                ],
                "total_views": len(mv_stats),
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting materialized view stats: {e}")
            raise

    def optimize_gaming_queries_with_joins(self) -> Dict[str, Any]:
        """Demonstrate optimized gaming queries using advanced SQL joins"""
        try:
            # Complex gaming analytics query with multiple joins and CTEs
            # Check if materialized view exists first
            mv_exists_query = text("""
                SELECT COUNT(*) as mv_count
                FROM pg_matviews
                WHERE matviewname = 'mv_gaming_project_summary'
            """)
            mv_exists = self.db.execute(mv_exists_query).fetchone().mv_count > 0

            if mv_exists:
                optimized_query = text("""
                    WITH gaming_activity_summary AS (
                        -- Use materialized view if available, fallback to live data
                        SELECT * FROM mv_gaming_project_summary
                        WHERE last_updated >= NOW() - INTERVAL '4 hours'

                        UNION ALL

                        SELECT
                            gp.id, gp.project_name, gp.blockchain, gp.category, gp.is_active,
                            gp.daily_active_users, 0 as total_articles, 0 as avg_sentiment,
                            0 as unique_sources, NULL as latest_article_date,
                            0 as total_transactions, 0 as unique_players, 0 as unique_contracts,
                            0 as avg_gas_used, 0 as nft_collections_count, 0 as avg_nft_floor_price,
                            0 as total_nft_volume_24h, 0 as twitter_mentions, 0 as reddit_mentions,
                            0 as engagement_score, 'NO_ACTIVITY' as activity_level, NOW() as last_updated
                        FROM gaming_projects gp
                        WHERE NOT EXISTS (
                            SELECT 1 FROM mv_gaming_project_summary mvs
                            WHERE mvs.id = gp.id AND mvs.last_updated >= NOW() - INTERVAL '4 hours'
                        )
                    ),
                    blockchain_performance AS (
                        SELECT
                            blockchain,
                            COUNT(*) as total_projects,
                            AVG(engagement_score) as avg_engagement,
                            SUM(total_transactions) as total_blockchain_transactions,
                            SUM(unique_players) as total_unique_players
                        FROM gaming_activity_summary
                        GROUP BY blockchain
                    ),
                    top_performers AS (
                        SELECT
                            gas.*,
                            bp.avg_engagement as blockchain_avg_engagement,
                            bp.total_blockchain_transactions,
                            RANK() OVER (PARTITION BY gas.blockchain ORDER BY gas.engagement_score DESC) as blockchain_rank,
                            RANK() OVER (ORDER BY gas.engagement_score DESC) as global_rank
                        FROM gaming_activity_summary gas
                        INNER JOIN blockchain_performance bp ON gas.blockchain = bp.blockchain
                        WHERE gas.is_active = true
                    )
                    SELECT
                        tp.project_name,
                        tp.blockchain,
                        tp.category,
                        tp.engagement_score,
                        tp.blockchain_rank,
                        tp.global_rank,
                        tp.total_transactions,
                        tp.unique_players,
                        tp.avg_sentiment,
                        tp.blockchain_avg_engagement,
                        tp.activity_level,
                        CASE
                            WHEN tp.engagement_score > tp.blockchain_avg_engagement * 1.5 THEN 'OUTPERFORMING'
                            WHEN tp.engagement_score > tp.blockchain_avg_engagement * 0.8 THEN 'AVERAGE'
                            ELSE 'UNDERPERFORMING'
                        END as performance_category
                    FROM top_performers tp
                    WHERE tp.blockchain_rank <= 10  -- Top 10 per blockchain
                    ORDER BY tp.global_rank
                    LIMIT 50
                """)
            else:
                # Fallback query without materialized views
                optimized_query = text("""
                    WITH gaming_activity_summary AS (
                        SELECT
                            gp.id, gp.project_name, gp.blockchain, gp.category, gp.is_active,
                            gp.daily_active_users,

                            -- Article metrics (simplified for fallback)
                            0 as total_articles,
                            0 as avg_sentiment,
                            0 as unique_sources,
                            NULL as latest_article_date,

                            -- Blockchain activity (simplified for fallback)
                            0 as total_transactions,
                            0 as unique_players,
                            0 as unique_contracts,
                            0 as avg_gas_used,

                            -- NFT metrics (simplified for fallback)
                            0 as nft_collections_count,
                            0 as avg_nft_floor_price,
                            0 as total_nft_volume_24h,

                            -- Social media presence (simplified for fallback)
                            0 as twitter_mentions,
                            0 as reddit_mentions,

                            -- Calculated engagement score (simplified)
                            COALESCE(gp.daily_active_users, 0) as engagement_score,
                            'NO_ACTIVITY' as activity_level,
                            NOW() as last_updated
                        FROM gaming_projects gp
                        WHERE gp.is_active = true
                    ),
                    blockchain_performance AS (
                        SELECT
                            blockchain,
                            COUNT(*) as total_projects,
                            AVG(engagement_score) as avg_engagement,
                            SUM(total_transactions) as total_blockchain_transactions,
                            SUM(unique_players) as total_unique_players
                        FROM gaming_activity_summary
                        GROUP BY blockchain
                    ),
                    top_performers AS (
                        SELECT
                            gas.*,
                            bp.avg_engagement as blockchain_avg_engagement,
                            bp.total_blockchain_transactions,
                            RANK() OVER (PARTITION BY gas.blockchain ORDER BY gas.engagement_score DESC) as blockchain_rank,
                            RANK() OVER (ORDER BY gas.engagement_score DESC) as global_rank
                        FROM gaming_activity_summary gas
                        INNER JOIN blockchain_performance bp ON gas.blockchain = bp.blockchain
                        WHERE gas.is_active = true
                    )
                    SELECT
                        tp.project_name,
                        tp.blockchain,
                        tp.category,
                        tp.engagement_score,
                        tp.blockchain_rank,
                        tp.global_rank,
                        tp.total_transactions,
                        tp.unique_players,
                        tp.avg_sentiment,
                        tp.blockchain_avg_engagement,
                        tp.activity_level,
                        CASE
                            WHEN tp.engagement_score > tp.blockchain_avg_engagement * 1.5 THEN 'OUTPERFORMING'
                            WHEN tp.engagement_score > tp.blockchain_avg_engagement * 0.8 THEN 'AVERAGE'
                            ELSE 'UNDERPERFORMING'
                        END as performance_category
                    FROM top_performers tp
                    WHERE tp.blockchain_rank <= 10  -- Top 10 per blockchain
                    ORDER BY tp.global_rank
                    LIMIT 50
                """)



            start_time = time.time()
            results = self.db.execute(optimized_query).fetchall()
            execution_time = time.time() - start_time

            # Format results
            gaming_analytics = [
                {
                    "project_name": row.project_name,
                    "blockchain": row.blockchain,
                    "category": row.category,
                    "engagement_score": float(row.engagement_score),
                    "blockchain_rank": row.blockchain_rank,
                    "global_rank": row.global_rank,
                    "total_transactions": row.total_transactions,
                    "unique_players": row.unique_players,
                    "avg_sentiment": float(row.avg_sentiment) if row.avg_sentiment else 0,
                    "performance_category": row.performance_category,
                    "activity_level": row.activity_level
                }
                for row in results
            ]

            return {
                "optimized_gaming_analytics": gaming_analytics,
                "execution_time_ms": execution_time * 1000,
                "total_projects": len(gaming_analytics),
                "query_optimization_features": [
                    "Materialized view integration",
                    "Complex CTEs for data aggregation",
                    "Window functions for ranking",
                    "Advanced JOIN strategies",
                    "Conditional performance categorization"
                ],
                "timestamp": datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Error in optimized gaming queries: {e}")
            raise


def get_sql_optimizer(db: Session = None) -> SQLOptimizer:
    """Get SQL optimizer instance"""
    if db is None:
        db = next(get_db())
    return SQLOptimizer(db)
