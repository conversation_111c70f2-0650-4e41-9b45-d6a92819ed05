"""
SQL Query Optimization Service
Replaces Python-heavy operations with efficient SQL joins and aggregations
"""
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy import text, func, and_, or_, desc, asc
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy.sql import select

from models.gaming import (
    Article, Source, GamingProject, NFTCollection, BlockchainData,
    TwitterPost, RedditPost, SocialMediaFilter
)
from models.base import get_db

logger = logging.getLogger(__name__)


class SQLOptimizer:
    """Optimized SQL queries to replace inefficient Python operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_dashboard_overview_optimized(self, hours: int = 24) -> Dict[str, Any]:
        """Single optimized query for dashboard overview instead of multiple separate queries"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            # Single comprehensive query with all counts and aggregations
            overview_query = text("""
                WITH overview_stats AS (
                    SELECT
                        (SELECT COUNT(*) FROM articles) as total_articles,
                        (SELECT COUNT(*) FROM sources WHERE is_active = true) as total_sources,
                        (SELECT COUNT(*) FROM gaming_projects WHERE is_active = true) as total_gaming_projects,
                        (SELECT COUNT(*) FROM nft_collections WHERE is_active = true) as total_nft_collections,
                        (SELECT COUNT(*) FROM articles WHERE published_at >= :since) as articles_recent,
                        (SELECT COUNT(DISTINCT blockchain_network) FROM articles WHERE blockchain_network IS NOT NULL) as active_networks,
                        (SELECT AVG(sentiment_score) FROM articles WHERE published_at >= :since AND sentiment_score IS NOT NULL) as avg_sentiment
                ),
                top_categories AS (
                    SELECT 
                        gaming_category,
                        COUNT(*) as category_count
                    FROM articles 
                    WHERE published_at >= :since 
                        AND gaming_category IS NOT NULL
                    GROUP BY gaming_category
                    ORDER BY COUNT(*) DESC
                    LIMIT 5
                ),
                network_activity AS (
                    SELECT 
                        blockchain_network,
                        COUNT(*) as network_articles,
                        AVG(sentiment_score) as network_sentiment
                    FROM articles 
                    WHERE published_at >= :since 
                        AND blockchain_network IS NOT NULL
                    GROUP BY blockchain_network
                    ORDER BY COUNT(*) DESC
                )
                SELECT 
                    os.*,
                    COALESCE(
                        json_agg(
                            json_build_object(
                                'category', tc.gaming_category,
                                'count', tc.category_count
                            )
                        ) FILTER (WHERE tc.gaming_category IS NOT NULL),
                        '[]'::json
                    ) as top_categories,
                    COALESCE(
                        json_agg(
                            json_build_object(
                                'network', na.blockchain_network,
                                'articles', na.network_articles,
                                'sentiment', na.network_sentiment
                            )
                        ) FILTER (WHERE na.blockchain_network IS NOT NULL),
                        '[]'::json
                    ) as network_activity
                FROM overview_stats os
                LEFT JOIN top_categories tc ON true
                LEFT JOIN network_activity na ON true
                GROUP BY os.total_articles, os.total_sources, os.total_gaming_projects, 
                         os.total_nft_collections, os.articles_recent, os.active_networks, os.avg_sentiment
            """)
            
            result = self.db.execute(overview_query, {"since": since}).fetchone()
            
            return {
                "total_articles": result.total_articles,
                "total_sources": result.total_sources,
                "total_gaming_projects": result.total_gaming_projects,
                "total_nft_collections": result.total_nft_collections,
                "articles_last_24h": result.articles_recent,
                "active_blockchain_networks": result.active_networks,
                "average_sentiment": float(result.avg_sentiment) if result.avg_sentiment else 0.0,
                "top_gaming_categories": result.top_categories,
                "network_activity": result.network_activity
            }
            
        except Exception as e:
            logger.error(f"Error in optimized dashboard overview query: {e}")
            raise
    
    def get_gaming_projects_with_metrics_optimized(self, 
                                                  blockchain: Optional[str] = None,
                                                  category: Optional[str] = None,
                                                  hours: int = 24) -> List[Dict[str, Any]]:
        """Optimized query for gaming projects with article metrics using joins"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            # Build dynamic WHERE conditions
            where_conditions = ["gp.is_active = true"]
            params = {"since": since}
            
            if blockchain:
                where_conditions.append("gp.blockchain_network = :blockchain")
                params["blockchain"] = blockchain
            
            if category:
                where_conditions.append("gp.category = :category")
                params["category"] = category
            
            where_clause = " AND ".join(where_conditions)
            
            # Single optimized query with LEFT JOINs for all metrics
            projects_query = text(f"""
                SELECT 
                    gp.id,
                    gp.project_name,
                    gp.name,
                    gp.blockchain_network,
                    gp.category,
                    gp.token_symbol,
                    gp.token_address,
                    gp.is_active,
                    gp.daily_active_users,
                    gp.monthly_active_users,
                    gp.total_value_locked,
                    
                    -- Article metrics
                    COUNT(DISTINCT a.id) as total_articles,
                    COUNT(DISTINCT CASE WHEN a.published_at >= :since THEN a.id END) as recent_articles,
                    AVG(a.sentiment_score) as avg_sentiment,
                    MAX(a.published_at) as latest_article_date,
                    
                    -- NFT metrics
                    COUNT(DISTINCT nft.id) as nft_collections_count,
                    AVG(nft.floor_price) as avg_floor_price,
                    SUM(nft.volume_24h) as total_nft_volume_24h,
                    
                    -- Social media metrics
                    COUNT(DISTINCT tp.id) as twitter_mentions,
                    COUNT(DISTINCT rp.id) as reddit_mentions,
                    AVG(tp.engagement_score) as avg_twitter_engagement,
                    AVG(rp.score) as avg_reddit_score
                    
                FROM gaming_projects gp
                LEFT JOIN articles a ON (
                    a.gaming_projects ? gp.project_name 
                    OR a.gaming_projects ? gp.name
                    OR LOWER(a.title) LIKE LOWER('%' || gp.project_name || '%')
                    OR LOWER(a.content) LIKE LOWER('%' || gp.project_name || '%')
                )
                LEFT JOIN nft_collections nft ON (
                    nft.gaming_project_id = gp.id
                    OR nft.project_name = gp.project_name
                )
                LEFT JOIN twitter_posts tp ON (
                    tp.gaming_projects ? gp.project_name
                    OR tp.gaming_projects ? gp.name
                    AND tp.created_at >= :since
                )
                LEFT JOIN reddit_posts rp ON (
                    rp.gaming_projects ? gp.project_name
                    OR rp.gaming_projects ? gp.name
                    AND rp.created_utc >= :since
                )
                WHERE {where_clause}
                GROUP BY gp.id, gp.project_name, gp.name, gp.blockchain_network, 
                         gp.category, gp.token_symbol, gp.token_address, gp.is_active,
                         gp.daily_active_users, gp.monthly_active_users, gp.total_value_locked
                ORDER BY recent_articles DESC, total_articles DESC, gp.daily_active_users DESC NULLS LAST
            """)
            
            results = self.db.execute(projects_query, params).fetchall()
            
            return [
                {
                    "id": row.id,
                    "project_name": row.project_name,
                    "name": row.name,
                    "blockchain_network": row.blockchain_network,
                    "category": row.category,
                    "token_symbol": row.token_symbol,
                    "token_address": row.token_address,
                    "is_active": row.is_active,
                    "daily_active_users": row.daily_active_users,
                    "monthly_active_users": row.monthly_active_users,
                    "total_value_locked": float(row.total_value_locked) if row.total_value_locked else 0.0,
                    "metrics": {
                        "total_articles": row.total_articles,
                        "recent_articles": row.recent_articles,
                        "avg_sentiment": float(row.avg_sentiment) if row.avg_sentiment else 0.0,
                        "latest_article_date": row.latest_article_date.isoformat() if row.latest_article_date else None,
                        "nft_collections_count": row.nft_collections_count,
                        "avg_floor_price": float(row.avg_floor_price) if row.avg_floor_price else 0.0,
                        "total_nft_volume_24h": float(row.total_nft_volume_24h) if row.total_nft_volume_24h else 0.0,
                        "twitter_mentions": row.twitter_mentions,
                        "reddit_mentions": row.reddit_mentions,
                        "avg_twitter_engagement": float(row.avg_twitter_engagement) if row.avg_twitter_engagement else 0.0,
                        "avg_reddit_score": float(row.avg_reddit_score) if row.avg_reddit_score else 0.0
                    }
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"Error in optimized gaming projects query: {e}")
            raise
    
    def get_social_media_with_gaming_context_optimized(self, 
                                                      platform: str = "both",
                                                      hours: int = 24,
                                                      min_engagement: int = 0,
                                                      gaming_only: bool = True) -> Dict[str, Any]:
        """Optimized social media query with gaming project context using joins"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            results = {}
            
            if platform in ["twitter", "both"]:
                # Optimized Twitter query with gaming project joins
                twitter_query = text("""
                    SELECT 
                        tp.id,
                        tp.twitter_id,
                        tp.text,
                        tp.author_username,
                        tp.author_name,
                        tp.created_at,
                        tp.url,
                        tp.like_count,
                        tp.retweet_count,
                        tp.reply_count,
                        tp.gaming_projects,
                        tp.gaming_influencers,
                        tp.hashtags,
                        tp.sentiment_score,
                        tp.relevance_score,
                        tp.engagement_score,
                        
                        -- Gaming project details via join
                        COALESCE(
                            json_agg(
                                DISTINCT json_build_object(
                                    'project_name', gp.project_name,
                                    'name', gp.name,
                                    'blockchain_network', gp.blockchain_network,
                                    'category', gp.category,
                                    'token_symbol', gp.token_symbol
                                )
                            ) FILTER (WHERE gp.id IS NOT NULL),
                            '[]'::json
                        ) as project_details
                        
                    FROM twitter_posts tp
                    LEFT JOIN gaming_projects gp ON (
                        tp.gaming_projects ? gp.project_name
                        OR tp.gaming_projects ? gp.name
                    )
                    WHERE tp.created_at >= :since
                        AND (:gaming_only = false OR tp.is_gaming_related = true)
                        AND (tp.like_count + tp.retweet_count) >= :min_engagement
                    GROUP BY tp.id, tp.twitter_id, tp.text, tp.author_username, tp.author_name,
                             tp.created_at, tp.url, tp.like_count, tp.retweet_count, tp.reply_count,
                             tp.gaming_projects, tp.gaming_influencers, tp.hashtags,
                             tp.sentiment_score, tp.relevance_score, tp.engagement_score
                    ORDER BY tp.created_at DESC, tp.engagement_score DESC
                    LIMIT 100
                """)
                
                twitter_results = self.db.execute(twitter_query, {
                    "since": since,
                    "gaming_only": gaming_only,
                    "min_engagement": min_engagement
                }).fetchall()
                
                results["twitter"] = [
                    {
                        "id": row.twitter_id,
                        "text": row.text,
                        "author_username": row.author_username,
                        "author_name": row.author_name,
                        "created_at": row.created_at.isoformat(),
                        "url": row.url,
                        "engagement": {
                            "likes": row.like_count,
                            "retweets": row.retweet_count,
                            "replies": row.reply_count,
                            "total_score": row.engagement_score
                        },
                        "gaming_projects": row.gaming_projects or [],
                        "gaming_influencers": row.gaming_influencers or [],
                        "hashtags": row.hashtags or [],
                        "sentiment_score": row.sentiment_score,
                        "relevance_score": row.relevance_score,
                        "project_details": row.project_details
                    }
                    for row in twitter_results
                ]
            
            if platform in ["reddit", "both"]:
                # Similar optimized Reddit query
                reddit_query = text("""
                    SELECT 
                        rp.id,
                        rp.reddit_id,
                        rp.title,
                        rp.selftext,
                        rp.author,
                        rp.created_utc,
                        rp.url,
                        rp.score,
                        rp.num_comments,
                        rp.upvote_ratio,
                        rp.subreddit,
                        rp.gaming_projects,
                        rp.gaming_keywords,
                        rp.sentiment_score,
                        rp.relevance_score,
                        
                        -- Gaming project details via join
                        COALESCE(
                            json_agg(
                                DISTINCT json_build_object(
                                    'project_name', gp.project_name,
                                    'name', gp.name,
                                    'blockchain_network', gp.blockchain_network,
                                    'category', gp.category,
                                    'token_symbol', gp.token_symbol
                                )
                            ) FILTER (WHERE gp.id IS NOT NULL),
                            '[]'::json
                        ) as project_details
                        
                    FROM reddit_posts rp
                    LEFT JOIN gaming_projects gp ON (
                        rp.gaming_projects ? gp.project_name
                        OR rp.gaming_projects ? gp.name
                    )
                    WHERE rp.created_utc >= :since
                        AND (:gaming_only = false OR rp.is_gaming_related = true)
                        AND rp.score >= :min_engagement
                        AND rp.meets_quality_threshold = true
                    GROUP BY rp.id, rp.reddit_id, rp.title, rp.selftext, rp.author,
                             rp.created_utc, rp.url, rp.score, rp.num_comments, rp.upvote_ratio,
                             rp.subreddit, rp.gaming_projects, rp.gaming_keywords,
                             rp.sentiment_score, rp.relevance_score
                    ORDER BY rp.created_utc DESC, rp.score DESC
                    LIMIT 100
                """)
                
                reddit_results = self.db.execute(reddit_query, {
                    "since": since,
                    "gaming_only": gaming_only,
                    "min_engagement": min_engagement
                }).fetchall()
                
                results["reddit"] = [
                    {
                        "id": row.reddit_id,
                        "title": row.title,
                        "selftext": row.selftext,
                        "author": row.author,
                        "created_utc": row.created_utc.isoformat(),
                        "url": row.url,
                        "score": row.score,
                        "num_comments": row.num_comments,
                        "upvote_ratio": row.upvote_ratio,
                        "subreddit": row.subreddit,
                        "gaming_projects": row.gaming_projects or [],
                        "gaming_keywords": row.gaming_keywords or [],
                        "sentiment_score": row.sentiment_score,
                        "relevance_score": row.relevance_score,
                        "project_details": row.project_details
                    }
                    for row in reddit_results
                ]
            
            return results
            
        except Exception as e:
            logger.error(f"Error in optimized social media query: {e}")
            raise
    
    def get_blockchain_activity_with_projects_optimized(self, 
                                                       hours: int = 24,
                                                       networks: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """Optimized blockchain activity query with gaming project context"""
        try:
            since = datetime.utcnow() - timedelta(hours=hours)
            
            # Build network filter
            network_filter = ""
            params = {"since": since}
            
            if networks:
                network_filter = "AND bd.blockchain_network = ANY(:networks)"
                params["networks"] = networks
            
            # Optimized query with gaming project joins
            activity_query = text(f"""
                SELECT 
                    bd.id,
                    bd.blockchain_network,
                    bd.contract_address,
                    bd.event_type,
                    bd.event_name,
                    bd.block_number,
                    bd.transaction_hash,
                    bd.block_timestamp,
                    bd.player_address,
                    bd.token_id,
                    bd.amount,
                    bd.game_action,
                    bd.gas_used,
                    bd.decoded_data,
                    
                    -- Gaming project details
                    gp.project_name,
                    gp.name as project_display_name,
                    gp.category as project_category,
                    gp.token_symbol,
                    
                    -- NFT collection details
                    nft.collection_name,
                    nft.floor_price,
                    nft.gaming_category as nft_category
                    
                FROM blockchain_data bd
                LEFT JOIN gaming_projects gp ON (
                    bd.contract_address = gp.token_address
                    OR bd.contract_address = ANY(
                        SELECT nft_inner.contract_address 
                        FROM nft_collections nft_inner 
                        WHERE nft_inner.gaming_project_id = gp.id
                    )
                )
                LEFT JOIN nft_collections nft ON (
                    bd.contract_address = nft.contract_address
                )
                WHERE bd.block_timestamp >= :since
                    {network_filter}
                ORDER BY bd.block_timestamp DESC, bd.block_number DESC
                LIMIT 500
            """)
            
            results = self.db.execute(activity_query, params).fetchall()
            
            return [
                {
                    "id": row.id,
                    "blockchain_network": row.blockchain_network,
                    "contract_address": row.contract_address,
                    "event_type": row.event_type,
                    "event_name": row.event_name,
                    "block_number": row.block_number,
                    "transaction_hash": row.transaction_hash,
                    "block_timestamp": row.block_timestamp.isoformat(),
                    "player_address": row.player_address,
                    "token_id": row.token_id,
                    "amount": row.amount,
                    "game_action": row.game_action,
                    "gas_used": row.gas_used,
                    "decoded_data": row.decoded_data,
                    "gaming_context": {
                        "project_name": row.project_name,
                        "project_display_name": row.project_display_name,
                        "project_category": row.project_category,
                        "token_symbol": row.token_symbol,
                        "nft_collection_name": row.collection_name,
                        "nft_floor_price": float(row.floor_price) if row.floor_price else None,
                        "nft_category": row.nft_category
                    }
                }
                for row in results
            ]
            
        except Exception as e:
            logger.error(f"Error in optimized blockchain activity query: {e}")
            raise


def get_sql_optimizer(db: Session = None) -> SQLOptimizer:
    """Get SQL optimizer instance"""
    if db is None:
        db = next(get_db())
    return SQLOptimizer(db)
