"""
Advanced Market Analytics for Gaming Sector Analysis
Phase 7: Investment Tracking, Competitive Analysis, and Market Alerts
"""
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

try:
    import numpy as np
    import pandas as pd
    ANALYTICS_AVAILABLE = True
except ImportError:
    ANALYTICS_AVAILABLE = False

from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class AlertType(Enum):
    """Types of market alerts"""
    PRICE_SURGE = "price_surge"
    PRICE_DROP = "price_drop"
    VOLUME_SPIKE = "volume_spike"
    NEWS_SENTIMENT = "news_sentiment"
    PROTOCOL_UPDATE = "protocol_update"
    COMPETITIVE_THREAT = "competitive_threat"
    REGULATORY_RISK = "regulatory_risk"


class InvestmentSignal(Enum):
    """Investment signal types"""
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    HOLD = "hold"
    SELL = "sell"
    STRONG_SELL = "strong_sell"


@dataclass
class MarketAlert:
    """Market alert data structure"""
    alert_type: AlertType
    project_name: str
    message: str
    severity: str  # low, medium, high, critical
    data: Dict[str, Any]
    timestamp: datetime
    is_active: bool = True


@dataclass
class InvestmentMetrics:
    """Investment tracking metrics"""
    project_name: str
    current_price: float
    price_change_24h: float
    price_change_7d: float
    volume_24h: float
    market_cap: float
    sentiment_score: float
    investment_signal: InvestmentSignal
    risk_score: float
    last_updated: datetime


@dataclass
class CompetitiveAnalysis:
    """Competitive analysis result"""
    project_name: str
    market_position: int
    competitive_advantages: List[str]
    competitive_threats: List[str]
    market_share_estimate: float
    growth_trajectory: str
    innovation_score: float
    community_strength: float
    analysis_timestamp: datetime


class GamingSectorAnalyzer:
    """Advanced gaming sector analysis engine"""
    
    def __init__(self):
        self.gaming_projects = {}
        self._load_gaming_projects_from_config()

    def _load_gaming_projects_from_config(self):
        """Load gaming projects from configuration instead of hardcoded data"""
        try:
            from config.gaming_config import gaming_project_manager

            enabled_projects = gaming_project_manager.get_enabled_projects()

            for project_key, project in enabled_projects.items():
                # Extract token symbols from project tokens
                token_symbols = [token.symbol for token in project.tokens if token.symbol]

                # Determine category from genre
                category = self._map_genre_to_category(project.genre)

                # Create project entry for market analytics
                self.gaming_projects[project_key] = {
                    'name': project.project_name,
                    'tokens': token_symbols,
                    'blockchain': project.blockchain.lower() if project.blockchain else 'ethereum',
                    'category': category,
                    'market_cap_rank': len(self.gaming_projects) + 1  # Simple ranking by order
                }

            logger.info(f"✅ Loaded {len(self.gaming_projects)} gaming projects for market analytics")

        except ImportError:
            logger.warning("⚠️ Gaming project manager not available, using fallback data")
            self._load_fallback_projects()
        except Exception as e:
            logger.error(f"❌ Failed to load gaming projects from config: {e}")
            self._load_fallback_projects()

    def _map_genre_to_category(self, genre: str) -> str:
        """Map project genre to market analytics category"""
        if not genre:
            return 'gaming'

        genre_lower = genre.lower()
        if 'strategy' in genre_lower or 'rpg' in genre_lower:
            return 'p2e'
        elif 'metaverse' in genre_lower or 'virtual' in genre_lower:
            return 'metaverse'
        elif 'platform' in genre_lower or 'ecosystem' in genre_lower:
            return 'gaming-platform'
        else:
            return 'gaming'

    def _load_fallback_projects(self):
        """Load minimal fallback projects when configuration is not available"""
        logger.warning("⚠️ Using fallback gaming projects - configuration should be loaded instead")
        self.gaming_projects = {
            'axie-infinity': {
                'name': 'Axie Infinity',
                'tokens': ['AXS', 'SLP'],
                'blockchain': 'ronin',
                'category': 'p2e',
                'market_cap_rank': 1
            },
            'star-atlas': {
                'name': 'Star Atlas',
                'tokens': ['ATLAS', 'POLIS'],
                'blockchain': 'solana',
                'category': 'metaverse',
                'market_cap_rank': 2
            }
        }
        
        self.sector_weights = {
            'p2e': 0.30,
            'metaverse': 0.25,
            'nft-gaming': 0.20,
            'defi-gaming': 0.15,
            'gaming-platform': 0.10
        }
        
        self.correlation_matrix = {}
        self.performance_history = {}
    
    async def analyze_cross_protocol_performance(self, timeframe: str = "7d") -> Dict[str, Any]:
        """Analyze cross-protocol performance correlations"""
        
        try:
            # Simulate performance data (in real implementation, fetch from APIs)
            performance_data = await self._fetch_performance_data(timeframe)
            
            # Calculate correlations
            correlations = self._calculate_correlations(performance_data)
            
            # Analyze sector performance
            sector_performance = self._analyze_sector_performance(performance_data)
            
            # Identify leading and lagging indicators
            indicators = self._identify_performance_indicators(performance_data)
            
            return {
                'timeframe': timeframe,
                'correlations': correlations,
                'sector_performance': sector_performance,
                'leading_indicators': indicators['leading'],
                'lagging_indicators': indicators['lagging'],
                'cross_chain_analysis': self._analyze_cross_chain_performance(performance_data),
                'analysis_timestamp': datetime.utcnow()
            }
            
        except Exception as e:
            logger.error(f"Error in cross-protocol analysis: {e}")
            return self._get_fallback_analysis()
    
    async def _fetch_performance_data(self, timeframe: str) -> Dict[str, Dict[str, float]]:
        """Fetch performance data for gaming projects"""
        
        # Simulate realistic performance data
        # In production, this would fetch from CoinGecko, DeFiPulse, etc.
        
        performance_data = {}
        
        for project_id, project_info in self.gaming_projects.items():
            # Simulate price changes based on project characteristics
            base_volatility = 0.1 if project_info['category'] == 'metaverse' else 0.15
            
            if timeframe == "24h":
                price_change = np.random.normal(0, base_volatility) if ANALYTICS_AVAILABLE else 0.05
                volume_change = np.random.normal(0.1, 0.2) if ANALYTICS_AVAILABLE else 0.1
            elif timeframe == "7d":
                price_change = np.random.normal(0, base_volatility * 2) if ANALYTICS_AVAILABLE else 0.1
                volume_change = np.random.normal(0.2, 0.3) if ANALYTICS_AVAILABLE else 0.2
            else:
                price_change = np.random.normal(0, base_volatility * 3) if ANALYTICS_AVAILABLE else 0.15
                volume_change = np.random.normal(0.3, 0.4) if ANALYTICS_AVAILABLE else 0.3
            
            performance_data[project_id] = {
                'price_change': price_change,
                'volume_change': volume_change,
                'market_cap': 1000000000 / project_info['market_cap_rank'],  # Simulated market cap
                'trading_volume': 50000000 * (1 + volume_change),
                'social_sentiment': np.random.uniform(0.3, 0.8) if ANALYTICS_AVAILABLE else 0.6
            }
        
        return performance_data
    
    def _calculate_correlations(self, performance_data: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """Calculate correlation matrix between gaming projects"""
        
        if not ANALYTICS_AVAILABLE:
            return {'correlation_matrix': {}, 'average_correlation': 0.5}
        
        try:
            # Extract price changes for correlation calculation
            projects = list(performance_data.keys())
            price_changes = [performance_data[project]['price_change'] for project in projects]
            
            # Create correlation matrix
            correlation_matrix = {}
            total_correlations = 0
            correlation_sum = 0
            
            for i, project1 in enumerate(projects):
                correlation_matrix[project1] = {}
                for j, project2 in enumerate(projects):
                    if i == j:
                        correlation = 1.0
                    else:
                        # Simulate correlation based on category similarity
                        cat1 = self.gaming_projects[project1]['category']
                        cat2 = self.gaming_projects[project2]['category']
                        
                        if cat1 == cat2:
                            correlation = np.random.uniform(0.6, 0.9)
                        else:
                            correlation = np.random.uniform(0.2, 0.6)
                    
                    correlation_matrix[project1][project2] = correlation
                    
                    if i != j:
                        correlation_sum += correlation
                        total_correlations += 1
            
            average_correlation = correlation_sum / max(1, total_correlations)
            
            return {
                'correlation_matrix': correlation_matrix,
                'average_correlation': average_correlation,
                'strongest_correlation': self._find_strongest_correlation(correlation_matrix),
                'weakest_correlation': self._find_weakest_correlation(correlation_matrix)
            }
            
        except Exception as e:
            logger.error(f"Error calculating correlations: {e}")
            return {'correlation_matrix': {}, 'average_correlation': 0.5}
    
    def _analyze_sector_performance(self, performance_data: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """Analyze performance by gaming sector"""
        
        sector_performance = {}
        
        for project_id, data in performance_data.items():
            category = self.gaming_projects[project_id]['category']
            
            if category not in sector_performance:
                sector_performance[category] = {
                    'projects': [],
                    'avg_price_change': 0,
                    'avg_volume_change': 0,
                    'total_market_cap': 0,
                    'avg_sentiment': 0
                }
            
            sector_performance[category]['projects'].append(project_id)
            sector_performance[category]['avg_price_change'] += data['price_change']
            sector_performance[category]['avg_volume_change'] += data['volume_change']
            sector_performance[category]['total_market_cap'] += data['market_cap']
            sector_performance[category]['avg_sentiment'] += data['social_sentiment']
        
        # Calculate averages
        for category, data in sector_performance.items():
            project_count = len(data['projects'])
            data['avg_price_change'] /= project_count
            data['avg_volume_change'] /= project_count
            data['avg_sentiment'] /= project_count
            data['market_dominance'] = data['total_market_cap'] / sum(
                perf['total_market_cap'] for perf in sector_performance.values()
            )
        
        return sector_performance
    
    def _identify_performance_indicators(self, performance_data: Dict[str, Dict[str, float]]) -> Dict[str, List[str]]:
        """Identify leading and lagging performance indicators"""
        
        # Sort projects by performance
        sorted_by_price = sorted(
            performance_data.items(),
            key=lambda x: x[1]['price_change'],
            reverse=True
        )
        
        sorted_by_volume = sorted(
            performance_data.items(),
            key=lambda x: x[1]['volume_change'],
            reverse=True
        )
        
        # Leading indicators (top performers)
        leading_indicators = [
            self.gaming_projects[project_id]['name']
            for project_id, _ in sorted_by_price[:2]
        ]
        
        # Lagging indicators (bottom performers)
        lagging_indicators = [
            self.gaming_projects[project_id]['name']
            for project_id, _ in sorted_by_price[-2:]
        ]
        
        return {
            'leading': leading_indicators,
            'lagging': lagging_indicators,
            'volume_leaders': [
                self.gaming_projects[project_id]['name']
                for project_id, _ in sorted_by_volume[:2]
            ]
        }
    
    def _analyze_cross_chain_performance(self, performance_data: Dict[str, Dict[str, float]]) -> Dict[str, Any]:
        """Analyze performance across different blockchains"""
        
        chain_performance = {}
        
        for project_id, data in performance_data.items():
            blockchain = self.gaming_projects[project_id]['blockchain']
            
            if blockchain not in chain_performance:
                chain_performance[blockchain] = {
                    'projects': [],
                    'avg_performance': 0,
                    'total_market_cap': 0
                }
            
            chain_performance[blockchain]['projects'].append(
                self.gaming_projects[project_id]['name']
            )
            chain_performance[blockchain]['avg_performance'] += data['price_change']
            chain_performance[blockchain]['total_market_cap'] += data['market_cap']
        
        # Calculate averages
        for blockchain, data in chain_performance.items():
            project_count = len(data['projects'])
            data['avg_performance'] /= project_count
        
        # Rank blockchains by performance
        ranked_chains = sorted(
            chain_performance.items(),
            key=lambda x: x[1]['avg_performance'],
            reverse=True
        )
        
        return {
            'chain_performance': chain_performance,
            'top_performing_chain': ranked_chains[0][0] if ranked_chains else None,
            'chain_rankings': [(chain, data['avg_performance']) for chain, data in ranked_chains]
        }
    
    def _find_strongest_correlation(self, correlation_matrix: Dict[str, Dict[str, float]]) -> Tuple[str, str, float]:
        """Find the strongest correlation pair"""
        
        strongest_corr = 0
        strongest_pair = ("", "")
        
        for project1, correlations in correlation_matrix.items():
            for project2, corr in correlations.items():
                if project1 != project2 and corr > strongest_corr:
                    strongest_corr = corr
                    strongest_pair = (
                        self.gaming_projects[project1]['name'],
                        self.gaming_projects[project2]['name']
                    )
        
        return (*strongest_pair, strongest_corr)
    
    def _find_weakest_correlation(self, correlation_matrix: Dict[str, Dict[str, float]]) -> Tuple[str, str, float]:
        """Find the weakest correlation pair"""
        
        weakest_corr = 1.0
        weakest_pair = ("", "")
        
        for project1, correlations in correlation_matrix.items():
            for project2, corr in correlations.items():
                if project1 != project2 and corr < weakest_corr:
                    weakest_corr = corr
                    weakest_pair = (
                        self.gaming_projects[project1]['name'],
                        self.gaming_projects[project2]['name']
                    )
        
        return (*weakest_pair, weakest_corr)
    
    def _get_fallback_analysis(self) -> Dict[str, Any]:
        """Fallback analysis when data fetching fails"""
        
        return {
            'timeframe': '24h',
            'correlations': {
                'correlation_matrix': {},
                'average_correlation': 0.5
            },
            'sector_performance': {
                'p2e': {'avg_price_change': 0.05, 'market_dominance': 0.3},
                'metaverse': {'avg_price_change': 0.03, 'market_dominance': 0.4},
                'nft-gaming': {'avg_price_change': 0.02, 'market_dominance': 0.2},
                'gaming-platform': {'avg_price_change': 0.04, 'market_dominance': 0.1}
            },
            'leading_indicators': ['Axie Infinity', 'Star Atlas'],
            'lagging_indicators': ['Gala Games'],
            'cross_chain_analysis': {
                'top_performing_chain': 'solana',
                'chain_rankings': [('solana', 0.05), ('ethereum', 0.03), ('ronin', 0.02)]
            },
            'analysis_timestamp': datetime.utcnow(),
            'note': 'Fallback data - external APIs unavailable'
        }


class InvestmentTracker:
    """Investment tracking system for gaming portfolios"""

    def __init__(self):
        self.tracked_projects = {}
        self.portfolio_metrics = {}
        self.alert_thresholds = {
            'price_surge': 0.15,  # 15% price increase
            'price_drop': -0.15,  # 15% price decrease
            'volume_spike': 2.0,  # 2x volume increase
            'sentiment_shift': 0.3  # 30% sentiment change
        }

    async def track_gaming_portfolio(self, portfolio: Dict[str, float]) -> Dict[str, Any]:
        """Track gaming portfolio performance"""

        try:
            portfolio_metrics = {}
            total_value = 0
            total_change_24h = 0

            for project_id, allocation in portfolio.items():
                if project_id in gaming_sector_analyzer.gaming_projects:
                    # Fetch current metrics (simulated)
                    metrics = await self._get_project_metrics(project_id)

                    # Calculate portfolio impact
                    position_value = allocation * metrics.current_price
                    position_change = position_value * (metrics.price_change_24h / 100)

                    total_value += position_value
                    total_change_24h += position_change

                    portfolio_metrics[project_id] = {
                        'allocation': allocation,
                        'current_value': position_value,
                        'change_24h': position_change,
                        'metrics': metrics
                    }

            # Calculate portfolio-level metrics
            portfolio_change_pct = (total_change_24h / total_value * 100) if total_value > 0 else 0

            # Risk assessment
            risk_assessment = self._assess_portfolio_risk(portfolio_metrics)

            # Generate recommendations
            recommendations = self._generate_portfolio_recommendations(portfolio_metrics)

            return {
                'portfolio_value': total_value,
                'change_24h': total_change_24h,
                'change_24h_pct': portfolio_change_pct,
                'positions': portfolio_metrics,
                'risk_assessment': risk_assessment,
                'recommendations': recommendations,
                'last_updated': datetime.utcnow()
            }

        except Exception as e:
            logger.error(f"Error tracking portfolio: {e}")
            return self._get_fallback_portfolio_data()

    async def _get_project_metrics(self, project_id: str) -> InvestmentMetrics:
        """Get current metrics for a gaming project"""

        project_info = gaming_sector_analyzer.gaming_projects[project_id]

        # Simulate realistic metrics
        base_price = 100 / project_info['market_cap_rank']  # Inverse rank pricing

        if ANALYTICS_AVAILABLE:
            price_change_24h = np.random.normal(0, 10)  # ±10% typical daily change
            price_change_7d = np.random.normal(0, 25)   # ±25% typical weekly change
            volume_24h = np.random.uniform(1000000, 50000000)  # $1M-$50M daily volume
            sentiment_score = np.random.uniform(0.2, 0.8)  # Sentiment 0.2-0.8
        else:
            price_change_24h = 2.5
            price_change_7d = 5.0
            volume_24h = 10000000
            sentiment_score = 0.6

        current_price = base_price * (1 + price_change_24h / 100)
        market_cap = current_price * 1000000  # Simulated circulating supply

        # Determine investment signal
        investment_signal = self._determine_investment_signal(
            price_change_24h, price_change_7d, sentiment_score
        )

        # Calculate risk score
        risk_score = self._calculate_risk_score(
            price_change_24h, price_change_7d, volume_24h, sentiment_score
        )

        return InvestmentMetrics(
            project_name=project_info['name'],
            current_price=current_price,
            price_change_24h=price_change_24h,
            price_change_7d=price_change_7d,
            volume_24h=volume_24h,
            market_cap=market_cap,
            sentiment_score=sentiment_score,
            investment_signal=investment_signal,
            risk_score=risk_score,
            last_updated=datetime.utcnow()
        )

    def _determine_investment_signal(self, price_24h: float, price_7d: float, sentiment: float) -> InvestmentSignal:
        """Determine investment signal based on metrics"""

        # Weighted scoring
        price_score = (price_24h + price_7d * 0.5) / 1.5
        sentiment_score = (sentiment - 0.5) * 100  # Convert to ±50 scale

        combined_score = price_score * 0.7 + sentiment_score * 0.3

        if combined_score > 15:
            return InvestmentSignal.STRONG_BUY
        elif combined_score > 5:
            return InvestmentSignal.BUY
        elif combined_score > -5:
            return InvestmentSignal.HOLD
        elif combined_score > -15:
            return InvestmentSignal.SELL
        else:
            return InvestmentSignal.STRONG_SELL

    def _calculate_risk_score(self, price_24h: float, price_7d: float, volume: float, sentiment: float) -> float:
        """Calculate risk score (0-1, higher = riskier)"""

        # Volatility risk
        volatility_risk = min(1.0, abs(price_24h) / 20 + abs(price_7d) / 50)

        # Liquidity risk (lower volume = higher risk)
        liquidity_risk = max(0, 1 - volume / 10000000)  # Risk increases below $10M volume

        # Sentiment risk
        sentiment_risk = abs(sentiment - 0.5) * 2  # Risk higher at extremes

        # Combined risk score
        risk_score = (volatility_risk * 0.4 + liquidity_risk * 0.3 + sentiment_risk * 0.3)
        return min(1.0, risk_score)

    def _assess_portfolio_risk(self, portfolio_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Assess overall portfolio risk"""

        if not portfolio_metrics:
            return {'overall_risk': 0.5, 'risk_factors': []}

        # Calculate weighted average risk
        total_value = sum(pos['current_value'] for pos in portfolio_metrics.values())
        weighted_risk = 0

        risk_factors = []

        for project_id, position in portfolio_metrics.items():
            weight = position['current_value'] / total_value
            project_risk = position['metrics'].risk_score
            weighted_risk += weight * project_risk

            # Identify specific risk factors
            if project_risk > 0.7:
                risk_factors.append(f"High volatility in {position['metrics'].project_name}")

            if position['metrics'].price_change_24h < -10:
                risk_factors.append(f"Significant decline in {position['metrics'].project_name}")

        # Concentration risk
        max_allocation = max(pos['allocation'] for pos in portfolio_metrics.values())
        if max_allocation > 0.5:
            risk_factors.append("High concentration risk - consider diversification")

        return {
            'overall_risk': weighted_risk,
            'risk_level': 'Low' if weighted_risk < 0.3 else 'Medium' if weighted_risk < 0.7 else 'High',
            'risk_factors': risk_factors,
            'diversification_score': 1 - max_allocation
        }

    def _generate_portfolio_recommendations(self, portfolio_metrics: Dict[str, Any]) -> List[str]:
        """Generate portfolio recommendations"""

        recommendations = []

        for project_id, position in portfolio_metrics.items():
            metrics = position['metrics']

            if metrics.investment_signal == InvestmentSignal.STRONG_BUY:
                recommendations.append(f"Consider increasing position in {metrics.project_name}")
            elif metrics.investment_signal == InvestmentSignal.STRONG_SELL:
                recommendations.append(f"Consider reducing position in {metrics.project_name}")

            if metrics.risk_score > 0.8:
                recommendations.append(f"Monitor {metrics.project_name} closely - high risk detected")

        return recommendations

    def _get_fallback_portfolio_data(self) -> Dict[str, Any]:
        """Fallback portfolio data when tracking fails"""

        return {
            'portfolio_value': 100000,
            'change_24h': 2500,
            'change_24h_pct': 2.5,
            'positions': {},
            'risk_assessment': {
                'overall_risk': 0.5,
                'risk_level': 'Medium',
                'risk_factors': ['External data unavailable'],
                'diversification_score': 0.8
            },
            'recommendations': ['Unable to generate recommendations - data unavailable'],
            'last_updated': datetime.utcnow()
        }


class MarketAlertSystem:
    """Market alert and notification system"""

    def __init__(self):
        self.active_alerts = []
        self.alert_history = []
        self.notification_channels = ['email', 'webhook', 'dashboard']

    async def monitor_market_conditions(self, projects: List[str]) -> List[MarketAlert]:
        """Monitor market conditions and generate alerts"""

        new_alerts = []

        try:
            for project_id in projects:
                if project_id in gaming_sector_analyzer.gaming_projects:
                    # Get current metrics
                    metrics = await investment_tracker._get_project_metrics(project_id)

                    # Check for alert conditions
                    alerts = self._check_alert_conditions(project_id, metrics)
                    new_alerts.extend(alerts)

            # Add to active alerts
            self.active_alerts.extend(new_alerts)

            # Clean up old alerts
            self._cleanup_old_alerts()

            return new_alerts

        except Exception as e:
            logger.error(f"Error monitoring market conditions: {e}")
            return []

    def _check_alert_conditions(self, project_id: str, metrics: InvestmentMetrics) -> List[MarketAlert]:
        """Check for alert conditions"""

        alerts = []
        project_name = metrics.project_name

        # Price surge alert
        if metrics.price_change_24h > 15:
            alerts.append(MarketAlert(
                alert_type=AlertType.PRICE_SURGE,
                project_name=project_name,
                message=f"{project_name} price surged {metrics.price_change_24h:.1f}% in 24h",
                severity="high" if metrics.price_change_24h > 25 else "medium",
                data={'price_change': metrics.price_change_24h, 'current_price': metrics.current_price},
                timestamp=datetime.utcnow()
            ))

        # Price drop alert
        if metrics.price_change_24h < -15:
            alerts.append(MarketAlert(
                alert_type=AlertType.PRICE_DROP,
                project_name=project_name,
                message=f"{project_name} price dropped {abs(metrics.price_change_24h):.1f}% in 24h",
                severity="high" if metrics.price_change_24h < -25 else "medium",
                data={'price_change': metrics.price_change_24h, 'current_price': metrics.current_price},
                timestamp=datetime.utcnow()
            ))

        # Volume spike alert
        if metrics.volume_24h > 20000000:  # $20M threshold
            alerts.append(MarketAlert(
                alert_type=AlertType.VOLUME_SPIKE,
                project_name=project_name,
                message=f"{project_name} experiencing high trading volume: ${metrics.volume_24h:,.0f}",
                severity="medium",
                data={'volume_24h': metrics.volume_24h},
                timestamp=datetime.utcnow()
            ))

        # Sentiment alert
        if metrics.sentiment_score < 0.3:
            alerts.append(MarketAlert(
                alert_type=AlertType.NEWS_SENTIMENT,
                project_name=project_name,
                message=f"{project_name} showing negative sentiment: {metrics.sentiment_score:.2f}",
                severity="medium",
                data={'sentiment_score': metrics.sentiment_score},
                timestamp=datetime.utcnow()
            ))
        elif metrics.sentiment_score > 0.8:
            alerts.append(MarketAlert(
                alert_type=AlertType.NEWS_SENTIMENT,
                project_name=project_name,
                message=f"{project_name} showing very positive sentiment: {metrics.sentiment_score:.2f}",
                severity="low",
                data={'sentiment_score': metrics.sentiment_score},
                timestamp=datetime.utcnow()
            ))

        return alerts

    def _cleanup_old_alerts(self):
        """Remove old inactive alerts"""

        cutoff_time = datetime.utcnow() - timedelta(hours=24)

        # Move old alerts to history
        old_alerts = [alert for alert in self.active_alerts if alert.timestamp < cutoff_time]
        self.alert_history.extend(old_alerts)

        # Keep only recent active alerts
        self.active_alerts = [alert for alert in self.active_alerts if alert.timestamp >= cutoff_time]

        # Limit history size
        if len(self.alert_history) > 1000:
            self.alert_history = self.alert_history[-1000:]

    def get_active_alerts(self, severity: Optional[str] = None) -> List[MarketAlert]:
        """Get active alerts, optionally filtered by severity"""

        if severity:
            return [alert for alert in self.active_alerts if alert.severity == severity]
        return self.active_alerts

    def get_alert_summary(self) -> Dict[str, Any]:
        """Get summary of alert activity"""

        total_alerts = len(self.active_alerts)
        severity_counts = {}

        for alert in self.active_alerts:
            severity_counts[alert.severity] = severity_counts.get(alert.severity, 0) + 1

        alert_types = {}
        for alert in self.active_alerts:
            alert_types[alert.alert_type.value] = alert_types.get(alert.alert_type.value, 0) + 1

        return {
            'total_active_alerts': total_alerts,
            'severity_breakdown': severity_counts,
            'alert_type_breakdown': alert_types,
            'most_recent_alert': self.active_alerts[-1].timestamp if self.active_alerts else None,
            'summary_timestamp': datetime.utcnow()
        }


# Global instances
gaming_sector_analyzer = GamingSectorAnalyzer()
investment_tracker = InvestmentTracker()
market_alert_system = MarketAlertSystem()
