"""
Advanced Content Intelligence and Market Analytics for Web3 Gaming
Phase 7: Content Intelligence and Market Analytics Implementation
"""
import logging
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
from enum import Enum
import asyncio
import numpy as np

try:
    from textblob import TextBlob
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.naive_bayes import MultinomialNB
    from sklearn.pipeline import Pipeline
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import classification_report
    import joblib
    NLP_AVAILABLE = True
except ImportError:
    NLP_AVAILABLE = False

logger = logging.getLogger(__name__)


class GamingContentCategory(Enum):
    """Enhanced gaming content categories"""
    P2E = "play-to-earn"
    NFT_GAMING = "nft-gaming"
    DEFI_GAMING = "defi-gaming"
    METAVERSE = "metaverse"
    GAMEFI = "gamefi"
    VIRTUAL_WORLDS = "virtual-worlds"
    BLOCKCHAIN_GAMES = "blockchain-games"
    CRYPTO_COLLECTIBLES = "crypto-collectibles"
    GAMING_INFRASTRUCTURE = "gaming-infrastructure"
    ESPORTS_CRYPTO = "esports-crypto"
    GENERAL_GAMING = "general-gaming"


class SentimentCategory(Enum):
    """Sentiment categories for gaming content"""
    VERY_POSITIVE = "very_positive"
    POSITIVE = "positive"
    NEUTRAL = "neutral"
    NEGATIVE = "negative"
    VERY_NEGATIVE = "very_negative"


@dataclass
class ContentClassificationResult:
    """Result of content classification analysis"""
    primary_category: GamingContentCategory
    category_confidence: float
    all_categories: Dict[GamingContentCategory, float]
    sentiment_score: float
    sentiment_category: SentimentCategory
    gaming_entities: List[str]
    blockchain_networks: List[str]
    market_signals: Dict[str, Any]
    trend_indicators: Dict[str, float]
    classification_timestamp: datetime


@dataclass
class MarketIntelligenceResult:
    """Result of market intelligence analysis"""
    market_sentiment: float
    trend_direction: str
    volatility_score: float
    correlation_signals: Dict[str, float]
    investment_signals: Dict[str, Any]
    competitive_analysis: Dict[str, Any]
    risk_assessment: Dict[str, float]
    analysis_timestamp: datetime


class AdvancedGamingContentClassifier:
    """Advanced ML-based gaming content classifier"""
    
    def __init__(self):
        self.model = None
        self.vectorizer = None
        self.is_trained = False
        
        # Enhanced gaming category patterns
        self.category_patterns = {
            GamingContentCategory.P2E: [
                r'\b(play.?to.?earn|p2e|earn.?while.?playing|gaming.?rewards|token.?rewards)\b',
                r'\b(daily.?rewards|quest.?rewards|battle.?rewards|farming.?tokens)\b',
                r'\b(scholarship|guild.?system|yield.?farming.?game)\b'
            ],
            GamingContentCategory.NFT_GAMING: [
                r'\b(nft.?game|gaming.?nft|collectible.?game|digital.?assets)\b',
                r'\b(character.?nft|weapon.?nft|land.?nft|breeding.?nft)\b',
                r'\b(opensea.?game|nft.?marketplace.?game|rare.?items)\b'
            ],
            GamingContentCategory.DEFI_GAMING: [
                r'\b(defi.?game|gaming.?defi|liquidity.?mining.?game|yield.?game)\b',
                r'\b(staking.?game|lending.?protocol.?game|amm.?game)\b',
                r'\b(governance.?token.?game|dao.?game|protocol.?rewards)\b'
            ],
            GamingContentCategory.METAVERSE: [
                r'\b(metaverse|virtual.?world|3d.?world|immersive.?experience)\b',
                r'\b(virtual.?reality|vr.?game|augmented.?reality|ar.?game)\b',
                r'\b(digital.?land|virtual.?property|metaverse.?platform)\b'
            ],
            GamingContentCategory.GAMEFI: [
                r'\b(gamefi|game.?finance|gaming.?finance|financial.?game)\b',
                r'\b(crypto.?game.?economy|tokenized.?gaming|blockchain.?economy)\b'
            ],
            GamingContentCategory.VIRTUAL_WORLDS: [
                r'\b(sandbox|decentraland|horizon.?worlds|virtual.?space)\b',
                r'\b(world.?building|user.?generated.?content|social.?world)\b'
            ],
            GamingContentCategory.GAMING_INFRASTRUCTURE: [
                r'\b(gaming.?infrastructure|blockchain.?gaming.?platform|gaming.?sdk)\b',
                r'\b(gaming.?engine|developer.?tools|gaming.?api)\b'
            ],
            GamingContentCategory.ESPORTS_CRYPTO: [
                r'\b(esports.?crypto|competitive.?gaming.?crypto|tournament.?rewards)\b',
                r'\b(esports.?betting|gaming.?competition|prize.?pool)\b'
            ]
        }
        
        # Market signal patterns
        self.market_signal_patterns = {
            'bullish': [
                r'\b(bullish|moon|pump|surge|rally|breakout|all.?time.?high|ath)\b',
                r'\b(massive.?gains|explosive.?growth|skyrocket|parabolic)\b'
            ],
            'bearish': [
                r'\b(bearish|dump|crash|plummet|collapse|bear.?market|correction)\b',
                r'\b(massive.?losses|steep.?decline|free.?fall|capitulation)\b'
            ],
            'neutral': [
                r'\b(sideways|consolidation|range.?bound|stable|steady)\b'
            ]
        }
        
        # Initialize training data
        self._initialize_training_data()
    
    def _initialize_training_data(self):
        """Initialize training data for the classifier"""
        self.training_data = [
            # P2E examples
            ("Players can earn tokens by completing daily quests and battles in this play-to-earn game", GamingContentCategory.P2E),
            ("New scholarship program allows players to earn while playing without initial investment", GamingContentCategory.P2E),
            ("Guild system enables shared rewards and collaborative gameplay for maximum earnings", GamingContentCategory.P2E),
            
            # NFT Gaming examples
            ("Rare character NFTs with unique abilities can be traded on the marketplace", GamingContentCategory.NFT_GAMING),
            ("Breeding system allows players to create new NFT creatures with enhanced stats", GamingContentCategory.NFT_GAMING),
            ("Land NFTs provide passive income and can be developed with buildings", GamingContentCategory.NFT_GAMING),
            
            # DeFi Gaming examples
            ("Stake your gaming tokens to earn yield while participating in governance", GamingContentCategory.DEFI_GAMING),
            ("Liquidity mining rewards available for providing tokens to the game's AMM", GamingContentCategory.DEFI_GAMING),
            ("DAO governance allows players to vote on game mechanics and token distribution", GamingContentCategory.DEFI_GAMING),
            
            # Metaverse examples
            ("Immersive virtual world where players can build, explore, and socialize", GamingContentCategory.METAVERSE),
            ("VR-enabled metaverse platform with realistic physics and social interactions", GamingContentCategory.METAVERSE),
            ("Digital land ownership in expansive 3D virtual worlds", GamingContentCategory.METAVERSE),
            
            # GameFi examples
            ("Revolutionary GameFi platform combining gaming with decentralized finance", GamingContentCategory.GAMEFI),
            ("Game finance protocols enabling new economic models for gaming", GamingContentCategory.GAMEFI),
            
            # Infrastructure examples
            ("New blockchain gaming SDK enables developers to build Web3 games easily", GamingContentCategory.GAMING_INFRASTRUCTURE),
            ("Gaming infrastructure platform provides tools for blockchain game development", GamingContentCategory.GAMING_INFRASTRUCTURE),
            
            # General gaming (negative examples)
            ("Traditional mobile game with in-app purchases and ads", GamingContentCategory.GENERAL_GAMING),
            ("Console gaming news about upcoming AAA releases", GamingContentCategory.GENERAL_GAMING),
        ]
    
    async def train_classifier(self):
        """Train the content classification model"""
        if not NLP_AVAILABLE:
            logger.warning("NLP libraries not available. Using pattern-based classification.")
            return
        
        try:
            # Prepare training data
            texts = [text for text, _ in self.training_data]
            labels = [category.value for _, category in self.training_data]
            
            # Create pipeline
            self.model = Pipeline([
                ('tfidf', TfidfVectorizer(
                    max_features=5000,
                    ngram_range=(1, 3),
                    stop_words='english',
                    lowercase=True
                )),
                ('classifier', MultinomialNB(alpha=0.1))
            ])
            
            # Train model
            self.model.fit(texts, labels)
            self.is_trained = True
            
            logger.info("Gaming content classifier trained successfully")
            
        except Exception as e:
            logger.error(f"Error training classifier: {e}")
            self.is_trained = False
    
    def classify_content(self, title: str, content: str = "", summary: str = "") -> ContentClassificationResult:
        """Classify gaming content with enhanced analysis"""
        full_text = f"{title} {content} {summary}".strip().lower()
        
        # Get category classification
        if self.is_trained and self.model:
            category_scores = self._ml_classify(full_text)
        else:
            category_scores = self._pattern_classify(full_text)
        
        # Get primary category
        primary_category = max(category_scores.items(), key=lambda x: x[1])[0]
        primary_confidence = category_scores[primary_category]
        
        # Sentiment analysis
        sentiment_score = self._analyze_sentiment(full_text)
        sentiment_category = self._categorize_sentiment(sentiment_score)
        
        # Entity extraction
        gaming_entities = self._extract_gaming_entities(full_text)
        blockchain_networks = self._extract_blockchain_networks(full_text)
        
        # Market signals
        market_signals = self._extract_market_signals(full_text)
        
        # Trend indicators
        trend_indicators = self._calculate_trend_indicators(full_text, market_signals)
        
        return ContentClassificationResult(
            primary_category=primary_category,
            category_confidence=primary_confidence,
            all_categories=category_scores,
            sentiment_score=sentiment_score,
            sentiment_category=sentiment_category,
            gaming_entities=gaming_entities,
            blockchain_networks=blockchain_networks,
            market_signals=market_signals,
            trend_indicators=trend_indicators,
            classification_timestamp=datetime.utcnow()
        )
    
    def _ml_classify(self, text: str) -> Dict[GamingContentCategory, float]:
        """ML-based classification"""
        try:
            # Get prediction probabilities
            probabilities = self.model.predict_proba([text])[0]
            classes = self.model.classes_
            
            # Convert to category scores
            category_scores = {}
            for category in GamingContentCategory:
                if category.value in classes:
                    idx = list(classes).index(category.value)
                    category_scores[category] = float(probabilities[idx])
                else:
                    category_scores[category] = 0.0
            
            return category_scores
            
        except Exception as e:
            logger.error(f"Error in ML classification: {e}")
            return self._pattern_classify(text)
    
    def _pattern_classify(self, text: str) -> Dict[GamingContentCategory, float]:
        """Pattern-based classification fallback"""
        category_scores = {}
        
        for category, patterns in self.category_patterns.items():
            score = 0.0
            for pattern in patterns:
                matches = len(re.findall(pattern, text, re.IGNORECASE))
                score += matches * 0.2  # Each match adds 0.2 to score
            
            # Normalize score
            category_scores[category] = min(1.0, score)
        
        # Ensure all categories are represented
        for category in GamingContentCategory:
            if category not in category_scores:
                category_scores[category] = 0.0
        
        # If no strong signals, default to general gaming
        if max(category_scores.values()) < 0.1:
            category_scores[GamingContentCategory.GENERAL_GAMING] = 0.5
        
        return category_scores
    
    def _analyze_sentiment(self, text: str) -> float:
        """Analyze sentiment of the content"""
        if not NLP_AVAILABLE:
            return 0.0
        
        try:
            blob = TextBlob(text)
            return blob.sentiment.polarity
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return 0.0
    
    def _categorize_sentiment(self, score: float) -> SentimentCategory:
        """Categorize sentiment score"""
        if score >= 0.6:
            return SentimentCategory.VERY_POSITIVE
        elif score >= 0.2:
            return SentimentCategory.POSITIVE
        elif score >= -0.2:
            return SentimentCategory.NEUTRAL
        elif score >= -0.6:
            return SentimentCategory.NEGATIVE
        else:
            return SentimentCategory.VERY_NEGATIVE
    
    def _extract_gaming_entities(self, text: str) -> List[str]:
        """Extract gaming-related entities"""
        gaming_entities = []
        
        # Common gaming entities patterns
        entity_patterns = [
            r'\b([A-Z][a-z]+\s+(?:Game|Games|Gaming|Protocol|Network|Platform|DAO|Guild))\b',
            r'\b([A-Z]{2,}\s+(?:token|coin|nft|protocol))\b',
            r'\b(Axie\s+Infinity|Star\s+Atlas|Decentraland|The\s+Sandbox|Gala\s+Games)\b'
        ]
        
        for pattern in entity_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            gaming_entities.extend(matches)
        
        return list(set(gaming_entities))
    
    def _extract_blockchain_networks(self, text: str) -> List[str]:
        """Extract mentioned blockchain networks"""
        networks = []
        
        network_patterns = {
            'ethereum': r'\b(ethereum|eth|erc.?20|erc.?721|erc.?1155)\b',
            'solana': r'\b(solana|sol|spl.?token)\b',
            'polygon': r'\b(polygon|matic|polygonscan)\b',
            'bsc': r'\b(bsc|binance\s+smart\s+chain|bnb\s+chain)\b',
            'avalanche': r'\b(avalanche|avax|c.?chain)\b',
            'ronin': r'\b(ronin|ron\s+network)\b',
            'arbitrum': r'\b(arbitrum|arb)\b',
            'optimism': r'\b(optimism|op\s+mainnet)\b'
        }
        
        for network, pattern in network_patterns.items():
            if re.search(pattern, text, re.IGNORECASE):
                networks.append(network)
        
        return networks
    
    def _extract_market_signals(self, text: str) -> Dict[str, Any]:
        """Extract market signals from content"""
        signals = {
            'bullish_signals': 0,
            'bearish_signals': 0,
            'neutral_signals': 0,
            'price_mentions': [],
            'volume_mentions': [],
            'market_cap_mentions': []
        }
        
        # Count signal patterns
        for signal_type, patterns in self.market_signal_patterns.items():
            count = 0
            for pattern in patterns:
                count += len(re.findall(pattern, text, re.IGNORECASE))
            signals[f'{signal_type}_signals'] = count
        
        # Extract price mentions
        price_pattern = r'\$[\d,]+\.?\d*|\d+\.?\d*\s*(?:usd|dollars?|cents?)'
        signals['price_mentions'] = re.findall(price_pattern, text, re.IGNORECASE)
        
        return signals
    
    def _calculate_trend_indicators(self, text: str, market_signals: Dict[str, Any]) -> Dict[str, float]:
        """Calculate trend indicators"""
        indicators = {}
        
        # Market sentiment indicator
        bullish = market_signals.get('bullish_signals', 0)
        bearish = market_signals.get('bearish_signals', 0)
        total_signals = bullish + bearish
        
        if total_signals > 0:
            indicators['market_sentiment_trend'] = (bullish - bearish) / total_signals
        else:
            indicators['market_sentiment_trend'] = 0.0
        
        # Innovation indicator (mentions of new tech/features)
        innovation_keywords = ['new', 'innovative', 'revolutionary', 'breakthrough', 'first', 'launch']
        innovation_count = sum(len(re.findall(rf'\b{keyword}\b', text, re.IGNORECASE)) for keyword in innovation_keywords)
        indicators['innovation_trend'] = min(1.0, innovation_count * 0.1)
        
        # Adoption indicator
        adoption_keywords = ['adoption', 'users', 'players', 'community', 'growth', 'expansion']
        adoption_count = sum(len(re.findall(rf'\b{keyword}\b', text, re.IGNORECASE)) for keyword in adoption_keywords)
        indicators['adoption_trend'] = min(1.0, adoption_count * 0.1)
        
        return indicators


class SentimentScoringEngine:
    """Advanced sentiment scoring for gaming community content"""

    def __init__(self):
        self.sentiment_weights = {
            'gaming_positive': ['fun', 'exciting', 'amazing', 'awesome', 'love', 'great', 'fantastic'],
            'gaming_negative': ['boring', 'terrible', 'hate', 'awful', 'broken', 'scam', 'waste'],
            'market_positive': ['moon', 'pump', 'bullish', 'gains', 'profit', 'surge', 'rally'],
            'market_negative': ['dump', 'crash', 'bearish', 'loss', 'rekt', 'rug', 'collapse'],
            'community_positive': ['community', 'together', 'support', 'help', 'share', 'collaborate'],
            'community_negative': ['toxic', 'drama', 'fight', 'argue', 'split', 'abandon']
        }

        self.sentiment_modifiers = {
            'intensifiers': ['very', 'extremely', 'incredibly', 'absolutely', 'totally'],
            'diminishers': ['somewhat', 'slightly', 'barely', 'hardly', 'maybe']
        }

    def analyze_gaming_sentiment(self, content: str, context: str = "general") -> Dict[str, Any]:
        """Comprehensive gaming sentiment analysis"""

        # Base sentiment using TextBlob
        base_sentiment = self._get_base_sentiment(content)

        # Gaming-specific sentiment adjustments
        gaming_sentiment = self._calculate_gaming_sentiment(content)

        # Market sentiment analysis
        market_sentiment = self._calculate_market_sentiment(content)

        # Community sentiment analysis
        community_sentiment = self._calculate_community_sentiment(content)

        # Temporal sentiment (if timestamps available)
        temporal_factors = self._analyze_temporal_factors(content)

        # Combine all sentiment scores
        combined_sentiment = self._combine_sentiment_scores(
            base_sentiment, gaming_sentiment, market_sentiment,
            community_sentiment, temporal_factors
        )

        return {
            'overall_sentiment': combined_sentiment,
            'base_sentiment': base_sentiment,
            'gaming_sentiment': gaming_sentiment,
            'market_sentiment': market_sentiment,
            'community_sentiment': community_sentiment,
            'sentiment_confidence': self._calculate_sentiment_confidence(content),
            'sentiment_category': self._categorize_sentiment(combined_sentiment),
            'key_sentiment_drivers': self._identify_sentiment_drivers(content),
            'analysis_timestamp': datetime.utcnow()
        }

    def _get_base_sentiment(self, content: str) -> float:
        """Get base sentiment using TextBlob"""
        if not NLP_AVAILABLE:
            return 0.0

        try:
            blob = TextBlob(content)
            return blob.sentiment.polarity
        except:
            return 0.0

    def _calculate_gaming_sentiment(self, content: str) -> float:
        """Calculate gaming-specific sentiment"""
        content_lower = content.lower()

        positive_score = sum(
            content_lower.count(word) for word in self.sentiment_weights['gaming_positive']
        )
        negative_score = sum(
            content_lower.count(word) for word in self.sentiment_weights['gaming_negative']
        )

        # Apply modifiers
        intensifier_count = sum(
            content_lower.count(word) for word in self.sentiment_modifiers['intensifiers']
        )
        diminisher_count = sum(
            content_lower.count(word) for word in self.sentiment_modifiers['diminishers']
        )

        # Calculate weighted sentiment
        net_score = positive_score - negative_score
        modifier_effect = (intensifier_count * 0.2) - (diminisher_count * 0.1)

        gaming_sentiment = (net_score + modifier_effect) / max(1, len(content.split()) / 10)
        return max(-1.0, min(1.0, gaming_sentiment))

    def _calculate_market_sentiment(self, content: str) -> float:
        """Calculate market-specific sentiment"""
        content_lower = content.lower()

        positive_score = sum(
            content_lower.count(word) for word in self.sentiment_weights['market_positive']
        )
        negative_score = sum(
            content_lower.count(word) for word in self.sentiment_weights['market_negative']
        )

        net_score = positive_score - negative_score
        market_sentiment = net_score / max(1, len(content.split()) / 10)
        return max(-1.0, min(1.0, market_sentiment))

    def _calculate_community_sentiment(self, content: str) -> float:
        """Calculate community-specific sentiment"""
        content_lower = content.lower()

        positive_score = sum(
            content_lower.count(word) for word in self.sentiment_weights['community_positive']
        )
        negative_score = sum(
            content_lower.count(word) for word in self.sentiment_weights['community_negative']
        )

        net_score = positive_score - negative_score
        community_sentiment = net_score / max(1, len(content.split()) / 10)
        return max(-1.0, min(1.0, community_sentiment))

    def _analyze_temporal_factors(self, content: str) -> Dict[str, float]:
        """Analyze temporal sentiment factors"""
        temporal_keywords = {
            'urgency': ['urgent', 'now', 'immediately', 'asap', 'quickly'],
            'future_positive': ['soon', 'upcoming', 'next', 'future', 'roadmap'],
            'past_negative': ['failed', 'delayed', 'cancelled', 'postponed', 'missed']
        }

        factors = {}
        content_lower = content.lower()

        for factor, keywords in temporal_keywords.items():
            score = sum(content_lower.count(word) for word in keywords)
            factors[factor] = min(1.0, score * 0.1)

        return factors

    def _combine_sentiment_scores(self, base: float, gaming: float, market: float,
                                 community: float, temporal: Dict[str, float]) -> float:
        """Combine all sentiment scores with weights"""

        # Weighted combination
        combined = (
            base * 0.3 +           # Base sentiment weight
            gaming * 0.25 +        # Gaming sentiment weight
            market * 0.25 +        # Market sentiment weight
            community * 0.2        # Community sentiment weight
        )

        # Apply temporal adjustments
        temporal_adjustment = (
            temporal.get('urgency', 0) * 0.1 +
            temporal.get('future_positive', 0) * 0.05 -
            temporal.get('past_negative', 0) * 0.05
        )

        combined += temporal_adjustment
        return max(-1.0, min(1.0, combined))

    def _calculate_sentiment_confidence(self, content: str) -> float:
        """Calculate confidence in sentiment analysis"""

        # Factors that increase confidence
        word_count = len(content.split())
        sentiment_word_count = 0

        all_sentiment_words = []
        for words in self.sentiment_weights.values():
            all_sentiment_words.extend(words)

        content_lower = content.lower()
        sentiment_word_count = sum(
            content_lower.count(word) for word in all_sentiment_words
        )

        # Confidence based on content length and sentiment word density
        length_factor = min(1.0, word_count / 50)  # More confident with longer content
        density_factor = min(1.0, sentiment_word_count / max(1, word_count / 10))

        confidence = (length_factor + density_factor) / 2
        return confidence

    def _categorize_sentiment(self, score: float) -> SentimentCategory:
        """Categorize sentiment score"""
        if score >= 0.6:
            return SentimentCategory.VERY_POSITIVE
        elif score >= 0.2:
            return SentimentCategory.POSITIVE
        elif score >= -0.2:
            return SentimentCategory.NEUTRAL
        elif score >= -0.6:
            return SentimentCategory.NEGATIVE
        else:
            return SentimentCategory.VERY_NEGATIVE

    def _identify_sentiment_drivers(self, content: str) -> List[str]:
        """Identify key words/phrases driving sentiment"""
        drivers = []
        content_lower = content.lower()

        # Find sentiment words present in content
        for category, words in self.sentiment_weights.items():
            for word in words:
                if word in content_lower:
                    drivers.append(f"{word} ({category})")

        return drivers[:10]  # Return top 10 drivers


class TrendDetectionEngine:
    """Market intelligence and trend detection for gaming content"""

    def __init__(self):
        self.trend_indicators = {
            'adoption_keywords': ['adoption', 'users', 'players', 'growth', 'expansion', 'mainstream'],
            'innovation_keywords': ['new', 'innovative', 'breakthrough', 'revolutionary', 'first', 'launch'],
            'partnership_keywords': ['partnership', 'collaboration', 'integration', 'alliance', 'merger'],
            'funding_keywords': ['funding', 'investment', 'raise', 'series', 'valuation', 'ipo'],
            'regulation_keywords': ['regulation', 'compliance', 'legal', 'sec', 'government', 'policy']
        }

        self.market_cycle_indicators = {
            'bull_market': ['bull', 'rally', 'surge', 'breakout', 'ath', 'moon'],
            'bear_market': ['bear', 'crash', 'dump', 'correction', 'capitulation', 'bottom'],
            'accumulation': ['accumulation', 'buying', 'dip', 'support', 'oversold'],
            'distribution': ['distribution', 'selling', 'resistance', 'overbought', 'profit-taking']
        }

    def detect_trends(self, content_batch: List[str], timeframe: str = "24h") -> Dict[str, Any]:
        """Detect trends from a batch of content"""

        # Combine all content
        combined_content = " ".join(content_batch).lower()

        # Analyze trend indicators
        trend_scores = self._calculate_trend_scores(combined_content)

        # Detect market cycle phase
        market_phase = self._detect_market_phase(combined_content)

        # Calculate trend momentum
        momentum = self._calculate_trend_momentum(content_batch)

        # Identify emerging themes
        emerging_themes = self._identify_emerging_themes(combined_content)

        # Correlation analysis
        correlations = self._analyze_correlations(content_batch)

        return {
            'trend_scores': trend_scores,
            'market_phase': market_phase,
            'trend_momentum': momentum,
            'emerging_themes': emerging_themes,
            'correlations': correlations,
            'timeframe': timeframe,
            'analysis_timestamp': datetime.utcnow()
        }

    def _calculate_trend_scores(self, content: str) -> Dict[str, float]:
        """Calculate scores for different trend indicators"""
        scores = {}

        for trend_type, keywords in self.trend_indicators.items():
            score = sum(content.count(keyword) for keyword in keywords)
            # Normalize by content length
            normalized_score = score / max(1, len(content.split()) / 1000)
            scores[trend_type] = min(1.0, normalized_score)

        return scores

    def _detect_market_phase(self, content: str) -> Dict[str, Any]:
        """Detect current market cycle phase"""
        phase_scores = {}

        for phase, keywords in self.market_cycle_indicators.items():
            score = sum(content.count(keyword) for keyword in keywords)
            phase_scores[phase] = score

        # Determine dominant phase
        if phase_scores:
            dominant_phase = max(phase_scores.items(), key=lambda x: x[1])
            confidence = dominant_phase[1] / max(1, sum(phase_scores.values()))
        else:
            dominant_phase = ('neutral', 0)
            confidence = 0.0

        return {
            'dominant_phase': dominant_phase[0],
            'confidence': confidence,
            'all_phases': phase_scores
        }

    def _calculate_trend_momentum(self, content_batch: List[str]) -> Dict[str, float]:
        """Calculate trend momentum over time"""
        if len(content_batch) < 2:
            return {'momentum': 0.0, 'direction': 'neutral'}

        # Simple momentum calculation based on sentiment progression
        sentiments = []
        for content in content_batch:
            if NLP_AVAILABLE:
                try:
                    blob = TextBlob(content)
                    sentiments.append(blob.sentiment.polarity)
                except:
                    sentiments.append(0.0)
            else:
                sentiments.append(0.0)

        # Calculate momentum as change in sentiment
        if len(sentiments) >= 2:
            momentum = sentiments[-1] - sentiments[0]
            direction = 'positive' if momentum > 0.1 else 'negative' if momentum < -0.1 else 'neutral'
        else:
            momentum = 0.0
            direction = 'neutral'

        return {
            'momentum': momentum,
            'direction': direction,
            'sentiment_progression': sentiments
        }

    def _identify_emerging_themes(self, content: str) -> List[Dict[str, Any]]:
        """Identify emerging themes in content"""

        # Extract potential themes using simple keyword frequency
        words = content.split()
        word_freq = {}

        # Filter for meaningful words (length > 3, not common words)
        common_words = {'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use'}

        for word in words:
            clean_word = re.sub(r'[^\w]', '', word.lower())
            if len(clean_word) > 3 and clean_word not in common_words:
                word_freq[clean_word] = word_freq.get(clean_word, 0) + 1

        # Get top themes
        sorted_themes = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]

        themes = []
        for theme, frequency in sorted_themes:
            themes.append({
                'theme': theme,
                'frequency': frequency,
                'relevance_score': min(1.0, frequency / max(1, len(words) / 100))
            })

        return themes

    def _analyze_correlations(self, content_batch: List[str]) -> Dict[str, Any]:
        """Analyze correlations between different content pieces"""

        if len(content_batch) < 2:
            return {'correlation_strength': 0.0, 'common_themes': []}

        # Simple correlation based on common words
        all_words = set()
        content_words = []

        for content in content_batch:
            words = set(re.findall(r'\b\w{4,}\b', content.lower()))
            content_words.append(words)
            all_words.update(words)

        # Calculate Jaccard similarity between content pieces
        similarities = []
        for i in range(len(content_words)):
            for j in range(i + 1, len(content_words)):
                intersection = len(content_words[i] & content_words[j])
                union = len(content_words[i] | content_words[j])
                similarity = intersection / max(1, union)
                similarities.append(similarity)

        avg_correlation = sum(similarities) / max(1, len(similarities))

        # Find common themes across all content
        common_words = all_words
        for words in content_words:
            common_words &= words

        return {
            'correlation_strength': avg_correlation,
            'common_themes': list(common_words)[:10],
            'similarity_scores': similarities
        }


class MarketIntelligenceEngine:
    """Advanced market intelligence for gaming sector analysis"""

    def __init__(self):
        self.sector_weights = {
            'p2e_gaming': 0.25,
            'nft_gaming': 0.20,
            'defi_gaming': 0.15,
            'metaverse': 0.20,
            'gaming_infrastructure': 0.10,
            'esports_crypto': 0.10
        }

        self.risk_factors = {
            'regulatory_risk': ['regulation', 'ban', 'illegal', 'compliance', 'sec'],
            'technical_risk': ['hack', 'exploit', 'bug', 'vulnerability', 'security'],
            'market_risk': ['volatility', 'crash', 'bubble', 'manipulation', 'liquidity'],
            'adoption_risk': ['adoption', 'users', 'retention', 'engagement', 'churn']
        }

        self.investment_signals = {
            'strong_buy': ['breakthrough', 'revolutionary', 'massive adoption', 'partnership', 'funding'],
            'buy': ['growth', 'expansion', 'positive', 'bullish', 'upgrade'],
            'hold': ['stable', 'consolidation', 'sideways', 'neutral', 'wait'],
            'sell': ['decline', 'negative', 'bearish', 'downgrade', 'concern'],
            'strong_sell': ['crash', 'scam', 'rug pull', 'exploit', 'collapse']
        }

    def analyze_gaming_sector(self, content_data: List[Dict[str, Any]],
                             timeframe: str = "24h") -> MarketIntelligenceResult:
        """Comprehensive gaming sector analysis"""

        # Aggregate all content
        all_content = []
        for item in content_data:
            content_text = f"{item.get('title', '')} {item.get('content', '')} {item.get('summary', '')}"
            all_content.append(content_text)

        combined_content = " ".join(all_content).lower()

        # Market sentiment analysis
        market_sentiment = self._calculate_market_sentiment(combined_content)

        # Trend direction analysis
        trend_direction = self._analyze_trend_direction(combined_content)

        # Volatility assessment
        volatility_score = self._assess_volatility(combined_content)

        # Correlation signals
        correlation_signals = self._analyze_correlation_signals(content_data)

        # Investment signals
        investment_signals = self._generate_investment_signals(combined_content)

        # Competitive analysis
        competitive_analysis = self._perform_competitive_analysis(content_data)

        # Risk assessment
        risk_assessment = self._assess_risks(combined_content)

        return MarketIntelligenceResult(
            market_sentiment=market_sentiment,
            trend_direction=trend_direction,
            volatility_score=volatility_score,
            correlation_signals=correlation_signals,
            investment_signals=investment_signals,
            competitive_analysis=competitive_analysis,
            risk_assessment=risk_assessment,
            analysis_timestamp=datetime.utcnow()
        )

    def _calculate_market_sentiment(self, content: str) -> float:
        """Calculate overall market sentiment"""

        positive_indicators = ['bullish', 'moon', 'pump', 'rally', 'surge', 'breakout', 'gains']
        negative_indicators = ['bearish', 'dump', 'crash', 'correction', 'decline', 'losses']

        positive_count = sum(content.count(indicator) for indicator in positive_indicators)
        negative_count = sum(content.count(indicator) for indicator in negative_indicators)

        total_indicators = positive_count + negative_count
        if total_indicators == 0:
            return 0.0

        sentiment = (positive_count - negative_count) / total_indicators
        return max(-1.0, min(1.0, sentiment))

    def _analyze_trend_direction(self, content: str) -> str:
        """Analyze overall trend direction"""

        uptrend_keywords = ['uptrend', 'rising', 'increasing', 'growth', 'expansion', 'bullish']
        downtrend_keywords = ['downtrend', 'falling', 'decreasing', 'decline', 'contraction', 'bearish']
        sideways_keywords = ['sideways', 'consolidation', 'range', 'stable', 'flat', 'neutral']

        uptrend_score = sum(content.count(keyword) for keyword in uptrend_keywords)
        downtrend_score = sum(content.count(keyword) for keyword in downtrend_keywords)
        sideways_score = sum(content.count(keyword) for keyword in sideways_keywords)

        scores = {'uptrend': uptrend_score, 'downtrend': downtrend_score, 'sideways': sideways_score}

        if max(scores.values()) == 0:
            return 'neutral'

        return max(scores.items(), key=lambda x: x[1])[0]

    def _assess_volatility(self, content: str) -> float:
        """Assess market volatility based on content"""

        volatility_keywords = [
            'volatile', 'volatility', 'swing', 'fluctuation', 'unstable',
            'erratic', 'unpredictable', 'wild', 'extreme', 'dramatic'
        ]

        volatility_count = sum(content.count(keyword) for keyword in volatility_keywords)
        content_length = len(content.split())

        # Normalize by content length
        volatility_score = volatility_count / max(1, content_length / 1000)
        return min(1.0, volatility_score)

    def _analyze_correlation_signals(self, content_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """Analyze correlation signals between different gaming sectors"""

        correlations = {}

        # Analyze cross-sector mentions
        sector_mentions = {sector: 0 for sector in self.sector_weights.keys()}

        for item in content_data:
            content_text = f"{item.get('title', '')} {item.get('content', '')}".lower()

            # Count sector mentions
            if any(keyword in content_text for keyword in ['play-to-earn', 'p2e', 'earn']):
                sector_mentions['p2e_gaming'] += 1
            if any(keyword in content_text for keyword in ['nft', 'collectible', 'digital asset']):
                sector_mentions['nft_gaming'] += 1
            if any(keyword in content_text for keyword in ['defi', 'yield', 'liquidity', 'staking']):
                sector_mentions['defi_gaming'] += 1
            if any(keyword in content_text for keyword in ['metaverse', 'virtual world', '3d']):
                sector_mentions['metaverse'] += 1

        # Calculate correlation strength
        total_mentions = sum(sector_mentions.values())
        if total_mentions > 0:
            for sector, count in sector_mentions.items():
                correlations[f'{sector}_correlation'] = count / total_mentions

        return correlations

    def _generate_investment_signals(self, content: str) -> Dict[str, Any]:
        """Generate investment signals based on content analysis"""

        signal_scores = {}
        for signal_type, keywords in self.investment_signals.items():
            score = sum(content.count(keyword) for keyword in keywords)
            signal_scores[signal_type] = score

        # Determine dominant signal
        if signal_scores:
            dominant_signal = max(signal_scores.items(), key=lambda x: x[1])
            signal_strength = dominant_signal[1] / max(1, sum(signal_scores.values()))
        else:
            dominant_signal = ('hold', 0)
            signal_strength = 0.0

        return {
            'dominant_signal': dominant_signal[0],
            'signal_strength': signal_strength,
            'all_signals': signal_scores,
            'recommendation': self._generate_recommendation(dominant_signal[0], signal_strength)
        }

    def _generate_recommendation(self, signal: str, strength: float) -> str:
        """Generate investment recommendation"""

        if strength < 0.1:
            return "Insufficient data for recommendation"
        elif strength < 0.3:
            return f"Weak {signal} signal - proceed with caution"
        elif strength < 0.6:
            return f"Moderate {signal} signal - consider position sizing"
        else:
            return f"Strong {signal} signal - high confidence recommendation"

    def _perform_competitive_analysis(self, content_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Perform competitive analysis of gaming projects"""

        project_mentions = {}
        sentiment_by_project = {}

        # Common gaming projects to track
        gaming_projects = [
            'axie infinity', 'star atlas', 'decentraland', 'the sandbox', 'gala games',
            'illuvium', 'alien worlds', 'splinterlands', 'gods unchained', 'my neighbor alice'
        ]

        for item in content_data:
            content_text = f"{item.get('title', '')} {item.get('content', '')}".lower()

            # Count project mentions
            for project in gaming_projects:
                if project in content_text:
                    project_mentions[project] = project_mentions.get(project, 0) + 1

                    # Analyze sentiment for this project mention
                    if NLP_AVAILABLE:
                        try:
                            blob = TextBlob(content_text)
                            sentiment = blob.sentiment.polarity
                            if project not in sentiment_by_project:
                                sentiment_by_project[project] = []
                            sentiment_by_project[project].append(sentiment)
                        except:
                            pass

        # Calculate average sentiment per project
        avg_sentiment_by_project = {}
        for project, sentiments in sentiment_by_project.items():
            avg_sentiment_by_project[project] = sum(sentiments) / len(sentiments)

        return {
            'project_mentions': project_mentions,
            'sentiment_by_project': avg_sentiment_by_project,
            'top_mentioned_projects': sorted(project_mentions.items(), key=lambda x: x[1], reverse=True)[:5],
            'most_positive_sentiment': max(avg_sentiment_by_project.items(), key=lambda x: x[1]) if avg_sentiment_by_project else None,
            'most_negative_sentiment': min(avg_sentiment_by_project.items(), key=lambda x: x[1]) if avg_sentiment_by_project else None
        }

    def _assess_risks(self, content: str) -> Dict[str, float]:
        """Assess various risk factors"""

        risk_scores = {}

        for risk_type, keywords in self.risk_factors.items():
            score = sum(content.count(keyword) for keyword in keywords)
            # Normalize by content length
            normalized_score = score / max(1, len(content.split()) / 1000)
            risk_scores[risk_type] = min(1.0, normalized_score)

        # Calculate overall risk score
        overall_risk = sum(risk_scores.values()) / len(risk_scores)
        risk_scores['overall_risk'] = overall_risk

        return risk_scores


class EntityRecognitionEngine:
    """Advanced entity recognition for gaming content"""

    def __init__(self):
        self.gaming_entities = {
            'projects': [
                'axie infinity', 'star atlas', 'decentraland', 'the sandbox', 'gala games',
                'illuvium', 'alien worlds', 'splinterlands', 'gods unchained', 'my neighbor alice',
                'stepn', 'genopets', 'defi kingdoms', 'crabada', 'thetan arena'
            ],
            'tokens': [
                'axs', 'slp', 'atlas', 'polis', 'mana', 'sand', 'gala', 'ilv', 'tlm',
                'sps', 'gods', 'alice', 'gmt', 'gst', 'gene', 'jewel', 'cra', 'thg'
            ],
            'blockchains': [
                'ethereum', 'solana', 'polygon', 'bsc', 'avalanche', 'ronin',
                'arbitrum', 'optimism', 'fantom', 'harmony', 'wax'
            ],
            'categories': [
                'play-to-earn', 'p2e', 'nft gaming', 'defi gaming', 'metaverse',
                'gamefi', 'virtual worlds', 'blockchain games', 'crypto games'
            ]
        }

        self.entity_patterns = self._compile_entity_patterns()

    def _compile_entity_patterns(self) -> Dict[str, List]:
        """Compile regex patterns for entity recognition"""
        patterns = {}

        for entity_type, entities in self.gaming_entities.items():
            patterns[entity_type] = []
            for entity in entities:
                # Create case-insensitive pattern with word boundaries
                pattern = re.compile(rf'\b{re.escape(entity)}\b', re.IGNORECASE)
                patterns[entity_type].append((entity, pattern))

        return patterns

    def recognize_entities(self, content: str) -> Dict[str, Any]:
        """Recognize gaming entities in content"""

        recognized_entities = {
            'projects': [],
            'tokens': [],
            'blockchains': [],
            'categories': [],
            'confidence_scores': {},
            'entity_contexts': {}
        }

        # Extract entities using patterns
        for entity_type, pattern_list in self.entity_patterns.items():
            for entity_name, pattern in pattern_list:
                matches = pattern.findall(content)
                if matches:
                    recognized_entities[entity_type].append(entity_name)

                    # Calculate confidence based on number of mentions
                    confidence = min(1.0, len(matches) * 0.2)
                    recognized_entities['confidence_scores'][entity_name] = confidence

                    # Extract context around mentions
                    contexts = self._extract_entity_context(content, entity_name)
                    recognized_entities['entity_contexts'][entity_name] = contexts

        # Remove duplicates
        for entity_type in ['projects', 'tokens', 'blockchains', 'categories']:
            recognized_entities[entity_type] = list(set(recognized_entities[entity_type]))

        # Additional analysis
        recognized_entities['entity_relationships'] = self._analyze_entity_relationships(recognized_entities)
        recognized_entities['dominant_ecosystem'] = self._identify_dominant_ecosystem(recognized_entities)

        return recognized_entities

    def _extract_entity_context(self, content: str, entity: str, context_window: int = 50) -> List[str]:
        """Extract context around entity mentions"""
        contexts = []

        # Find all occurrences of the entity
        pattern = re.compile(rf'\b{re.escape(entity)}\b', re.IGNORECASE)

        for match in pattern.finditer(content):
            start = max(0, match.start() - context_window)
            end = min(len(content), match.end() + context_window)
            context = content[start:end].strip()
            contexts.append(context)

        return contexts[:3]  # Return up to 3 contexts

    def _analyze_entity_relationships(self, entities: Dict[str, Any]) -> Dict[str, List[str]]:
        """Analyze relationships between recognized entities"""
        relationships = {}

        # Map projects to their associated tokens and blockchains
        project_token_map = {
            'axie infinity': ['axs', 'slp'],
            'star atlas': ['atlas', 'polis'],
            'decentraland': ['mana'],
            'the sandbox': ['sand'],
            'gala games': ['gala'],
            'illuvium': ['ilv'],
            'alien worlds': ['tlm'],
            'splinterlands': ['sps'],
            'gods unchained': ['gods'],
            'my neighbor alice': ['alice']
        }

        project_blockchain_map = {
            'axie infinity': ['ethereum', 'ronin'],
            'star atlas': ['solana'],
            'decentraland': ['ethereum', 'polygon'],
            'the sandbox': ['ethereum', 'polygon'],
            'gala games': ['ethereum'],
            'illuvium': ['ethereum'],
            'alien worlds': ['wax', 'bsc', 'ethereum'],
            'splinterlands': ['hive'],
            'gods unchained': ['ethereum'],
            'my neighbor alice': ['bsc']
        }

        # Find relationships for recognized projects
        for project in entities['projects']:
            relationships[project] = {
                'associated_tokens': [token for token in project_token_map.get(project, []) if token in entities['tokens']],
                'associated_blockchains': [chain for chain in project_blockchain_map.get(project, []) if chain in entities['blockchains']]
            }

        return relationships

    def _identify_dominant_ecosystem(self, entities: Dict[str, Any]) -> Dict[str, Any]:
        """Identify the dominant blockchain ecosystem"""

        blockchain_scores = {}

        # Score based on direct mentions
        for blockchain in entities['blockchains']:
            blockchain_scores[blockchain] = blockchain_scores.get(blockchain, 0) + 1

        # Score based on associated projects
        project_blockchain_map = {
            'axie infinity': ['ethereum', 'ronin'],
            'star atlas': ['solana'],
            'decentraland': ['ethereum', 'polygon'],
            'the sandbox': ['ethereum', 'polygon'],
            'gala games': ['ethereum'],
            'illuvium': ['ethereum'],
            'alien worlds': ['wax', 'bsc', 'ethereum']
        }

        for project in entities['projects']:
            associated_chains = project_blockchain_map.get(project, [])
            for chain in associated_chains:
                blockchain_scores[chain] = blockchain_scores.get(chain, 0) + 0.5

        if blockchain_scores:
            dominant_ecosystem = max(blockchain_scores.items(), key=lambda x: x[1])
            total_score = sum(blockchain_scores.values())
            dominance_percentage = (dominant_ecosystem[1] / total_score) * 100

            return {
                'dominant_blockchain': dominant_ecosystem[0],
                'dominance_percentage': dominance_percentage,
                'all_ecosystem_scores': blockchain_scores
            }

        return {
            'dominant_blockchain': None,
            'dominance_percentage': 0,
            'all_ecosystem_scores': {}
        }


# Global instances
gaming_content_classifier = AdvancedGamingContentClassifier()
sentiment_scoring_engine = SentimentScoringEngine()
trend_detection_engine = TrendDetectionEngine()
market_intelligence_engine = MarketIntelligenceEngine()
entity_recognition_engine = EntityRecognitionEngine()
