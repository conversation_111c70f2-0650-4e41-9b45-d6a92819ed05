# Log Management Configuration
# Comprehensive configuration for log rotation, archival, monitoring, and alerting

# Log Directory Configuration
log_directory: "logs"
archive_directory: "logs/archive"
temp_directory: "logs/temp"

# Retention Policies (in days)
retention_policies:
  debug: 7        # Debug logs kept for 1 week
  info: 30        # Info logs kept for 1 month  
  warning: 90     # Warning logs kept for 3 months
  error: 365      # Error logs kept for 1 year
  critical: 730   # Critical logs kept for 2 years

# Log Rotation Configuration
rotation:
  max_file_size_mb: 100      # Rotate when file exceeds 100MB
  max_backup_count: 10       # Keep 10 backup files
  compress_rotated: true     # Compress rotated files
  rotation_schedule: "daily" # daily, weekly, monthly
  rotation_time: "02:00"     # Time to perform rotation (24h format)

# Archive Configuration
archival:
  archive_after_days: 7      # Archive files older than 7 days
  compress_archives: true    # Compress archived files
  archive_schedule: "weekly" # weekly, monthly
  archive_day: "sunday"      # Day of week for archival
  archive_time: "03:00"      # Time to perform archival

# Cleanup Configuration
cleanup:
  cleanup_schedule: "monthly"    # monthly, quarterly
  cleanup_day: 1                 # Day of month for cleanup
  cleanup_time: "04:00"          # Time to perform cleanup
  min_free_space_gb: 5           # Minimum free space to maintain
  emergency_cleanup_threshold: 1 # Emergency cleanup when < 1GB free

# Log Monitoring Configuration
monitoring:
  enabled: true
  check_interval_seconds: 60     # Check logs every minute
  metrics_reporting_interval: 300 # Report metrics every 5 minutes
  
  # Performance thresholds
  thresholds:
    response_time_warning_ms: 2000    # Warn if response > 2s
    response_time_critical_ms: 5000   # Critical if response > 5s
    error_rate_warning_per_minute: 5  # Warn if > 5 errors/minute
    error_rate_critical_per_minute: 20 # Critical if > 20 errors/minute
    disk_space_warning_gb: 10         # Warn if < 10GB free
    disk_space_critical_gb: 2         # Critical if < 2GB free

# Alert Configuration
alerts:
  # Critical error alerts
  - name: "Critical Errors"
    pattern: '"level":\s*"CRITICAL"'
    threshold: 1
    time_window_seconds: 60
    severity: "critical"
    cooldown_seconds: 300
    
  # High error rate alerts
  - name: "High Error Rate"
    pattern: '"level":\s*"ERROR"'
    threshold: 10
    time_window_seconds: 300
    severity: "warning"
    cooldown_seconds: 600
    
  # Database connection issues
  - name: "Database Connection Issues"
    pattern: 'database.*connection.*failed|connection.*database.*error'
    threshold: 3
    time_window_seconds: 180
    severity: "critical"
    cooldown_seconds: 300
    
  # Blockchain RPC failures
  - name: "Blockchain RPC Failures"
    pattern: 'rpc.*failed|blockchain.*connection.*error'
    threshold: 5
    time_window_seconds: 300
    severity: "warning"
    cooldown_seconds: 600
    
  # API performance issues
  - name: "Slow API Responses"
    pattern: '"response_time_ms":\s*[5-9]\d{3,}'
    threshold: 5
    time_window_seconds: 300
    severity: "warning"
    cooldown_seconds: 600
    
  # Memory issues
  - name: "Memory Issues"
    pattern: 'memory.*error|out of memory|memory.*exceeded'
    threshold: 1
    time_window_seconds: 60
    severity: "critical"
    cooldown_seconds: 300
    
  # Authentication failures
  - name: "Authentication Failures"
    pattern: 'authentication.*failed|unauthorized|invalid.*token'
    threshold: 10
    time_window_seconds: 300
    severity: "warning"
    cooldown_seconds: 600
    
  # Scraper failures
  - name: "Scraper Failures"
    pattern: 'scraper.*failed|scraping.*error'
    threshold: 5
    time_window_seconds: 600
    severity: "warning"
    cooldown_seconds: 900

# Notification Configuration
notifications:
  # Email notifications
  email:
    enabled: false  # Set to true and configure SMTP settings
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    username: ""    # Set via environment variable
    password: ""    # Set via environment variable
    from_email: ""  # Set via environment variable
    to_emails: []   # List of recipient emails
    
  # Webhook notifications
  webhook:
    enabled: false  # Set to true and configure webhook URL
    url: ""         # Webhook URL
    headers:
      Content-Type: "application/json"
    timeout_seconds: 30
    
  # Slack notifications
  slack:
    enabled: false  # Set to true and configure Slack webhook
    webhook_url: "" # Slack webhook URL
    channel: "#alerts"
    username: "LogMonitor"
    
  # Discord notifications
  discord:
    enabled: false  # Set to true and configure Discord webhook
    webhook_url: "" # Discord webhook URL

# Log Analysis Configuration
analysis:
  # Error pattern classification
  error_patterns:
    database_error:
      keywords: ["database", "sql", "connection", "query", "transaction"]
      severity_weight: 0.8
      
    blockchain_error:
      keywords: ["blockchain", "rpc", "web3", "contract", "transaction"]
      severity_weight: 0.7
      
    api_error:
      keywords: ["api", "http", "request", "response", "endpoint"]
      severity_weight: 0.6
      
    scraper_error:
      keywords: ["scraper", "scraping", "crawl", "parse", "extract"]
      severity_weight: 0.5
      
    timeout_error:
      keywords: ["timeout", "timed out", "deadline", "expired"]
      severity_weight: 0.7
      
    connection_error:
      keywords: ["connection", "connect", "disconnect", "network"]
      severity_weight: 0.6
      
    memory_error:
      keywords: ["memory", "oom", "out of memory", "heap", "stack"]
      severity_weight: 0.9
      
    auth_error:
      keywords: ["auth", "authentication", "authorization", "permission", "token"]
      severity_weight: 0.6

  # Performance analysis
  performance:
    response_time_buckets: [100, 500, 1000, 2000, 5000, 10000]  # milliseconds
    percentiles: [50, 75, 90, 95, 99]
    slow_query_threshold_ms: 1000
    
  # Trending analysis
  trending:
    time_windows: ["1h", "6h", "24h", "7d", "30d"]
    trend_threshold_percent: 20  # Alert if trend changes > 20%

# Export Configuration
export:
  formats: ["json", "csv", "elasticsearch"]
  
  # Elasticsearch configuration
  elasticsearch:
    enabled: false
    host: "localhost"
    port: 9200
    index_prefix: "web3-gaming-logs"
    index_rotation: "daily"  # daily, weekly, monthly
    
  # CSV export settings
  csv:
    delimiter: ","
    quote_char: '"'
    include_headers: true
    
  # JSON export settings
  json:
    pretty_print: true
    include_metadata: true

# Automation Configuration
automation:
  # Scheduled tasks
  schedules:
    log_rotation:
      enabled: true
      cron: "0 2 * * *"  # Daily at 2 AM
      
    log_archival:
      enabled: true
      cron: "0 3 * * 0"  # Weekly on Sunday at 3 AM
      
    log_cleanup:
      enabled: true
      cron: "0 4 1 * *"  # Monthly on 1st at 4 AM
      
    health_check:
      enabled: true
      cron: "*/15 * * * *"  # Every 15 minutes
      
    metrics_collection:
      enabled: true
      cron: "*/5 * * * *"   # Every 5 minutes
      
    analysis_report:
      enabled: true
      cron: "0 6 * * *"     # Daily at 6 AM

  # Emergency procedures
  emergency:
    auto_cleanup_enabled: true
    emergency_threshold_gb: 1
    emergency_actions:
      - "compress_old_logs"
      - "archive_rotated_logs"
      - "cleanup_temp_files"
      - "remove_debug_logs"

# Integration Configuration
integrations:
  # Prometheus metrics
  prometheus:
    enabled: false
    metrics_port: 9090
    metrics_path: "/metrics"
    
  # Grafana dashboards
  grafana:
    enabled: false
    dashboard_url: ""
    api_key: ""
    
  # Sentry error tracking
  sentry:
    enabled: false
    dsn: ""
    environment: "production"

# Security Configuration
security:
  # Log file permissions
  file_permissions: "640"
  directory_permissions: "750"
  
  # Log sanitization
  sanitize_logs: true
  sensitive_patterns:
    - "password"
    - "token"
    - "api_key"
    - "secret"
    - "private_key"
    
  # Audit logging
  audit_enabled: true
  audit_events:
    - "log_rotation"
    - "log_archival"
    - "log_cleanup"
    - "configuration_change"
    - "alert_triggered"
