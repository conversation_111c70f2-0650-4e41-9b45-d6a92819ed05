{"timestamp": "2025-07-07T12:49:14.558813", "docker": {"status": "daemon_not_running", "containers": []}, "services": [{"service": "prometheus", "status": "not_running", "port": 9090, "error": "Connection refused", "url": "http://localhost:9090/-/healthy"}, {"service": "grafana", "status": "not_running", "port": 3000, "error": "Connection refused", "url": "http://localhost:3000/api/health"}, {"service": "api", "status": "not_running", "port": 8000, "error": "Connection refused", "url": "http://localhost:8000/health"}, {"service": "dashboard", "status": "not_running", "port": 3001, "error": "Connection refused", "url": "http://localhost:3001/"}], "configurations": {"docker-compose.yml": {"exists": true, "path": "/Volumes/Booter/growlerHome/Documents/augment-projects/webThreeGameScraper/docker-compose.yml"}, "docker-compose.monitoring.yml": {"exists": true, "path": "/Volumes/Booter/growlerHome/Documents/augment-projects/webThreeGameScraper/docker-compose.monitoring.yml"}, "prometheus.yml": {"exists": true, "path": "/Volumes/Booter/growlerHome/Documents/augment-projects/webThreeGameScraper/prometheus/prometheus.yml"}, "grafana_datasource": {"exists": true, "path": "/Volumes/Booter/growlerHome/Documents/augment-projects/webThreeGameScraper/grafana/provisioning/datasources/prometheus.yml"}, "grafana_dashboards": {"exists": true, "path": "/Volumes/Booter/growlerHome/Documents/augment-projects/webThreeGameScraper/grafana/dashboards", "dashboard_count": 2}}, "summary": {"overall_status": "docker_unavailable", "healthy_services": 0, "total_services": 4, "docker_available": false, "configurations_complete": true, "recommendations": ["Start Docker daemon"]}}