#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add sx={textFieldSx} to all TextField components in AddGameForm.jsx
"""

import re

def fix_textfield_styling():
    file_path = "dashboard/frontend/src/components/AddGameForm.jsx"
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Pattern to match TextField components that don't already have sx prop
    pattern = r'(<TextField\s+[^>]*?)(\s*/>|\s*>)'
    
    def replace_textfield(match):
        textfield_content = match.group(1)
        closing = match.group(2)
        
        # Check if sx prop already exists
        if 'sx=' in textfield_content:
            return match.group(0)  # Return unchanged
        
        # Add sx={textFieldSx} before the closing
        return textfield_content + '\n                  sx={textFieldSx}' + closing
    
    # Apply the replacement
    new_content = re.sub(pattern, replace_textfield, content, flags=re.DOTALL)
    
    # Write back to file
    with open(file_path, 'w') as f:
        f.write(new_content)
    
    print("✅ Added sx={textFieldSx} to all TextField components")

if __name__ == "__main__":
    fix_textfield_styling()
