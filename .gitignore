# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# Environment variables
.env
.env.local
.env.production

# Database
*.db
*.sqlite3

# Logs
*.log
logs/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Docker
.dockerignore

# Monitoring
prometheus_data/
grafana_data/

# Temporary files
tmp/
temp/
*.tmp

# Coverage
.coverage
htmlcov/
.pytest_cache/

# Jupyter
.ipynb_checkpoints/

# Node.js (if any frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
