"""
Base client for blockchain data APIs
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
import aiohttp
from datetime import datetime, timedelta
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class RateLimiter:
    """Simple rate limiter for API requests"""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.requests = []
    
    async def wait_if_needed(self):
        """Wait if rate limit would be exceeded"""
        now = datetime.utcnow()
        # Remove requests older than 1 minute
        self.requests = [req_time for req_time in self.requests 
                        if now - req_time < timedelta(minutes=1)]
        
        if len(self.requests) >= self.requests_per_minute:
            # Wait until the oldest request is more than 1 minute old
            oldest_request = min(self.requests)
            wait_time = 60 - (now - oldest_request).total_seconds()
            if wait_time > 0:
                logger.info(f"Rate limit reached, waiting {wait_time:.2f} seconds")
                await asyncio.sleep(wait_time)
        
        self.requests.append(now)


class BaseBlockchainDataClient(ABC):
    """Base class for blockchain data API clients"""
    
    def __init__(self, api_key: str, base_url: str, rate_limit: int = 60):
        self.api_key = api_key
        self.base_url = base_url
        self.rate_limiter = RateLimiter(rate_limit)
        self.session: Optional[aiohttp.ClientSession] = None
        self.timeout = aiohttp.ClientTimeout(total=settings.blockchain_data.request_timeout)
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _make_request(
        self, 
        method: str, 
        endpoint: str, 
        params: Optional[Dict] = None,
        data: Optional[Dict] = None,
        headers: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """Make HTTP request with rate limiting and error handling"""
        await self.rate_limiter.wait_if_needed()
        
        url = f"{self.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        # Default headers
        request_headers = {
            'User-Agent': 'Web3GamingNewsBot/1.0',
            'Accept': 'application/json',
        }
        if headers:
            request_headers.update(headers)
        
        # Add API key to headers or params based on API requirements
        request_headers.update(self._get_auth_headers())
        
        for attempt in range(settings.blockchain_data.max_retries):
            try:
                if not self.session:
                    raise RuntimeError("Client session not initialized. Use async context manager.")
                
                async with self.session.request(
                    method=method,
                    url=url,
                    params=params,
                    json=data,
                    headers=request_headers
                ) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 429:  # Rate limited
                        wait_time = 2 ** attempt
                        logger.warning(f"Rate limited, waiting {wait_time} seconds")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        response_text = await response.text()
                        logger.error(f"API request failed: {response.status} - {response_text}")
                        response.raise_for_status()
                        
            except asyncio.TimeoutError:
                logger.warning(f"Request timeout (attempt {attempt + 1})")
                if attempt == settings.blockchain_data.max_retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)
            except Exception as e:
                logger.error(f"Request failed (attempt {attempt + 1}): {e}")
                if attempt == settings.blockchain_data.max_retries - 1:
                    raise
                await asyncio.sleep(2 ** attempt)
        
        raise Exception(f"Failed to complete request after {settings.blockchain_data.max_retries} attempts")
    
    @abstractmethod
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for the API"""
        pass
    
    @abstractmethod
    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get data for gaming tokens"""
        pass
    
    @abstractmethod
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data"""
        pass
    
    @abstractmethod
    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics"""
        pass
    
    async def test_connection(self) -> bool:
        """Test API connection"""
        try:
            # Each client should implement a simple test endpoint
            await self._test_endpoint()
            return True
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False
    
    @abstractmethod
    async def _test_endpoint(self):
        """Test a simple endpoint to verify connection"""
        pass
