"""
BitQuery API client for blockchain data
"""
import logging
from typing import Dict, List, Optional, Any
from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class BitQueryClient(BaseBlockchainDataClient):
    """Client for BitQuery GraphQL API"""
    
    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.bitquery_api_key,
            base_url=settings.blockchain_data.bitquery_base_url,
            rate_limit=settings.blockchain_data.rate_limit_per_minute
        )
        self.access_token = settings.blockchain_data.bitquery_access_token
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for BitQuery API"""
        return {
            'X-API-KEY': self.api_key,
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
    
    async def _test_endpoint(self):
        """Test BitQuery API connection"""
        # Simple test query
        query = """
        {
          ethereum {
            blocks(limit: 1) {
              height
              timestamp {
                time
              }
            }
          }
        }
        """
        await self.execute_graphql_query(query)
    
    async def execute_graphql_query(self, query: str, variables: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a GraphQL query"""
        data = {
            'query': query,
            'variables': variables or {}
        }
        
        response = await self._make_request('POST', '/', data=data)
        
        if 'errors' in response:
            raise Exception(f"GraphQL errors: {response['errors']}")
        
        return response.get('data', {})
    
    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get gaming token data from BitQuery"""
        if not tokens:
            return []
        
        # Create token filter for GraphQL
        token_symbols = ', '.join([f'"{token}"' for token in tokens])
        
        query = f"""
        {{
          ethereum {{
            dexTrades(
              baseCurrency: {{symbol: {{in: [{token_symbols}]}}}}
              date: {{since: "2024-01-01"}}
              options: {{limit: 100, desc: "timeInterval.minute"}}
            ) {{
              baseCurrency {{
                symbol
                name
                address
              }}
              quoteCurrency {{
                symbol
              }}
              timeInterval {{
                minute(count: 60)
              }}
              volume: baseAmount
              trades: count
              maximum_price: quotePrice(calculate: maximum)
              minimum_price: quotePrice(calculate: minimum)
              open_price: quotePrice(calculate: open)
              close_price: quotePrice(calculate: close)
            }}
          }}
        }}
        """
        
        try:
            result = await self.execute_graphql_query(query)
            return result.get('ethereum', {}).get('dexTrades', [])
        except Exception as e:
            logger.error(f"Failed to get gaming tokens data: {e}")
            return []
    
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data from BitQuery"""
        query = f"""
        {{
          ethereum {{
            transfers(
              currency: {{is: "{collection_address}"}}
              date: {{since: "2024-01-01"}}
              options: {{limit: 1000}}
            ) {{
              currency {{
                address
                name
                symbol
                tokenType
              }}
              count
              amount
              sender {{
                address
              }}
              receiver {{
                address
              }}
              transaction {{
                hash
                gasPrice
                gasValue
              }}
              block {{
                timestamp {{
                  time
                }}
                height
              }}
            }}
          }}
        }}
        """
        
        try:
            result = await self.execute_graphql_query(query)
            transfers = result.get('ethereum', {}).get('transfers', [])
            
            if not transfers:
                return {}
            
            # Aggregate data
            unique_holders = set()
            total_transfers = len(transfers)
            total_gas_used = 0
            
            for transfer in transfers:
                unique_holders.add(transfer.get('sender', {}).get('address'))
                unique_holders.add(transfer.get('receiver', {}).get('address'))
                gas_value = transfer.get('transaction', {}).get('gasValue', 0)
                if gas_value:
                    total_gas_used += float(gas_value)
            
            return {
                'collection_address': collection_address,
                'unique_holders': len(unique_holders),
                'total_transfers': total_transfers,
                'total_gas_used': total_gas_used,
                'currency_info': transfers[0].get('currency', {}) if transfers else {}
            }
        except Exception as e:
            logger.error(f"Failed to get NFT collection data: {e}")
            return {}
    
    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics from BitQuery"""
        # This would need specific contract addresses for the protocol
        # For now, we'll use a generic approach
        query = f"""
        {{
          ethereum {{
            smartContractCalls(
              date: {{since: "2024-01-01"}}
              smartContractAddress: {{is: "******************************************"}}
              options: {{limit: 1000}}
            ) {{
              smartContract {{
                address {{
                  address
                }}
                contractType
              }}
              smartContractMethod {{
                name
                signatureHash
              }}
              count
              gasValue
              transaction {{
                gasPrice
                gasUsed
              }}
              block {{
                timestamp {{
                  time
                }}
              }}
            }}
          }}
        }}
        """
        
        try:
            result = await self.execute_graphql_query(query)
            return {
                'protocol_name': protocol_name,
                'smart_contract_calls': result.get('ethereum', {}).get('smartContractCalls', [])
            }
        except Exception as e:
            logger.error(f"Failed to get gaming protocol metrics: {e}")
            return {}
    
    async def get_dex_trading_data(self, token_addresses: List[str]) -> Dict[str, Any]:
        """Get DEX trading data for gaming tokens"""
        if not token_addresses:
            return {}
        
        # Create address filter
        addresses = ', '.join([f'"{addr}"' for addr in token_addresses])
        
        query = f"""
        {{
          ethereum {{
            dexTrades(
              baseCurrency: {{address: {{in: [{addresses}]}}}}
              date: {{since: "2024-01-01"}}
              options: {{limit: 500, desc: "timeInterval.hour"}}
            ) {{
              baseCurrency {{
                address
                symbol
                name
              }}
              quoteCurrency {{
                symbol
              }}
              timeInterval {{
                hour(count: 1)
              }}
              buyCurrency
              sellCurrency
              trades: count
              buyers: count(uniq: buyers)
              sellers: count(uniq: sellers)
              buyAmount
              sellAmount
              tradeAmount(in: USD)
              maximum_price: quotePrice(calculate: maximum)
              minimum_price: quotePrice(calculate: minimum)
            }}
          }}
        }}
        """
        
        try:
            result = await self.execute_graphql_query(query)
            return {
                'dex_trades': result.get('ethereum', {}).get('dexTrades', [])
            }
        except Exception as e:
            logger.error(f"Failed to get DEX trading data: {e}")
            return {}
    
    async def get_gaming_wallet_analysis(self, wallet_addresses: List[str]) -> Dict[str, Any]:
        """Analyze gaming-related wallet activity"""
        if not wallet_addresses:
            return {}
        
        addresses = ', '.join([f'"{addr}"' for addr in wallet_addresses])
        
        query = f"""
        {{
          ethereum {{
            transfers(
              sender: {{in: [{addresses}]}}
              date: {{since: "2024-01-01"}}
              options: {{limit: 1000}}
            ) {{
              sender {{
                address
              }}
              receiver {{
                address
              }}
              currency {{
                symbol
                address
                name
              }}
              amount
              transaction {{
                hash
                gasPrice
                gasValue
              }}
              block {{
                timestamp {{
                  time
                }}
              }}
            }}
          }}
        }}
        """
        
        try:
            result = await self.execute_graphql_query(query)
            return {
                'wallet_transfers': result.get('ethereum', {}).get('transfers', [])
            }
        except Exception as e:
            logger.error(f"Failed to get gaming wallet analysis: {e}")
            return {}
