"""
Flipside Crypto API client for blockchain analytics
"""
import logging
from typing import Dict, List, Optional, Any
from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class FlipsideClient(BaseBlockchainDataClient):
    """Client for Flipside Crypto API"""
    
    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.flipside_api_key,
            base_url=settings.blockchain_data.flipside_base_url,
            rate_limit=settings.blockchain_data.rate_limit_per_minute
        )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Flipside API"""
        return {
            'x-api-key': self.api_key,
            'Content-Type': 'application/json'
        }
    
    async def _test_endpoint(self):
        """Test Flipside API connection"""
        # Test with a simple query
        await self.execute_query("SELECT 1 as test_value")
    
    async def execute_query(self, sql_query: str, ttl_minutes: int = 60) -> Dict[str, Any]:
        """Execute a SQL query on Flipside"""
        data = {
            "sql": sql_query,
            "ttlMinutes": ttl_minutes
        }
        
        # Submit query
        response = await self._make_request('POST', '/queries', data=data)
        query_id = response.get('token')
        
        if not query_id:
            raise Exception("Failed to submit query to Flipside")
        
        # Poll for results
        return await self._poll_query_results(query_id)
    
    async def _poll_query_results(self, query_id: str, max_attempts: int = 30) -> Dict[str, Any]:
        """Poll for query results"""
        import asyncio
        
        for attempt in range(max_attempts):
            response = await self._make_request('GET', f'/queries/{query_id}')
            
            status = response.get('status')
            if status == 'finished':
                return response
            elif status == 'error':
                raise Exception(f"Query failed: {response.get('errorMessage', 'Unknown error')}")
            elif status in ['pending', 'running']:
                await asyncio.sleep(2)  # Wait 2 seconds before next poll
                continue
            else:
                raise Exception(f"Unknown query status: {status}")
        
        raise Exception("Query timed out")
    
    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get gaming token data from Flipside"""
        if not tokens:
            return []
        
        # Create SQL query for gaming tokens
        token_list = "', '".join(tokens)
        sql_query = f"""
        SELECT 
            symbol,
            price_usd,
            market_cap_usd,
            volume_24h_usd,
            price_change_24h_percent,
            block_timestamp as last_updated
        FROM ethereum.core.fact_token_prices 
        WHERE symbol IN ('{token_list}')
        AND block_timestamp >= CURRENT_DATE - 1
        ORDER BY block_timestamp DESC
        LIMIT 100
        """
        
        try:
            result = await self.execute_query(sql_query)
            return result.get('results', [])
        except Exception as e:
            logger.error(f"Failed to get gaming tokens data: {e}")
            return []
    
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data from Flipside"""
        sql_query = f"""
        SELECT 
            nft_address,
            project_name,
            COUNT(DISTINCT nft_to_address) as unique_holders,
            COUNT(*) as total_transfers,
            AVG(price_usd) as avg_price_usd,
            MAX(price_usd) as max_price_usd,
            SUM(price_usd) as total_volume_usd,
            MAX(block_timestamp) as last_activity
        FROM ethereum.core.fact_nft_sales 
        WHERE nft_address = LOWER('{collection_address}')
        AND block_timestamp >= CURRENT_DATE - 7
        GROUP BY nft_address, project_name
        """
        
        try:
            result = await self.execute_query(sql_query)
            results = result.get('results', [])
            return results[0] if results else {}
        except Exception as e:
            logger.error(f"Failed to get NFT collection data: {e}")
            return {}
    
    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics from Flipside"""
        # This is a generic query - would need to be customized per protocol
        sql_query = f"""
        SELECT 
            COUNT(DISTINCT from_address) as daily_active_users,
            COUNT(*) as daily_transactions,
            SUM(tx_fee) as daily_fees_eth,
            AVG(tx_fee) as avg_tx_fee_eth,
            DATE_TRUNC('day', block_timestamp) as date
        FROM ethereum.core.fact_transactions 
        WHERE block_timestamp >= CURRENT_DATE - 7
        AND (
            to_address IN (
                SELECT DISTINCT contract_address 
                FROM ethereum.core.dim_contracts 
                WHERE contract_name ILIKE '%{protocol_name}%'
            )
            OR from_address IN (
                SELECT DISTINCT contract_address 
                FROM ethereum.core.dim_contracts 
                WHERE contract_name ILIKE '%{protocol_name}%'
            )
        )
        GROUP BY DATE_TRUNC('day', block_timestamp)
        ORDER BY date DESC
        LIMIT 7
        """
        
        try:
            result = await self.execute_query(sql_query)
            return {
                'protocol_name': protocol_name,
                'metrics': result.get('results', [])
            }
        except Exception as e:
            logger.error(f"Failed to get gaming protocol metrics: {e}")
            return {}
    
    async def get_gaming_defi_metrics(self) -> Dict[str, Any]:
        """Get gaming DeFi metrics"""
        sql_query = """
        SELECT 
            symbol,
            SUM(amount_usd) as total_volume_usd,
            COUNT(DISTINCT origin_from_address) as unique_users,
            COUNT(*) as transaction_count,
            DATE_TRUNC('day', block_timestamp) as date
        FROM ethereum.core.fact_token_transfers 
        WHERE symbol IN ('AXS', 'SLP', 'SAND', 'MANA', 'ENJ', 'GALA', 'ILV', 'ALICE')
        AND block_timestamp >= CURRENT_DATE - 7
        GROUP BY symbol, DATE_TRUNC('day', block_timestamp)
        ORDER BY date DESC, total_volume_usd DESC
        """
        
        try:
            result = await self.execute_query(sql_query)
            return {
                'gaming_defi_metrics': result.get('results', [])
            }
        except Exception as e:
            logger.error(f"Failed to get gaming DeFi metrics: {e}")
            return {}
    
    async def get_p2e_game_activity(self, game_contracts: List[str]) -> Dict[str, Any]:
        """Get Play-to-Earn game activity metrics"""
        if not game_contracts:
            return {}
        
        contract_list = "', '".join([addr.lower() for addr in game_contracts])
        sql_query = f"""
        SELECT 
            to_address as contract_address,
            COUNT(DISTINCT from_address) as daily_players,
            COUNT(*) as daily_transactions,
            DATE_TRUNC('day', block_timestamp) as date
        FROM ethereum.core.fact_transactions 
        WHERE to_address IN ('{contract_list}')
        AND block_timestamp >= CURRENT_DATE - 7
        AND status = 'SUCCESS'
        GROUP BY to_address, DATE_TRUNC('day', block_timestamp)
        ORDER BY date DESC, daily_players DESC
        """
        
        try:
            result = await self.execute_query(sql_query)
            return {
                'p2e_activity': result.get('results', [])
            }
        except Exception as e:
            logger.error(f"Failed to get P2E game activity: {e}")
            return {}
