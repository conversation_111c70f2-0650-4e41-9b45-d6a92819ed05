"""
DexTools API client for DEX trading data
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class DexToolsClient(BaseBlockchainDataClient):
    """Client for DexTools API"""
    
    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.dextools_api_key,
            base_url=settings.blockchain_data.dextools_base_url,
            rate_limit=settings.blockchain_data.rate_limit_per_minute
        )
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for DexTools API"""
        return {
            'X-API-Key': self.api_key,
            'Content-Type': 'application/json'
        }
    
    async def _test_endpoint(self):
        """Test DexTools API connection"""
        # Test with a simple endpoint
        await self._make_request('GET', '/ranking/hotpools', params={'limit': 1})
    
    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """Get gaming token data from DexTools"""
        if not tokens:
            return []
        
        results = []
        
        for token_symbol in tokens:
            try:
                # Search for token by symbol
                params = {
                    'query': token_symbol,
                    'limit': 5
                }
                
                search_response = await self._make_request('GET', '/token/search', params=params)
                tokens_data = search_response.get('data', [])
                
                # Find exact match or best match
                token_data = None
                for token in tokens_data:
                    if token.get('symbol', '').upper() == token_symbol.upper():
                        token_data = token
                        break
                
                if not token_data and tokens_data:
                    token_data = tokens_data[0]  # Take first result
                
                if token_data:
                    # Get detailed token info
                    token_address = token_data.get('address')
                    chain = token_data.get('chain', 'ethereum')
                    
                    if token_address:
                        detailed_data = await self.get_token_details(chain, token_address)
                        results.append({
                            'symbol': token_symbol,
                            'address': token_address,
                            'chain': chain,
                            'search_data': token_data,
                            'detailed_data': detailed_data
                        })
                        
            except Exception as e:
                logger.error(f"Failed to get data for token {token_symbol}: {e}")
                continue
        
        return results
    
    async def get_token_details(self, chain: str, token_address: str) -> Dict[str, Any]:
        """Get detailed token information"""
        try:
            response = await self._make_request('GET', f'/token/{chain}/{token_address}')
            return response.get('data', {})
        except Exception as e:
            logger.error(f"Failed to get token details for {token_address}: {e}")
            return {}
    
    async def get_token_pools(self, chain: str, token_address: str) -> Dict[str, Any]:
        """Get trading pools for a token"""
        try:
            response = await self._make_request('GET', f'/token/{chain}/{token_address}/pools')
            return response.get('data', {})
        except Exception as e:
            logger.error(f"Failed to get token pools for {token_address}: {e}")
            return {}
    
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """Get NFT collection data from DexTools"""
        # DexTools primarily focuses on DEX trading, not NFTs
        # This might not be available or would need different endpoints
        try:
            # Placeholder - DexTools may not have NFT endpoints
            return {}
        except Exception as e:
            logger.error(f"Failed to get NFT collection data: {e}")
            return {}
    
    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """Get gaming protocol metrics from DexTools"""
        try:
            # Search for tokens related to the protocol
            params = {
                'query': protocol_name,
                'limit': 10
            }
            
            response = await self._make_request('GET', '/token/search', params=params)
            tokens = response.get('data', [])
            
            # Get pool data for found tokens
            protocol_data = []
            for token in tokens:
                token_address = token.get('address')
                chain = token.get('chain', 'ethereum')
                
                if token_address:
                    pools_data = await self.get_token_pools(chain, token_address)
                    protocol_data.append({
                        'token': token,
                        'pools': pools_data
                    })
            
            return {
                'protocol_name': protocol_name,
                'tokens': protocol_data
            }
            
        except Exception as e:
            logger.error(f"Failed to get gaming protocol metrics: {e}")
            return {}
    
    async def get_hot_pools(self, limit: int = 50) -> Dict[str, Any]:
        """Get hot trading pools"""
        try:
            params = {
                'limit': limit,
                'sortBy': 'volume24h'
            }
            
            response = await self._make_request('GET', '/ranking/hotpools', params=params)
            return response.get('data', {})
            
        except Exception as e:
            logger.error(f"Failed to get hot pools: {e}")
            return {}
    
    async def get_trending_tokens(self, chain: str = 'ethereum', limit: int = 50) -> Dict[str, Any]:
        """Get trending tokens"""
        try:
            params = {
                'chain': chain,
                'limit': limit,
                'sortBy': 'volume24h'
            }
            
            response = await self._make_request('GET', '/ranking/tokens', params=params)
            return response.get('data', {})
            
        except Exception as e:
            logger.error(f"Failed to get trending tokens: {e}")
            return {}
    
    async def get_pool_info(self, chain: str, pool_address: str) -> Dict[str, Any]:
        """Get detailed pool information"""
        try:
            response = await self._make_request('GET', f'/pool/{chain}/{pool_address}')
            return response.get('data', {})
        except Exception as e:
            logger.error(f"Failed to get pool info for {pool_address}: {e}")
            return {}
    
    async def get_token_price_history(self, chain: str, token_address: str, resolution: str = '1h') -> Dict[str, Any]:
        """Get token price history"""
        try:
            params = {
                'resolution': resolution,
                'from': int((datetime.utcnow() - timedelta(days=7)).timestamp()),
                'to': int(datetime.utcnow().timestamp())
            }
            
            response = await self._make_request(
                'GET', 
                f'/token/{chain}/{token_address}/price', 
                params=params
            )
            return response.get('data', {})
            
        except Exception as e:
            logger.error(f"Failed to get price history for {token_address}: {e}")
            return {}
    
    async def search_gaming_tokens(self, gaming_keywords: List[str]) -> Dict[str, Any]:
        """Search for gaming-related tokens"""
        gaming_tokens = []
        
        for keyword in gaming_keywords:
            try:
                params = {
                    'query': keyword,
                    'limit': 10
                }
                
                response = await self._make_request('GET', '/token/search', params=params)
                tokens = response.get('data', [])
                
                # Filter for gaming-related tokens
                for token in tokens:
                    name = token.get('name', '').lower()
                    symbol = token.get('symbol', '').lower()
                    
                    if any(gaming_word in name or gaming_word in symbol 
                          for gaming_word in ['game', 'gaming', 'play', 'nft', 'metaverse']):
                        gaming_tokens.append(token)
                        
            except Exception as e:
                logger.error(f"Failed to search for keyword {keyword}: {e}")
                continue
        
        return {
            'gaming_tokens': gaming_tokens
        }
    
    async def get_dex_analytics(self, chain: str = 'ethereum') -> Dict[str, Any]:
        """Get DEX analytics for a specific chain"""
        try:
            # Get top pools by volume
            hot_pools = await self.get_hot_pools(limit=20)
            
            # Get trending tokens
            trending = await self.get_trending_tokens(chain=chain, limit=20)
            
            return {
                'chain': chain,
                'hot_pools': hot_pools,
                'trending_tokens': trending
            }
            
        except Exception as e:
            logger.error(f"Failed to get DEX analytics: {e}")
            return {}
