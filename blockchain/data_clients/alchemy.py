"""
Alchemy API Client for Web3 Gaming Analytics
Comprehensive client for Portfolio, NFT, Token, and Transfer APIs
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass
import json

from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class AlchemyNetworkConfig:
    """Network configuration for Alchemy API"""
    name: str
    alchemy_id: str
    chain_id: int
    native_token: str
    gaming_focus: bool = False


class AlchemyClient(BaseBlockchainDataClient):
    """Enhanced Alchemy API client for gaming analytics"""
    
    # Network mappings for gaming-focused chains
    NETWORK_CONFIGS = {
        "ethereum": AlchemyNetworkConfig("Ethereum", "eth-mainnet", 1, "ETH", True),
        "polygon": AlchemyNetworkConfig("Polygon", "polygon-mainnet", 137, "MATIC", True),
        "arbitrum": AlchemyNetworkConfig("Arbitrum", "arbitrum-mainnet", 42161, "ETH", True),
        "optimism": AlchemyNetworkConfig("Optimism", "optimism-mainnet", 10, "ETH", True),
        "base": AlchemyNetworkConfig("Base", "base-mainnet", 8453, "ETH", True),
        "bsc": AlchemyNetworkConfig("BSC", "bsc-mainnet", 56, "BNB", True),
    }

    # API base URLs for different services
    PRICES_API_BASE = "https://api.g.alchemy.com/prices/v1"
    NFT_API_BASE = "https://{network}.g.alchemy.com/nft/v3"
    NODE_API_BASE = "https://{network}.g.alchemy.com/v2"

    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.alchemy_api_key,
            base_url=settings.blockchain_data.alchemy_base_url,
            rate_limit=settings.blockchain_data.rate_limit_per_minute
        )
        self.supported_networks = settings.blockchain_data.alchemy_supported_networks
        self._cache = {}
        self._cache_ttl = timedelta(minutes=5)  # 5-minute cache for API responses
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Alchemy API"""
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    
    def _get_prices_api_url(self, endpoint: str) -> str:
        """Construct Prices API URL with API key"""
        return f"{self.PRICES_API_BASE}/{self.api_key}/{endpoint}"

    def _get_nft_api_url(self, network: str, endpoint: str) -> str:
        """Construct NFT API URL with network and API key"""
        return f"{self.NFT_API_BASE.format(network=network)}/{self.api_key}/{endpoint}"

    def _get_node_api_url(self, network: str) -> str:
        """Construct Node API URL with network and API key"""
        return f"{self.NODE_API_BASE.format(network=network)}/{self.api_key}"

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for API requests"""
        return {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self._cache:
            return False
        
        cached_time, _ = self._cache[cache_key]
        return datetime.now(timezone.utc) - cached_time < self._cache_ttl
    
    def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get cached data if valid"""
        if self._is_cache_valid(cache_key):
            _, data = self._cache[cache_key]
            return data
        return None
    
    def _cache_data(self, cache_key: str, data: Any):
        """Cache data with timestamp"""
        self._cache[cache_key] = (datetime.now(timezone.utc), data)
    
    async def _test_endpoint(self):
        """Test Alchemy API connection with a simple request"""
        # Test with a simple token metadata request
        test_endpoint = f"{self.api_key}/assets/tokens/by-address"
        test_payload = {
            "addresses": [{
                "address": "******************************************",  # Zero address
                "networks": ["eth-mainnet"]
            }],
            "withMetadata": False,
            "withPrices": False
        }
        
        response = await self._make_request('POST', test_endpoint, data=test_payload)
        if 'data' not in response:
            raise Exception("Invalid response format from Alchemy API")
    
    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """
        Get gaming token data using Alchemy Portfolio API

        Args:
            tokens: List of token symbols (e.g., ['AXS', 'SLP'])

        Returns:
            List of token data with prices, metadata, and analytics
        """
        if not tokens:
            return []

        cache_key = f"gaming_tokens_{hash(tuple(sorted(tokens)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        async with self:  # Use async context manager
            try:
                # Get token contract addresses from gaming project manager
                from config.gaming_config import gaming_project_manager

                # Build token address mapping from gaming projects
                token_addresses = {}
                for project_name, project in gaming_project_manager.projects.items():
                    for token in project.tokens:
                        if token.symbol in tokens:
                            # Map symbol to contract address and network
                            network_config = self.get_network_config(project.blockchain.lower())
                            if network_config:
                                token_addresses[token.symbol] = {
                                    'address': token.contract_address,
                                    'network': network_config.alchemy_id,
                                    'project': project.project_name,
                                    'type': token.token_type
                                }

                if not token_addresses:
                    logger.warning(f"No contract addresses found for tokens: {tokens}")
                    result = []
                    self._cache_data(cache_key, result)
                    return result

                # Prepare addresses for Alchemy Portfolio API
                addresses_by_network = {}
                for symbol, token_info in token_addresses.items():
                    network = token_info['network']
                    if network not in addresses_by_network:
                        addresses_by_network[network] = []
                    addresses_by_network[network].append({
                        'address': token_info['address'],
                        'symbol': symbol,
                        'project': token_info['project'],
                        'type': token_info['type']
                    })

                # Fetch token data from Alchemy
                all_token_data = []
                for network, token_list in addresses_by_network.items():
                    try:
                        # Use Prices API to get token prices by address (correct format)
                        prices_url = self._get_prices_api_url("tokens/by-address")
                        payload = {
                            "addresses": [{
                                "network": network,
                                "address": token['address']
                            } for token in token_list]
                        }

                        # Make direct request to Prices API
                        if not self.session:
                            raise RuntimeError("Client session not initialized. Use async context manager.")

                        async with self.session.post(prices_url, json=payload, headers=self._get_auth_headers()) as resp:
                            if resp.status == 200:
                                response = await resp.json()
                            else:
                                logger.error(f"Prices API request failed: {resp.status}")
                                response = {"data": []}

                        if 'data' in response:
                            for i, token_data in enumerate(response['data']):
                                if i < len(token_list):
                                    token_info = token_list[i]

                                    # Extract price data from correct response structure
                                    prices_list = token_data.get('prices', [])
                                    price_info = prices_list[0] if prices_list else {}

                                    gaming_token = {
                                        'symbol': token_info['symbol'],
                                        'name': token_info['symbol'],  # Use symbol as name for now
                                        'contract_address': token_info['address'],
                                        'network': network,
                                        'project': token_info['project'],
                                        'token_type': token_info['type'],
                                        'decimals': 18,  # Default decimals
                                        'logo': None,
                                        'price_usd': float(price_info.get('value', 0)) if price_info.get('value') else None,
                                        'price_currency': price_info.get('currency'),
                                        'last_updated': datetime.now(timezone.utc).isoformat(),
                                        'data_source': 'alchemy'
                                    }

                                    all_token_data.append(gaming_token)

                    except Exception as e:
                        logger.error(f"Error fetching token data for network {network}: {e}")
                        continue

                logger.info(f"✅ Retrieved data for {len(all_token_data)} gaming tokens via Alchemy")
                self._cache_data(cache_key, all_token_data)
                return all_token_data

            except Exception as e:
                logger.error(f"Error fetching gaming tokens data: {e}")
                result = []
                self._cache_data(cache_key, result)
                return result
    
    async def get_gaming_wallet_portfolio(
        self, 
        addresses: List[str], 
        networks: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Get comprehensive gaming wallet portfolio data"""
        if not addresses:
            return {}
        
        if not networks:
            networks = ["eth-mainnet", "polygon-mainnet"]  # Default gaming networks
        
        cache_key = f"wallet_portfolio_{hash(tuple(sorted(addresses)))}_{hash(tuple(sorted(networks)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        # Prepare request payload
        address_network_pairs = []
        for address in addresses[:2]:  # Alchemy limit: 2 addresses
            for network in networks[:5]:  # Alchemy limit: 5 networks per address
                if network in self.supported_networks:
                    address_network_pairs.append({
                        "address": address,
                        "networks": [network]
                    })
        
        if not address_network_pairs:
            return {}
        
        payload = {
            "addresses": address_network_pairs,
            "withMetadata": True,
            "withPrices": True,
            "includeNativeTokens": True
        }
        
        try:
            endpoint = f"{self.api_key}/assets/tokens/by-address"
            response = await self._make_request('POST', endpoint, data=payload)
            
            result = {
                'addresses': addresses,
                'networks': networks,
                'portfolio_data': response.get('data', []),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            self._cache_data(cache_key, result)
            return result
            
        except Exception as e:
            logger.error(f"Failed to get gaming wallet portfolio: {e}")
            return {}
    
    async def get_transaction_history(
        self, 
        addresses: List[str], 
        networks: Optional[List[str]] = None,
        limit: int = 25
    ) -> Dict[str, Any]:
        """Get transaction history for gaming wallets"""
        if not addresses:
            return {}
        
        if not networks:
            networks = ["eth-mainnet"]  # Currently limited to Ethereum and Base
        
        cache_key = f"tx_history_{hash(tuple(sorted(addresses)))}_{hash(tuple(sorted(networks)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        # Alchemy currently limits to 1 address for transaction history
        address = addresses[0]
        network = networks[0] if networks[0] in ["eth-mainnet", "base-mainnet"] else "eth-mainnet"
        
        payload = {
            "addresses": [{
                "address": address,
                "networks": [network]
            }],
            "limit": min(limit, 50)  # Alchemy max: 50
        }
        
        try:
            endpoint = f"{self.api_key}/transactions/history/by-address"
            response = await self._make_request('POST', endpoint, data=payload)
            
            result = {
                'address': address,
                'network': network,
                'transactions': response.get('transactions', []),
                'total_count': response.get('totalCount', 0),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            self._cache_data(cache_key, result)
            return result
            
        except Exception as e:
            logger.error(f"Failed to get transaction history: {e}")
            return {}
    
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """
        Get NFT collection data using Alchemy NFT API

        Args:
            collection_address: Contract address of the NFT collection

        Returns:
            Dictionary containing collection metadata, floor price, and analytics
        """
        if not collection_address:
            return {}

        cache_key = f"nft_collection_{collection_address}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        async with self:  # Use async context manager
            try:
                # Get collection metadata and floor price from gaming project manager
                from config.gaming_config import gaming_project_manager

                # Find the project and NFT info for this collection
                collection_info = None
                project_info = None

                for project_name, project in gaming_project_manager.projects.items():
                    for nft in project.nfts:
                        if nft.contract_address.lower() == collection_address.lower():
                            collection_info = nft
                            project_info = project
                            break
                    if collection_info:
                        break

                # Determine network for this collection
                network = None
                if project_info:
                    network_config = self.get_network_config(project_info.blockchain.lower())
                    if network_config:
                        network = network_config.alchemy_id

                if not network:
                    # Default to Ethereum if network not found
                    network = "eth-mainnet"
                    logger.warning(f"Network not found for collection {collection_address}, defaulting to Ethereum")

                # Use Alchemy NFT API to get collection metadata
                nft_url = self._get_nft_api_url(network, "getContractMetadata")
                params = {
                    'contractAddress': collection_address
                }

                # Make direct request to NFT API
                if not self.session:
                    raise RuntimeError("Client session not initialized. Use async context manager.")

                async with self.session.get(nft_url, params=params, headers=self._get_auth_headers()) as resp:
                    if resp.status == 200:
                        response = await resp.json()
                    else:
                        logger.error(f"NFT API request failed: {resp.status}")
                        response = {}

                # Extract collection data (NFT API returns data directly)
                contract_metadata = response

                # Get floor price using NFT API
                floor_price_data = await self._get_nft_floor_price(collection_address, network)

                # Get collection stats
                stats_data = await self._get_nft_collection_stats(collection_address, network)

                # Build comprehensive collection data
                result = {
                    'collection_address': collection_address,
                    'network': network,
                    'name': contract_metadata.get('name', collection_info.name if collection_info else 'Unknown'),
                    'symbol': contract_metadata.get('symbol', ''),
                    'description': collection_info.function if collection_info else '',
                    'total_supply': contract_metadata.get('totalSupply'),
                    'contract_type': contract_metadata.get('tokenType', 'ERC721'),
                    'verified': True,  # Assume verified if we have data

                    # Gaming-specific data
                    'gaming_project': project_info.project_name if project_info else None,
                    'gaming_category': 'Gaming NFT',  # Default category for gaming NFTs
                    'nft_function': collection_info.function if collection_info else None,
                    'marketplace_links': [collection_info.marketplace_link] if collection_info and collection_info.marketplace_link else [],

                    # Market data
                    'floor_price': floor_price_data.get('floor_price'),
                    'floor_price_currency': floor_price_data.get('currency', 'ETH'),
                    'volume_24h': stats_data.get('volume_24h'),
                    'holders_count': stats_data.get('holders_count'),
                    'sales_count_24h': stats_data.get('sales_count_24h'),

                    # Metadata
                    'image_url': contract_metadata.get('openSeaMetadata', {}).get('imageUrl'),
                    'external_url': contract_metadata.get('openSeaMetadata', {}).get('externalUrl'),
                    'discord_url': contract_metadata.get('openSeaMetadata', {}).get('discordUrl'),
                    'twitter_username': contract_metadata.get('openSeaMetadata', {}).get('twitterUsername'),

                    # Analytics
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'data_source': 'alchemy',
                    'api_endpoints_used': ['getContractMetadata', 'getFloorPrice', 'getCollectionStats']
                }

                logger.info(f"✅ Retrieved NFT collection data for {collection_address} via Alchemy")
                self._cache_data(cache_key, result)
                return result

            except Exception as e:
                logger.error(f"Error fetching NFT collection data for {collection_address}: {e}")

                # Return basic data structure on error
                result = {
                    'collection_address': collection_address,
                    'error': str(e),
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'data_source': 'alchemy'
                }

                self._cache_data(cache_key, result)
                return result

    async def _get_nft_floor_price(self, collection_address: str, network: str) -> Dict[str, Any]:
        """Get NFT collection floor price using Alchemy NFT API"""
        try:
            nft_url = self._get_nft_api_url(network, "getFloorPrice")
            params = {
                'contractAddress': collection_address
            }

            # Make direct request to NFT API
            if not self.session:
                raise RuntimeError("Client session not initialized. Use async context manager.")

            async with self.session.get(nft_url, params=params, headers=self._get_auth_headers()) as resp:
                if resp.status == 200:
                    response = await resp.json()
                else:
                    logger.error(f"NFT Floor Price API request failed: {resp.status}")
                    response = {}

            floor_price_data = response.get('openSea', {})
            return {
                'floor_price': floor_price_data.get('floorPrice'),
                'currency': floor_price_data.get('priceCurrency', 'ETH'),
                'marketplace': 'OpenSea'
            }

        except Exception as e:
            logger.warning(f"Could not fetch floor price for {collection_address}: {e}")
            return {}

    async def _get_nft_collection_stats(self, collection_address: str, network: str) -> Dict[str, Any]:
        """Get NFT collection statistics using Alchemy NFT API"""
        try:
            # Get owners count
            nft_url = self._get_nft_api_url(network, "getOwnersForContract")
            params = {
                'contractAddress': collection_address,
                'withTokenBalances': 'false'
            }

            # Make direct request to NFT API
            if not self.session:
                raise RuntimeError("Client session not initialized. Use async context manager.")

            async with self.session.get(nft_url, params=params, headers=self._get_auth_headers()) as resp:
                if resp.status == 200:
                    response = await resp.json()
                else:
                    logger.error(f"NFT Owners API request failed: {resp.status}")
                    response = {"owners": []}
            owners_count = len(response.get('owners', []))

            # Additional stats would require more API calls
            # For now, return basic holder count
            return {
                'holders_count': owners_count,
                'volume_24h': None,  # Would need marketplace API
                'sales_count_24h': None  # Would need marketplace API
            }

        except Exception as e:
            logger.warning(f"Could not fetch collection stats for {collection_address}: {e}")
            return {}

    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """
        Get gaming protocol metrics using Alchemy Transfer API

        Args:
            protocol_name: Name of the gaming protocol (e.g., 'axie-infinity')

        Returns:
            Dictionary containing protocol transaction metrics and analytics
        """
        cache_key = f"protocol_metrics_{protocol_name}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        async with self:  # Use async context manager
            try:
                # Get protocol information from gaming project manager
                from config.gaming_config import gaming_project_manager

                # Find the project configuration
                project = gaming_project_manager.projects.get(protocol_name)
                if not project:
                    logger.warning(f"Protocol {protocol_name} not found in gaming projects")
                    result = {
                        'protocol_name': protocol_name,
                        'error': 'Protocol not found in gaming projects database',
                        'last_updated': datetime.now(timezone.utc).isoformat()
                    }
                    self._cache_data(cache_key, result)
                    return result

                # Determine network for this protocol
                network_config = self.get_network_config(project.blockchain.lower())
                if not network_config:
                    logger.warning(f"Network {project.blockchain} not supported for protocol {protocol_name}")
                    result = {
                        'protocol_name': protocol_name,
                        'error': f'Network {project.blockchain} not supported',
                        'last_updated': datetime.now(timezone.utc).isoformat()
                    }
                    self._cache_data(cache_key, result)
                    return result

                network = network_config.alchemy_id

                # Collect all contract addresses for this protocol
                contract_addresses = []

                # Add token contracts
                for token in project.tokens:
                    if token.contract_address:
                        contract_addresses.append({
                            'address': token.contract_address,
                            'type': 'token',
                            'symbol': token.symbol,
                            'name': token.symbol
                        })

                # Add NFT contracts
                for nft in project.nfts:
                    if nft.contract_address:
                        contract_addresses.append({
                            'address': nft.contract_address,
                            'type': 'nft',
                            'symbol': nft.name,
                            'name': nft.name
                        })

                if not contract_addresses:
                    logger.warning(f"No contract addresses found for protocol {protocol_name}")
                    result = {
                        'protocol_name': protocol_name,
                        'error': 'No contract addresses found',
                        'last_updated': datetime.now(timezone.utc).isoformat()
                    }
                    self._cache_data(cache_key, result)
                    return result

                # Get transfer activity for protocol contracts
                protocol_metrics = {
                    'protocol_name': protocol_name,
                    'project_name': project.project_name,
                    'blockchain': project.blockchain,
                    'network': network,
                    'status': project.status,
                    'daily_active_users': project.daily_active_users,
                    'daily_unique_wallets': project.daily_unique_wallets,
                    'contracts': contract_addresses,
                    'transfer_metrics': {},
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'data_source': 'alchemy'
                }

                # Get transfer data for each contract (limited to avoid rate limits)
                for contract in contract_addresses[:3]:  # Limit to first 3 contracts
                    try:
                        transfer_data = await self._get_contract_transfer_metrics(
                            contract['address'],
                            network,
                            contract['type']
                        )
                        protocol_metrics['transfer_metrics'][contract['address']] = transfer_data

                    except Exception as e:
                        logger.warning(f"Could not get transfer metrics for {contract['address']}: {e}")
                        continue

                logger.info(f"✅ Retrieved protocol metrics for {protocol_name} via Alchemy")
                self._cache_data(cache_key, protocol_metrics)
                return protocol_metrics

            except Exception as e:
                logger.error(f"Error fetching gaming protocol metrics for {protocol_name}: {e}")

                result = {
                    'protocol_name': protocol_name,
                    'error': str(e),
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'data_source': 'alchemy'
                }

                self._cache_data(cache_key, result)
                return result

    async def _get_contract_transfer_metrics(self, contract_address: str, network: str, contract_type: str) -> Dict[str, Any]:
        """Get transfer metrics for a specific contract using Alchemy Transfer API"""
        try:
            # Use Alchemy Node API to get recent transfer activity
            node_url = self._get_node_api_url(network)

            # Get recent transfers for this contract (correct JSON-RPC format)
            payload = {
                "jsonrpc": "2.0",
                "method": "alchemy_getAssetTransfers",
                "params": [{
                    "fromBlock": "0x0",
                    "toBlock": "latest",
                    "contractAddresses": [contract_address],
                    "category": ["erc20", "erc721", "erc1155"] if contract_type == "token" else ["erc721", "erc1155"],
                    "maxCount": "0x64",
                    "order": "desc"
                }],
                "id": 1
            }

            # Make direct request to Node API
            if not self.session:
                raise RuntimeError("Client session not initialized. Use async context manager.")

            async with self.session.post(node_url, json=payload, headers=self._get_auth_headers()) as resp:
                if resp.status == 200:
                    response = await resp.json()
                else:
                    logger.error(f"Node API request failed: {resp.status}")
                    response = {"result": {"transfers": []}}

            transfers = response.get('result', {}).get('transfers', [])

            # Analyze transfer data
            unique_addresses = set()
            total_transfers = len(transfers)

            for transfer in transfers:
                if transfer.get('from'):
                    unique_addresses.add(transfer['from'])
                if transfer.get('to'):
                    unique_addresses.add(transfer['to'])

            return {
                'contract_address': contract_address,
                'contract_type': contract_type,
                'total_transfers_recent': total_transfers,
                'unique_addresses_recent': len(unique_addresses),
                'sample_transfers': transfers[:5],  # First 5 transfers as sample
                'last_updated': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.warning(f"Could not fetch transfer metrics for {contract_address}: {e}")
            return {
                'contract_address': contract_address,
                'contract_type': contract_type,
                'error': str(e),
                'last_updated': datetime.now(timezone.utc).isoformat()
            }

    def get_supported_networks(self) -> List[str]:
        """Get list of supported networks for gaming analytics"""
        return list(self.NETWORK_CONFIGS.keys())
    
    def get_network_config(self, network: str) -> Optional[AlchemyNetworkConfig]:
        """Get network configuration with blockchain mapping support"""
        # Handle blockchain name variations and mappings
        blockchain_mappings = {
            'ethereum, ronin': 'ethereum',  # For multi-chain projects, default to Ethereum
            'avalanche, ethereum': 'ethereum',  # For multi-chain projects, default to Ethereum
            'ronin': 'ethereum',  # Ronin is not supported by Alchemy, use Ethereum
            'bsc': 'bsc',
            'polygon': 'polygon',
            'arbitrum': 'arbitrum',
            'optimism': 'optimism',
            'base': 'base',
            'solana': None,  # Solana not supported by Alchemy yet
            'avalanche': None,  # Avalanche not supported by Alchemy yet
            'ton': None  # TON not supported by Alchemy yet
        }

        network_key = network.lower().strip()
        mapped_network = blockchain_mappings.get(network_key, network_key)

        if mapped_network is None:
            return None

        return self.NETWORK_CONFIGS.get(mapped_network)
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check for Alchemy client"""
        try:
            await self._test_endpoint()
            return {
                'status': 'healthy',
                'api_key_configured': bool(self.api_key),
                'supported_networks': len(self.supported_networks),
                'cache_entries': len(self._cache),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }


# Global instance for easy access
alchemy_client = AlchemyClient()
