"""
Alchemy API Client for Web3 Gaming Analytics
Comprehensive client for Portfolio, NFT, Token, and Transfer APIs
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass
import json

from .base import BaseBlockchainDataClient
from config.settings import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


@dataclass
class AlchemyNetworkConfig:
    """Network configuration for Alchemy API"""
    name: str
    alchemy_id: str
    chain_id: int
    native_token: str
    gaming_focus: bool = False


@dataclass
class GamingAsset:
    """Gaming asset information"""
    token_address: str
    symbol: str
    name: str
    balance: str
    balance_usd: float
    price_usd: float
    network: str
    is_gaming_token: bool = False
    is_nft: bool = False
    gaming_project: Optional[str] = None
    asset_type: str = "token"  # token, nft, native
    metadata: Dict[str, Any] = None


@dataclass
class GamingPortfolio:
    """Comprehensive gaming wallet portfolio"""
    wallet_address: str
    total_value_usd: float
    gaming_value_usd: float
    gaming_percentage: float
    networks: List[str]
    assets: List[GamingAsset]
    gaming_projects: List[str]
    nft_collections: List[str]
    last_updated: datetime

    def get_gaming_assets(self) -> List[GamingAsset]:
        """Get only gaming-related assets"""
        return [asset for asset in self.assets if asset.is_gaming_token or asset.is_nft]

    def get_assets_by_network(self, network: str) -> List[GamingAsset]:
        """Get assets for a specific network"""
        return [asset for asset in self.assets if asset.network == network]

    def get_gaming_allocation(self) -> Dict[str, float]:
        """Get gaming project allocation breakdown"""
        allocation = {}
        for asset in self.get_gaming_assets():
            if asset.gaming_project:
                allocation[asset.gaming_project] = allocation.get(asset.gaming_project, 0) + asset.balance_usd
        return allocation


@dataclass
class NFTCollectionMetrics:
    """NFT collection analysis metrics"""
    collection_address: str
    collection_name: str
    network: str
    total_supply: int
    floor_price: float
    floor_price_usd: float
    volume_24h: float
    holders_count: int
    rarity_scores: Dict[str, float]
    trait_distribution: Dict[str, Dict[str, int]]
    gaming_project: Optional[str] = None
    utility_type: str = ""  # character, land, item, etc.


@dataclass
class CrossCollectionAnalysis:
    """Cross-collection holder analysis"""
    wallet_address: str
    collections_held: List[str]
    total_nfts: int
    total_value_usd: float
    whale_score: float  # 0-100 based on holdings
    gaming_focus_score: float  # 0-100 based on gaming NFT ratio
    collection_overlap: Dict[str, List[str]]  # collections that share holders


class AlchemyClient(BaseBlockchainDataClient):
    """Enhanced Alchemy API client for gaming analytics"""
    
    # Network mappings for gaming-focused chains
    NETWORK_CONFIGS = {
        "ethereum": AlchemyNetworkConfig("Ethereum", "eth-mainnet", 1, "ETH", True),
        "polygon": AlchemyNetworkConfig("Polygon", "polygon-mainnet", 137, "MATIC", True),
        "arbitrum": AlchemyNetworkConfig("Arbitrum", "arbitrum-mainnet", 42161, "ETH", True),
        "optimism": AlchemyNetworkConfig("Optimism", "optimism-mainnet", 10, "ETH", True),
        "base": AlchemyNetworkConfig("Base", "base-mainnet", 8453, "ETH", True),
        "bsc": AlchemyNetworkConfig("BSC", "bsc-mainnet", 56, "BNB", True),
    }

    # API base URLs for different services
    PRICES_API_BASE = "https://api.g.alchemy.com/prices/v1"
    NFT_API_BASE = "https://{network}.g.alchemy.com/nft/v3"
    NODE_API_BASE = "https://{network}.g.alchemy.com/v2"

    def __init__(self):
        super().__init__(
            api_key=settings.blockchain_data.alchemy_api_key,
            base_url=settings.blockchain_data.alchemy_base_url,
            rate_limit=settings.blockchain_data.rate_limit_per_minute
        )
        self.supported_networks = settings.blockchain_data.alchemy_supported_networks
        self._cache = {}
        self._cache_ttl = timedelta(minutes=5)  # 5-minute cache for API responses
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for Alchemy API"""
        return {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    
    def _get_prices_api_url(self, endpoint: str) -> str:
        """Construct Prices API URL with API key"""
        return f"{self.PRICES_API_BASE}/{self.api_key}/{endpoint}"

    def _get_nft_api_url(self, network: str, endpoint: str) -> str:
        """Construct NFT API URL with network and API key"""
        return f"{self.NFT_API_BASE.format(network=network)}/{self.api_key}/{endpoint}"

    def _get_node_api_url(self, network: str) -> str:
        """Construct Node API URL with network and API key"""
        return f"{self.NODE_API_BASE.format(network=network)}/{self.api_key}"

    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for API requests"""
        return {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self._cache:
            return False
        
        cached_time, _ = self._cache[cache_key]
        return datetime.now(timezone.utc) - cached_time < self._cache_ttl
    
    def _get_cached_data(self, cache_key: str) -> Optional[Any]:
        """Get cached data if valid"""
        if self._is_cache_valid(cache_key):
            _, data = self._cache[cache_key]
            return data
        return None
    
    def _cache_data(self, cache_key: str, data: Any):
        """Cache data with timestamp"""
        self._cache[cache_key] = (datetime.now(timezone.utc), data)
    
    async def _test_endpoint(self):
        """Test Alchemy API connection with a simple request"""
        # Test with a simple token metadata request
        test_endpoint = f"{self.api_key}/assets/tokens/by-address"
        test_payload = {
            "addresses": [{
                "address": "******************************************",  # Zero address
                "networks": ["eth-mainnet"]
            }],
            "withMetadata": False,
            "withPrices": False
        }
        
        response = await self._make_request('POST', test_endpoint, data=test_payload)
        if 'data' not in response:
            raise Exception("Invalid response format from Alchemy API")
    
    async def get_gaming_tokens_data(self, tokens: List[str]) -> List[Dict[str, Any]]:
        """
        Get gaming token data using Alchemy Portfolio API

        Args:
            tokens: List of token symbols (e.g., ['AXS', 'SLP'])

        Returns:
            List of token data with prices, metadata, and analytics
        """
        if not tokens:
            return []

        cache_key = f"gaming_tokens_{hash(tuple(sorted(tokens)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        async with self:  # Use async context manager
            try:
                # Get token contract addresses from gaming project manager
                from config.gaming_config import gaming_project_manager

                # Build token address mapping from gaming projects
                token_addresses = {}
                for project_name, project in gaming_project_manager.projects.items():
                    for token in project.tokens:
                        if token.symbol in tokens:
                            # Map symbol to contract address and network
                            network_config = self.get_network_config(project.blockchain.lower())
                            if network_config:
                                token_addresses[token.symbol] = {
                                    'address': token.contract_address,
                                    'network': network_config.alchemy_id,
                                    'project': project.project_name,
                                    'type': token.token_type
                                }

                if not token_addresses:
                    logger.warning(f"No contract addresses found for tokens: {tokens}")
                    result = []
                    self._cache_data(cache_key, result)
                    return result

                # Prepare addresses for Alchemy Portfolio API
                addresses_by_network = {}
                for symbol, token_info in token_addresses.items():
                    network = token_info['network']
                    if network not in addresses_by_network:
                        addresses_by_network[network] = []
                    addresses_by_network[network].append({
                        'address': token_info['address'],
                        'symbol': symbol,
                        'project': token_info['project'],
                        'type': token_info['type']
                    })

                # Fetch token data from Alchemy
                all_token_data = []
                for network, token_list in addresses_by_network.items():
                    try:
                        # Use Prices API to get token prices by address (correct format)
                        prices_url = self._get_prices_api_url("tokens/by-address")
                        payload = {
                            "addresses": [{
                                "network": network,
                                "address": token['address']
                            } for token in token_list]
                        }

                        # Make direct request to Prices API
                        if not self.session:
                            raise RuntimeError("Client session not initialized. Use async context manager.")

                        async with self.session.post(prices_url, json=payload, headers=self._get_auth_headers()) as resp:
                            if resp.status == 200:
                                response = await resp.json()
                            else:
                                logger.error(f"Prices API request failed: {resp.status}")
                                response = {"data": []}

                        if 'data' in response:
                            for i, token_data in enumerate(response['data']):
                                if i < len(token_list):
                                    token_info = token_list[i]

                                    # Extract price data from correct response structure
                                    prices_list = token_data.get('prices', [])
                                    price_info = prices_list[0] if prices_list else {}

                                    gaming_token = {
                                        'symbol': token_info['symbol'],
                                        'name': token_info['symbol'],  # Use symbol as name for now
                                        'contract_address': token_info['address'],
                                        'network': network,
                                        'project': token_info['project'],
                                        'token_type': token_info['type'],
                                        'decimals': 18,  # Default decimals
                                        'logo': None,
                                        'price_usd': float(price_info.get('value', 0)) if price_info.get('value') else None,
                                        'price_currency': price_info.get('currency'),
                                        'last_updated': datetime.now(timezone.utc).isoformat(),
                                        'data_source': 'alchemy'
                                    }

                                    all_token_data.append(gaming_token)

                    except Exception as e:
                        logger.error(f"Error fetching token data for network {network}: {e}")
                        continue

                logger.info(f"✅ Retrieved data for {len(all_token_data)} gaming tokens via Alchemy")
                self._cache_data(cache_key, all_token_data)
                return all_token_data

            except Exception as e:
                logger.error(f"Error fetching gaming tokens data: {e}")
                result = []
                self._cache_data(cache_key, result)
                return result
    
    async def get_gaming_wallet_portfolio(
        self,
        addresses: List[str],
        networks: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Get comprehensive gaming wallet portfolio data with enhanced analytics"""
        if not addresses:
            return {}

        if not networks:
            networks = ["eth-mainnet", "polygon-mainnet", "arbitrum-mainnet", "optimism-mainnet", "base-mainnet"]

        cache_key = f"enhanced_wallet_portfolio_{hash(tuple(sorted(addresses)))}_{hash(tuple(sorted(networks)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        try:
            portfolios = []

            for address in addresses[:5]:  # Process up to 5 addresses
                portfolio = await self._get_single_wallet_portfolio(address, networks)
                if portfolio:
                    portfolios.append(portfolio)

            result = {
                'portfolios': portfolios,
                'summary': self._calculate_portfolio_summary(portfolios),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            self._cache_data(cache_key, result)
            return result

        except Exception as e:
            logger.error(f"Failed to get enhanced gaming wallet portfolio: {e}")
            return {}

    async def _get_single_wallet_portfolio(self, address: str, networks: List[str]) -> Optional[GamingPortfolio]:
        """Get portfolio data for a single wallet address"""
        try:
            # Get token balances across all networks
            all_assets = []
            total_value_usd = 0.0
            gaming_value_usd = 0.0
            gaming_projects = set()
            nft_collections = set()

            for network in networks:
                if network not in self.supported_networks:
                    continue

                # Get token balances
                tokens = await self._get_wallet_tokens(address, network)
                if tokens:
                    for token_data in tokens:
                        asset = await self._process_token_asset(token_data, network)
                        if asset:
                            all_assets.append(asset)
                            total_value_usd += asset.balance_usd

                            if asset.is_gaming_token:
                                gaming_value_usd += asset.balance_usd
                                if asset.gaming_project:
                                    gaming_projects.add(asset.gaming_project)

                # Get NFT holdings
                nfts = await self._get_wallet_nfts(address, network)
                if nfts:
                    for nft_data in nfts:
                        asset = await self._process_nft_asset(nft_data, network)
                        if asset:
                            all_assets.append(asset)
                            total_value_usd += asset.balance_usd

                            if asset.is_nft:
                                gaming_value_usd += asset.balance_usd
                                if asset.gaming_project:
                                    gaming_projects.add(asset.gaming_project)
                                nft_collections.add(asset.token_address)

            gaming_percentage = (gaming_value_usd / total_value_usd * 100) if total_value_usd > 0 else 0

            return GamingPortfolio(
                wallet_address=address,
                total_value_usd=total_value_usd,
                gaming_value_usd=gaming_value_usd,
                gaming_percentage=gaming_percentage,
                networks=networks,
                assets=all_assets,
                gaming_projects=list(gaming_projects),
                nft_collections=list(nft_collections),
                last_updated=datetime.now(timezone.utc)
            )

        except Exception as e:
            logger.error(f"Failed to get portfolio for address {address}: {e}")
            return None

    async def _get_wallet_tokens(self, address: str, network: str) -> List[Dict[str, Any]]:
        """Get token balances for a wallet on a specific network"""
        try:
            payload = {
                "addresses": [{
                    "address": address,
                    "networks": [network]
                }],
                "withMetadata": True,
                "withPrices": True,
                "includeNativeTokens": True
            }

            endpoint = f"{self.api_key}/assets/tokens/by-address"
            response = await self._make_request('POST', endpoint, data=payload)

            return response.get('data', [])

        except Exception as e:
            logger.error(f"Failed to get tokens for {address} on {network}: {e}")
            return []

    async def _get_wallet_nfts(self, address: str, network: str) -> List[Dict[str, Any]]:
        """Get NFT holdings for a wallet on a specific network"""
        try:
            # Use NFT API to get owned NFTs
            nft_endpoint = f"https://{network}.g.alchemy.com/nft/v3/{self.api_key}/getNFTsForOwner"
            params = {
                "owner": address,
                "withMetadata": "true",
                "pageSize": "100"
            }

            response = await self._make_request('GET', nft_endpoint, params=params)
            return response.get('ownedNfts', [])

        except Exception as e:
            logger.error(f"Failed to get NFTs for {address} on {network}: {e}")
            return []

    async def _process_token_asset(self, token_data: Dict[str, Any], network: str) -> Optional[GamingAsset]:
        """Process token data into GamingAsset object"""
        try:
            # Extract token information
            token_address = token_data.get('contractAddress', '')
            symbol = token_data.get('symbol', '')
            name = token_data.get('name', '')
            balance = token_data.get('balance', '0')
            price_usd = float(token_data.get('price', {}).get('usd', 0))
            balance_float = float(balance) / (10 ** token_data.get('decimals', 18))
            balance_usd = balance_float * price_usd

            # Check if this is a gaming token
            is_gaming_token, gaming_project = await self._identify_gaming_token(token_address, symbol)

            return GamingAsset(
                token_address=token_address,
                symbol=symbol,
                name=name,
                balance=balance,
                balance_usd=balance_usd,
                price_usd=price_usd,
                network=network,
                is_gaming_token=is_gaming_token,
                is_nft=False,
                gaming_project=gaming_project,
                asset_type="token",
                metadata=token_data
            )

        except Exception as e:
            logger.error(f"Failed to process token asset: {e}")
            return None

    async def _process_nft_asset(self, nft_data: Dict[str, Any], network: str) -> Optional[GamingAsset]:
        """Process NFT data into GamingAsset object"""
        try:
            # Extract NFT information
            contract_address = nft_data.get('contract', {}).get('address', '')
            name = nft_data.get('name', '')
            symbol = nft_data.get('contract', {}).get('symbol', '')
            token_id = nft_data.get('tokenId', '')

            # Estimate NFT value (placeholder - would use floor price data)
            estimated_value_usd = await self._estimate_nft_value(contract_address, network)

            # Check if this is a gaming NFT
            is_gaming_nft, gaming_project = await self._identify_gaming_nft(contract_address)

            return GamingAsset(
                token_address=contract_address,
                symbol=symbol,
                name=f"{name} #{token_id}",
                balance="1",
                balance_usd=estimated_value_usd,
                price_usd=estimated_value_usd,
                network=network,
                is_gaming_token=False,
                is_nft=True,
                gaming_project=gaming_project if is_gaming_nft else None,
                asset_type="nft",
                metadata=nft_data
            )

        except Exception as e:
            logger.error(f"Failed to process NFT asset: {e}")
            return None

    async def _identify_gaming_token(self, token_address: str, symbol: str) -> tuple[bool, Optional[str]]:
        """Identify if a token is gaming-related and which project it belongs to"""
        try:
            from config.gaming_config import gaming_project_manager

            # Check against known gaming projects
            for project_slug, project in gaming_project_manager.projects.items():
                for token in project.tokens:
                    if (token.contract_address.lower() == token_address.lower() or
                        token.symbol.upper() == symbol.upper()):
                        return True, project.project_name

            # Check against common gaming token symbols
            gaming_symbols = {
                'AXS', 'SLP', 'ATLAS', 'POLIS', 'SAND', 'MANA', 'GALA', 'ALICE', 'TLM',
                'SKILL', 'GHST', 'REVV', 'TOWER', 'NFTX', 'RARI', 'SUPER', 'GODS'
            }

            if symbol.upper() in gaming_symbols:
                return True, None

            return False, None

        except Exception as e:
            logger.error(f"Failed to identify gaming token {symbol}: {e}")
            return False, None

    async def _identify_gaming_nft(self, contract_address: str) -> tuple[bool, Optional[str]]:
        """Identify if an NFT collection is gaming-related"""
        try:
            from config.gaming_config import gaming_project_manager

            # Check against known gaming NFT collections
            for project_slug, project in gaming_project_manager.projects.items():
                for nft in project.nfts:
                    if nft.contract_address.lower() == contract_address.lower():
                        return True, project.project_name

            return False, None

        except Exception as e:
            logger.error(f"Failed to identify gaming NFT {contract_address}: {e}")
            return False, None

    async def _estimate_nft_value(self, contract_address: str, network: str) -> float:
        """Estimate NFT value using floor price data"""
        try:
            # This would integrate with NFT floor price tracking
            # For now, return a placeholder value
            return 0.1  # Placeholder value

        except Exception as e:
            logger.error(f"Failed to estimate NFT value for {contract_address}: {e}")
            return 0.0

    def _calculate_portfolio_summary(self, portfolios: List[GamingPortfolio]) -> Dict[str, Any]:
        """Calculate summary statistics across multiple portfolios"""
        if not portfolios:
            return {}

        total_value = sum(p.total_value_usd for p in portfolios)
        total_gaming_value = sum(p.gaming_value_usd for p in portfolios)

        all_gaming_projects = set()
        all_nft_collections = set()
        all_networks = set()

        for portfolio in portfolios:
            all_gaming_projects.update(portfolio.gaming_projects)
            all_nft_collections.update(portfolio.nft_collections)
            all_networks.update(portfolio.networks)

        return {
            'total_wallets': len(portfolios),
            'total_value_usd': total_value,
            'total_gaming_value_usd': total_gaming_value,
            'gaming_percentage': (total_gaming_value / total_value * 100) if total_value > 0 else 0,
            'unique_gaming_projects': len(all_gaming_projects),
            'unique_nft_collections': len(all_nft_collections),
            'networks_covered': list(all_networks),
            'gaming_projects': list(all_gaming_projects),
            'nft_collections': list(all_nft_collections)
        }

    async def get_gaming_nft_collection_analysis(
        self,
        collection_address: str,
        network: str = "eth-mainnet"
    ) -> Optional[NFTCollectionMetrics]:
        """Get comprehensive NFT collection analysis with rarity and market data"""
        cache_key = f"nft_collection_analysis_{collection_address}_{network}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        try:
            # Get collection metadata
            collection_metadata = await self._get_nft_collection_metadata(collection_address, network)
            if not collection_metadata:
                return None

            # Get collection stats
            collection_stats = await self._get_nft_collection_stats(collection_address, network)

            # Analyze rarity and traits
            rarity_analysis = await self._analyze_nft_rarity(collection_address, network)

            # Identify gaming project association
            is_gaming, gaming_project = await self._identify_gaming_nft(collection_address)

            metrics = NFTCollectionMetrics(
                collection_address=collection_address,
                collection_name=collection_metadata.get('name', ''),
                network=network,
                total_supply=collection_stats.get('totalSupply', 0),
                floor_price=collection_stats.get('floorPrice', 0.0),
                floor_price_usd=collection_stats.get('floorPriceUsd', 0.0),
                volume_24h=collection_stats.get('volume24h', 0.0),
                holders_count=collection_stats.get('holdersCount', 0),
                rarity_scores=rarity_analysis.get('rarity_scores', {}),
                trait_distribution=rarity_analysis.get('trait_distribution', {}),
                gaming_project=gaming_project if is_gaming else None,
                utility_type=self._determine_nft_utility_type(collection_metadata, gaming_project)
            )

            self._cache_data(cache_key, metrics)
            return metrics

        except Exception as e:
            logger.error(f"Failed to analyze NFT collection {collection_address}: {e}")
            return None

    async def _get_nft_collection_metadata(self, collection_address: str, network: str) -> Dict[str, Any]:
        """Get NFT collection metadata"""
        try:
            endpoint = f"https://{network}.g.alchemy.com/nft/v3/{self.api_key}/getContractMetadata"
            params = {"contractAddress": collection_address}

            response = await self._make_request('GET', endpoint, params=params)
            return response.get('contractMetadata', {})

        except Exception as e:
            logger.error(f"Failed to get collection metadata for {collection_address}: {e}")
            return {}

    async def _get_nft_collection_stats(self, collection_address: str, network: str) -> Dict[str, Any]:
        """Get NFT collection statistics"""
        try:
            # Get floor price from Alchemy
            floor_endpoint = f"https://{network}.g.alchemy.com/nft/v3/{self.api_key}/getFloorPrice"
            floor_params = {"contractAddress": collection_address}

            floor_response = await self._make_request('GET', floor_endpoint, params=floor_params)
            floor_data = floor_response.get('floorPrice', {})

            # Get collection stats
            stats_endpoint = f"https://{network}.g.alchemy.com/nft/v3/{self.api_key}/getOwnersForCollection"
            stats_params = {"contractAddress": collection_address, "withTokenBalances": "false"}

            stats_response = await self._make_request('GET', stats_endpoint, params=stats_params)

            return {
                'totalSupply': floor_data.get('totalSupply', 0),
                'floorPrice': floor_data.get('priceCurrency', {}).get('value', 0.0),
                'floorPriceUsd': floor_data.get('priceUsd', 0.0),
                'volume24h': 0.0,  # Would need marketplace API integration
                'holdersCount': len(stats_response.get('owners', []))
            }

        except Exception as e:
            logger.error(f"Failed to get collection stats for {collection_address}: {e}")
            return {}

    async def _analyze_nft_rarity(self, collection_address: str, network: str) -> Dict[str, Any]:
        """Analyze NFT rarity and trait distribution"""
        try:
            # Get sample of NFTs to analyze traits
            endpoint = f"https://{network}.g.alchemy.com/nft/v3/{self.api_key}/getNFTsForCollection"
            params = {
                "contractAddress": collection_address,
                "withMetadata": "true",
                "limit": "100"  # Sample size
            }

            response = await self._make_request('GET', endpoint, params=params)
            nfts = response.get('nfts', [])

            if not nfts:
                return {'rarity_scores': {}, 'trait_distribution': {}}

            # Analyze trait distribution
            trait_counts = {}
            total_nfts = len(nfts)

            for nft in nfts:
                metadata = nft.get('metadata', {})
                attributes = metadata.get('attributes', [])

                for attr in attributes:
                    trait_type = attr.get('trait_type', '')
                    trait_value = attr.get('value', '')

                    if trait_type not in trait_counts:
                        trait_counts[trait_type] = {}

                    trait_counts[trait_type][trait_value] = trait_counts[trait_type].get(trait_value, 0) + 1

            # Calculate rarity scores (simplified)
            rarity_scores = {}
            for nft in nfts[:10]:  # Sample for rarity calculation
                token_id = nft.get('tokenId', '')
                attributes = nft.get('metadata', {}).get('attributes', [])

                rarity_score = 0.0
                for attr in attributes:
                    trait_type = attr.get('trait_type', '')
                    trait_value = attr.get('value', '')

                    if trait_type in trait_counts and trait_value in trait_counts[trait_type]:
                        trait_rarity = 1.0 / (trait_counts[trait_type][trait_value] / total_nfts)
                        rarity_score += trait_rarity

                rarity_scores[token_id] = rarity_score

            return {
                'rarity_scores': rarity_scores,
                'trait_distribution': trait_counts
            }

        except Exception as e:
            logger.error(f"Failed to analyze rarity for {collection_address}: {e}")
            return {'rarity_scores': {}, 'trait_distribution': {}}

    def _determine_nft_utility_type(self, metadata: Dict[str, Any], gaming_project: Optional[str]) -> str:
        """Determine the utility type of NFTs based on metadata and gaming project"""
        if not gaming_project:
            return "collectible"

        name = metadata.get('name', '').lower()
        description = metadata.get('description', '').lower()

        # Common gaming NFT utility types
        if any(keyword in name or keyword in description for keyword in ['character', 'hero', 'avatar', 'player']):
            return "character"
        elif any(keyword in name or keyword in description for keyword in ['land', 'plot', 'territory', 'estate']):
            return "land"
        elif any(keyword in name or keyword in description for keyword in ['weapon', 'sword', 'armor', 'equipment', 'item']):
            return "equipment"
        elif any(keyword in name or keyword in description for keyword in ['pet', 'companion', 'mount']):
            return "companion"
        elif any(keyword in name or keyword in description for keyword in ['card', 'deck']):
            return "card"
        else:
            return "utility"

    async def get_cross_collection_holder_analysis(
        self,
        collection_addresses: List[str],
        network: str = "eth-mainnet"
    ) -> List[CrossCollectionAnalysis]:
        """Analyze holders across multiple gaming NFT collections"""
        cache_key = f"cross_collection_analysis_{hash(tuple(sorted(collection_addresses)))}_{network}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        try:
            # Get holders for each collection
            collection_holders = {}

            for collection_address in collection_addresses:
                holders = await self._get_collection_holders(collection_address, network)
                collection_holders[collection_address] = holders

            # Find cross-collection holders
            all_holders = set()
            for holders in collection_holders.values():
                all_holders.update(holders.keys())

            cross_collection_analyses = []

            for holder_address in all_holders:
                # Calculate holdings across collections
                collections_held = []
                total_nfts = 0
                total_value_usd = 0.0

                for collection_address, holders in collection_holders.items():
                    if holder_address in holders:
                        nft_count = holders[holder_address]
                        collections_held.append(collection_address)
                        total_nfts += nft_count

                        # Estimate value (would use real floor price data)
                        estimated_value = nft_count * 0.1  # Placeholder
                        total_value_usd += estimated_value

                # Only include holders with multiple collections or significant holdings
                if len(collections_held) > 1 or total_nfts > 5:
                    whale_score = min(100, total_nfts * 2)  # Simple whale scoring
                    gaming_focus_score = len(collections_held) * 20  # Gaming focus based on diversity

                    # Find collection overlaps
                    collection_overlap = {}
                    for i, collection1 in enumerate(collections_held):
                        for collection2 in collections_held[i+1:]:
                            if collection1 not in collection_overlap:
                                collection_overlap[collection1] = []
                            collection_overlap[collection1].append(collection2)

                    analysis = CrossCollectionAnalysis(
                        wallet_address=holder_address,
                        collections_held=collections_held,
                        total_nfts=total_nfts,
                        total_value_usd=total_value_usd,
                        whale_score=whale_score,
                        gaming_focus_score=gaming_focus_score,
                        collection_overlap=collection_overlap
                    )

                    cross_collection_analyses.append(analysis)

            # Sort by whale score (highest first)
            cross_collection_analyses.sort(key=lambda x: x.whale_score, reverse=True)

            self._cache_data(cache_key, cross_collection_analyses)
            return cross_collection_analyses

        except Exception as e:
            logger.error(f"Failed to analyze cross-collection holders: {e}")
            return []

    async def _get_collection_holders(self, collection_address: str, network: str) -> Dict[str, int]:
        """Get holders and their NFT counts for a collection"""
        try:
            endpoint = f"https://{network}.g.alchemy.com/nft/v3/{self.api_key}/getOwnersForCollection"
            params = {
                "contractAddress": collection_address,
                "withTokenBalances": "true"
            }

            response = await self._make_request('GET', endpoint, params=params)
            owners_data = response.get('owners', [])

            holders = {}
            for owner_info in owners_data:
                owner_address = owner_info.get('ownerAddress', '')
                token_balances = owner_info.get('tokenBalances', [])
                total_balance = sum(int(balance.get('balance', 0)) for balance in token_balances)

                if total_balance > 0:
                    holders[owner_address] = total_balance

            return holders

        except Exception as e:
            logger.error(f"Failed to get holders for {collection_address}: {e}")
            return {}

    async def get_enhanced_gaming_protocol_metrics(
        self,
        protocol_name: str,
        networks: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Get comprehensive gaming protocol metrics across multiple chains"""
        if not networks:
            networks = ["eth-mainnet", "polygon-mainnet", "arbitrum-mainnet", "optimism-mainnet", "base-mainnet"]

        cache_key = f"enhanced_protocol_metrics_{protocol_name}_{hash(tuple(sorted(networks)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        try:
            from config.gaming_config import gaming_project_manager

            # Get project configuration
            project = gaming_project_manager.projects.get(protocol_name)
            if not project:
                logger.warning(f"Protocol {protocol_name} not found in gaming projects")
                return {}

            # Collect metrics across all networks
            multi_chain_metrics = {}
            total_activity = {
                'total_transactions_24h': 0,
                'total_unique_users_24h': 0,
                'total_volume_24h_usd': 0.0,
                'total_gas_used_24h': 0,
                'active_networks': []
            }

            for network in networks:
                if network not in self.supported_networks:
                    continue

                network_metrics = await self._get_network_protocol_metrics(project, network)
                if network_metrics:
                    multi_chain_metrics[network] = network_metrics
                    total_activity['active_networks'].append(network)

                    # Aggregate totals
                    total_activity['total_transactions_24h'] += network_metrics.get('transactions_24h', 0)
                    total_activity['total_unique_users_24h'] += network_metrics.get('unique_users_24h', 0)
                    total_activity['total_volume_24h_usd'] += network_metrics.get('volume_24h_usd', 0.0)
                    total_activity['total_gas_used_24h'] += network_metrics.get('gas_used_24h', 0)

            # Calculate cross-chain insights
            cross_chain_insights = self._calculate_cross_chain_insights(multi_chain_metrics)

            result = {
                'protocol_name': protocol_name,
                'multi_chain_metrics': multi_chain_metrics,
                'aggregated_metrics': total_activity,
                'cross_chain_insights': cross_chain_insights,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }

            self._cache_data(cache_key, result)
            return result

        except Exception as e:
            logger.error(f"Failed to get enhanced protocol metrics for {protocol_name}: {e}")
            return {}

    async def _get_network_protocol_metrics(self, project, network: str) -> Dict[str, Any]:
        """Get protocol metrics for a specific network"""
        try:
            metrics = {
                'network': network,
                'transactions_24h': 0,
                'unique_users_24h': 0,
                'volume_24h_usd': 0.0,
                'gas_used_24h': 0,
                'contract_interactions': {},
                'token_transfers': {},
                'nft_activity': {}
            }

            # Analyze token contracts
            for token in project.tokens:
                if token.contract_address:
                    token_activity = await self._get_token_activity(token.contract_address, network)
                    if token_activity:
                        metrics['token_transfers'][token.symbol] = token_activity
                        metrics['transactions_24h'] += token_activity.get('transfer_count_24h', 0)
                        metrics['unique_users_24h'] += token_activity.get('unique_users_24h', 0)
                        metrics['volume_24h_usd'] += token_activity.get('volume_24h_usd', 0.0)

            # Analyze NFT contracts
            for nft in project.nfts:
                if nft.contract_address:
                    nft_activity = await self._get_nft_activity(nft.contract_address, network)
                    if nft_activity:
                        metrics['nft_activity'][nft.name or nft.contract_address] = nft_activity
                        metrics['transactions_24h'] += nft_activity.get('transfer_count_24h', 0)
                        metrics['unique_users_24h'] += nft_activity.get('unique_users_24h', 0)

            return metrics

        except Exception as e:
            logger.error(f"Failed to get network metrics for {network}: {e}")
            return {}

    async def _get_token_activity(self, token_address: str, network: str) -> Dict[str, Any]:
        """Get token activity metrics using Alchemy Transfer API"""
        try:
            # Get recent transfers for the token
            endpoint = f"https://{network}.g.alchemy.com/v2/{self.api_key}"

            # Get transfer events for the last 24 hours
            current_block = await self._get_current_block_number(network)
            blocks_24h = 7200  # Approximate blocks in 24 hours (varies by network)
            from_block = max(0, current_block - blocks_24h)

            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "alchemy_getAssetTransfers",
                "params": [{
                    "fromBlock": hex(from_block),
                    "toBlock": "latest",
                    "contractAddresses": [token_address],
                    "category": ["erc20"],
                    "withMetadata": True,
                    "excludeZeroValue": True,
                    "maxCount": "0x3e8"  # 1000 transfers max
                }]
            }

            response = await self._make_request('POST', endpoint, data=payload)
            transfers = response.get('result', {}).get('transfers', [])

            # Analyze transfers
            unique_users = set()
            total_volume = 0.0

            for transfer in transfers:
                unique_users.add(transfer.get('from', ''))
                unique_users.add(transfer.get('to', ''))

                # Convert value to float (simplified)
                value = transfer.get('value', 0)
                if isinstance(value, str) and value.startswith('0x'):
                    value = int(value, 16) / 1e18  # Assume 18 decimals
                total_volume += float(value) if value else 0

            return {
                'transfer_count_24h': len(transfers),
                'unique_users_24h': len(unique_users),
                'volume_24h': total_volume,
                'volume_24h_usd': total_volume * 1.0,  # Would need price data
                'network': network
            }

        except Exception as e:
            logger.error(f"Failed to get token activity for {token_address}: {e}")
            return {}

    async def _get_nft_activity(self, nft_address: str, network: str) -> Dict[str, Any]:
        """Get NFT activity metrics"""
        try:
            # Similar to token activity but for NFT transfers
            endpoint = f"https://{network}.g.alchemy.com/v2/{self.api_key}"

            current_block = await self._get_current_block_number(network)
            blocks_24h = 7200
            from_block = max(0, current_block - blocks_24h)

            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "alchemy_getAssetTransfers",
                "params": [{
                    "fromBlock": hex(from_block),
                    "toBlock": "latest",
                    "contractAddresses": [nft_address],
                    "category": ["erc721", "erc1155"],
                    "withMetadata": True,
                    "maxCount": "0x3e8"
                }]
            }

            response = await self._make_request('POST', endpoint, data=payload)
            transfers = response.get('result', {}).get('transfers', [])

            unique_users = set()
            for transfer in transfers:
                unique_users.add(transfer.get('from', ''))
                unique_users.add(transfer.get('to', ''))

            return {
                'transfer_count_24h': len(transfers),
                'unique_users_24h': len(unique_users),
                'network': network
            }

        except Exception as e:
            logger.error(f"Failed to get NFT activity for {nft_address}: {e}")
            return {}

    async def _get_current_block_number(self, network: str) -> int:
        """Get current block number for a network"""
        try:
            endpoint = f"https://{network}.g.alchemy.com/v2/{self.api_key}"
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "eth_blockNumber",
                "params": []
            }

            response = await self._make_request('POST', endpoint, data=payload)
            block_hex = response.get('result', '0x0')
            return int(block_hex, 16)

        except Exception as e:
            logger.error(f"Failed to get current block number for {network}: {e}")
            return 0

    def _calculate_cross_chain_insights(self, multi_chain_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate insights across multiple blockchain networks"""
        if not multi_chain_metrics:
            return {}

        insights = {
            'network_distribution': {},
            'dominant_network': '',
            'cross_chain_user_overlap': 0.0,
            'network_efficiency': {},
            'growth_trends': {}
        }

        # Calculate network distribution
        total_transactions = sum(
            metrics.get('transactions_24h', 0)
            for metrics in multi_chain_metrics.values()
        )

        if total_transactions > 0:
            for network, metrics in multi_chain_metrics.items():
                network_txs = metrics.get('transactions_24h', 0)
                insights['network_distribution'][network] = (network_txs / total_transactions) * 100

        # Find dominant network
        if insights['network_distribution']:
            insights['dominant_network'] = max(
                insights['network_distribution'].items(),
                key=lambda x: x[1]
            )[0]

        # Calculate network efficiency (transactions per gas used)
        for network, metrics in multi_chain_metrics.items():
            gas_used = metrics.get('gas_used_24h', 1)
            transactions = metrics.get('transactions_24h', 0)
            insights['network_efficiency'][network] = transactions / gas_used if gas_used > 0 else 0

        # Placeholder for cross-chain user overlap analysis
        insights['cross_chain_user_overlap'] = 15.0  # Would need detailed user analysis

        return insights
    
    async def get_transaction_history(
        self, 
        addresses: List[str], 
        networks: Optional[List[str]] = None,
        limit: int = 25
    ) -> Dict[str, Any]:
        """Get transaction history for gaming wallets"""
        if not addresses:
            return {}
        
        if not networks:
            networks = ["eth-mainnet"]  # Currently limited to Ethereum and Base
        
        cache_key = f"tx_history_{hash(tuple(sorted(addresses)))}_{hash(tuple(sorted(networks)))}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data
        
        # Alchemy currently limits to 1 address for transaction history
        address = addresses[0]
        network = networks[0] if networks[0] in ["eth-mainnet", "base-mainnet"] else "eth-mainnet"
        
        payload = {
            "addresses": [{
                "address": address,
                "networks": [network]
            }],
            "limit": min(limit, 50)  # Alchemy max: 50
        }
        
        try:
            endpoint = f"{self.api_key}/transactions/history/by-address"
            response = await self._make_request('POST', endpoint, data=payload)
            
            result = {
                'address': address,
                'network': network,
                'transactions': response.get('transactions', []),
                'total_count': response.get('totalCount', 0),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            self._cache_data(cache_key, result)
            return result
            
        except Exception as e:
            logger.error(f"Failed to get transaction history: {e}")
            return {}
    
    async def get_nft_collection_data(self, collection_address: str) -> Dict[str, Any]:
        """
        Get NFT collection data using Alchemy NFT API

        Args:
            collection_address: Contract address of the NFT collection

        Returns:
            Dictionary containing collection metadata, floor price, and analytics
        """
        if not collection_address:
            return {}

        cache_key = f"nft_collection_{collection_address}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        async with self:  # Use async context manager
            try:
                # Get collection metadata and floor price from gaming project manager
                from config.gaming_config import gaming_project_manager

                # Find the project and NFT info for this collection
                collection_info = None
                project_info = None

                for project_name, project in gaming_project_manager.projects.items():
                    for nft in project.nfts:
                        if nft.contract_address.lower() == collection_address.lower():
                            collection_info = nft
                            project_info = project
                            break
                    if collection_info:
                        break

                # Determine network for this collection
                network = None
                if project_info:
                    network_config = self.get_network_config(project_info.blockchain.lower())
                    if network_config:
                        network = network_config.alchemy_id

                if not network:
                    # Default to Ethereum if network not found
                    network = "eth-mainnet"
                    logger.warning(f"Network not found for collection {collection_address}, defaulting to Ethereum")

                # Use Alchemy NFT API to get collection metadata
                nft_url = self._get_nft_api_url(network, "getContractMetadata")
                params = {
                    'contractAddress': collection_address
                }

                # Make direct request to NFT API
                if not self.session:
                    raise RuntimeError("Client session not initialized. Use async context manager.")

                async with self.session.get(nft_url, params=params, headers=self._get_auth_headers()) as resp:
                    if resp.status == 200:
                        response = await resp.json()
                    else:
                        logger.error(f"NFT API request failed: {resp.status}")
                        response = {}

                # Extract collection data (NFT API returns data directly)
                contract_metadata = response

                # Get floor price using NFT API
                floor_price_data = await self._get_nft_floor_price(collection_address, network)

                # Get collection stats
                stats_data = await self._get_nft_collection_stats(collection_address, network)

                # Build comprehensive collection data
                result = {
                    'collection_address': collection_address,
                    'network': network,
                    'name': contract_metadata.get('name', collection_info.name if collection_info else 'Unknown'),
                    'symbol': contract_metadata.get('symbol', ''),
                    'description': collection_info.function if collection_info else '',
                    'total_supply': contract_metadata.get('totalSupply'),
                    'contract_type': contract_metadata.get('tokenType', 'ERC721'),
                    'verified': True,  # Assume verified if we have data

                    # Gaming-specific data
                    'gaming_project': project_info.project_name if project_info else None,
                    'gaming_category': 'Gaming NFT',  # Default category for gaming NFTs
                    'nft_function': collection_info.function if collection_info else None,
                    'marketplace_links': [collection_info.marketplace_link] if collection_info and collection_info.marketplace_link else [],

                    # Market data
                    'floor_price': floor_price_data.get('floor_price'),
                    'floor_price_currency': floor_price_data.get('currency', 'ETH'),
                    'volume_24h': stats_data.get('volume_24h'),
                    'holders_count': stats_data.get('holders_count'),
                    'sales_count_24h': stats_data.get('sales_count_24h'),

                    # Metadata
                    'image_url': contract_metadata.get('openSeaMetadata', {}).get('imageUrl'),
                    'external_url': contract_metadata.get('openSeaMetadata', {}).get('externalUrl'),
                    'discord_url': contract_metadata.get('openSeaMetadata', {}).get('discordUrl'),
                    'twitter_username': contract_metadata.get('openSeaMetadata', {}).get('twitterUsername'),

                    # Analytics
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'data_source': 'alchemy',
                    'api_endpoints_used': ['getContractMetadata', 'getFloorPrice', 'getCollectionStats']
                }

                logger.info(f"✅ Retrieved NFT collection data for {collection_address} via Alchemy")
                self._cache_data(cache_key, result)
                return result

            except Exception as e:
                logger.error(f"Error fetching NFT collection data for {collection_address}: {e}")

                # Return basic data structure on error
                result = {
                    'collection_address': collection_address,
                    'error': str(e),
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'data_source': 'alchemy'
                }

                self._cache_data(cache_key, result)
                return result

    async def _get_nft_floor_price(self, collection_address: str, network: str) -> Dict[str, Any]:
        """Get NFT collection floor price using Alchemy NFT API"""
        try:
            nft_url = self._get_nft_api_url(network, "getFloorPrice")
            params = {
                'contractAddress': collection_address
            }

            # Make direct request to NFT API
            if not self.session:
                raise RuntimeError("Client session not initialized. Use async context manager.")

            async with self.session.get(nft_url, params=params, headers=self._get_auth_headers()) as resp:
                if resp.status == 200:
                    response = await resp.json()
                else:
                    logger.error(f"NFT Floor Price API request failed: {resp.status}")
                    response = {}

            floor_price_data = response.get('openSea', {})
            return {
                'floor_price': floor_price_data.get('floorPrice'),
                'currency': floor_price_data.get('priceCurrency', 'ETH'),
                'marketplace': 'OpenSea'
            }

        except Exception as e:
            logger.warning(f"Could not fetch floor price for {collection_address}: {e}")
            return {}

    async def _get_nft_collection_stats(self, collection_address: str, network: str) -> Dict[str, Any]:
        """Get NFT collection statistics using Alchemy NFT API"""
        try:
            # Get owners count
            nft_url = self._get_nft_api_url(network, "getOwnersForContract")
            params = {
                'contractAddress': collection_address,
                'withTokenBalances': 'false'
            }

            # Make direct request to NFT API
            if not self.session:
                raise RuntimeError("Client session not initialized. Use async context manager.")

            async with self.session.get(nft_url, params=params, headers=self._get_auth_headers()) as resp:
                if resp.status == 200:
                    response = await resp.json()
                else:
                    logger.error(f"NFT Owners API request failed: {resp.status}")
                    response = {"owners": []}
            owners_count = len(response.get('owners', []))

            # Additional stats would require more API calls
            # For now, return basic holder count
            return {
                'holders_count': owners_count,
                'volume_24h': None,  # Would need marketplace API
                'sales_count_24h': None  # Would need marketplace API
            }

        except Exception as e:
            logger.warning(f"Could not fetch collection stats for {collection_address}: {e}")
            return {}

    async def get_gaming_protocol_metrics(self, protocol_name: str) -> Dict[str, Any]:
        """
        Get gaming protocol metrics using Alchemy Transfer API

        Args:
            protocol_name: Name of the gaming protocol (e.g., 'axie-infinity')

        Returns:
            Dictionary containing protocol transaction metrics and analytics
        """
        cache_key = f"protocol_metrics_{protocol_name}"
        cached_data = self._get_cached_data(cache_key)
        if cached_data:
            return cached_data

        async with self:  # Use async context manager
            try:
                # Get protocol information from gaming project manager
                from config.gaming_config import gaming_project_manager

                # Find the project configuration
                project = gaming_project_manager.projects.get(protocol_name)
                if not project:
                    logger.warning(f"Protocol {protocol_name} not found in gaming projects")
                    result = {
                        'protocol_name': protocol_name,
                        'error': 'Protocol not found in gaming projects database',
                        'last_updated': datetime.now(timezone.utc).isoformat()
                    }
                    self._cache_data(cache_key, result)
                    return result

                # Determine network for this protocol
                network_config = self.get_network_config(project.blockchain.lower())
                if not network_config:
                    logger.warning(f"Network {project.blockchain} not supported for protocol {protocol_name}")
                    result = {
                        'protocol_name': protocol_name,
                        'error': f'Network {project.blockchain} not supported',
                        'last_updated': datetime.now(timezone.utc).isoformat()
                    }
                    self._cache_data(cache_key, result)
                    return result

                network = network_config.alchemy_id

                # Collect all contract addresses for this protocol
                contract_addresses = []

                # Add token contracts
                for token in project.tokens:
                    if token.contract_address:
                        contract_addresses.append({
                            'address': token.contract_address,
                            'type': 'token',
                            'symbol': token.symbol,
                            'name': token.symbol
                        })

                # Add NFT contracts
                for nft in project.nfts:
                    if nft.contract_address:
                        contract_addresses.append({
                            'address': nft.contract_address,
                            'type': 'nft',
                            'symbol': nft.name,
                            'name': nft.name
                        })

                if not contract_addresses:
                    logger.warning(f"No contract addresses found for protocol {protocol_name}")
                    result = {
                        'protocol_name': protocol_name,
                        'error': 'No contract addresses found',
                        'last_updated': datetime.now(timezone.utc).isoformat()
                    }
                    self._cache_data(cache_key, result)
                    return result

                # Get transfer activity for protocol contracts
                protocol_metrics = {
                    'protocol_name': protocol_name,
                    'project_name': project.project_name,
                    'blockchain': project.blockchain,
                    'network': network,
                    'status': project.status,
                    'daily_active_users': project.daily_active_users,
                    'daily_unique_wallets': project.daily_unique_wallets,
                    'contracts': contract_addresses,
                    'transfer_metrics': {},
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'data_source': 'alchemy'
                }

                # Get transfer data for each contract (limited to avoid rate limits)
                for contract in contract_addresses[:3]:  # Limit to first 3 contracts
                    try:
                        transfer_data = await self._get_contract_transfer_metrics(
                            contract['address'],
                            network,
                            contract['type']
                        )
                        protocol_metrics['transfer_metrics'][contract['address']] = transfer_data

                    except Exception as e:
                        logger.warning(f"Could not get transfer metrics for {contract['address']}: {e}")
                        continue

                logger.info(f"✅ Retrieved protocol metrics for {protocol_name} via Alchemy")
                self._cache_data(cache_key, protocol_metrics)
                return protocol_metrics

            except Exception as e:
                logger.error(f"Error fetching gaming protocol metrics for {protocol_name}: {e}")

                result = {
                    'protocol_name': protocol_name,
                    'error': str(e),
                    'last_updated': datetime.now(timezone.utc).isoformat(),
                    'data_source': 'alchemy'
                }

                self._cache_data(cache_key, result)
                return result

    async def _get_contract_transfer_metrics(self, contract_address: str, network: str, contract_type: str) -> Dict[str, Any]:
        """Get transfer metrics for a specific contract using Alchemy Transfer API"""
        try:
            # Use Alchemy Node API to get recent transfer activity
            node_url = self._get_node_api_url(network)

            # Get recent transfers for this contract (correct JSON-RPC format)
            payload = {
                "jsonrpc": "2.0",
                "method": "alchemy_getAssetTransfers",
                "params": [{
                    "fromBlock": "0x0",
                    "toBlock": "latest",
                    "contractAddresses": [contract_address],
                    "category": ["erc20", "erc721", "erc1155"] if contract_type == "token" else ["erc721", "erc1155"],
                    "maxCount": "0x64",
                    "order": "desc"
                }],
                "id": 1
            }

            # Make direct request to Node API
            if not self.session:
                raise RuntimeError("Client session not initialized. Use async context manager.")

            async with self.session.post(node_url, json=payload, headers=self._get_auth_headers()) as resp:
                if resp.status == 200:
                    response = await resp.json()
                else:
                    logger.error(f"Node API request failed: {resp.status}")
                    response = {"result": {"transfers": []}}

            transfers = response.get('result', {}).get('transfers', [])

            # Analyze transfer data
            unique_addresses = set()
            total_transfers = len(transfers)

            for transfer in transfers:
                if transfer.get('from'):
                    unique_addresses.add(transfer['from'])
                if transfer.get('to'):
                    unique_addresses.add(transfer['to'])

            return {
                'contract_address': contract_address,
                'contract_type': contract_type,
                'total_transfers_recent': total_transfers,
                'unique_addresses_recent': len(unique_addresses),
                'sample_transfers': transfers[:5],  # First 5 transfers as sample
                'last_updated': datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.warning(f"Could not fetch transfer metrics for {contract_address}: {e}")
            return {
                'contract_address': contract_address,
                'contract_type': contract_type,
                'error': str(e),
                'last_updated': datetime.now(timezone.utc).isoformat()
            }

    def get_supported_networks(self) -> List[str]:
        """Get list of supported networks for gaming analytics"""
        return list(self.NETWORK_CONFIGS.keys())
    
    def get_network_config(self, network: str) -> Optional[AlchemyNetworkConfig]:
        """Get network configuration with blockchain mapping support"""
        # Handle blockchain name variations and mappings
        blockchain_mappings = {
            'ethereum, ronin': 'ethereum',  # For multi-chain projects, default to Ethereum
            'avalanche, ethereum': 'ethereum',  # For multi-chain projects, default to Ethereum
            'ronin': 'ethereum',  # Ronin is not supported by Alchemy, use Ethereum
            'bsc': 'bsc',
            'polygon': 'polygon',
            'arbitrum': 'arbitrum',
            'optimism': 'optimism',
            'base': 'base',
            'solana': None,  # Solana not supported by Alchemy yet
            'avalanche': None,  # Avalanche not supported by Alchemy yet
            'ton': None  # TON not supported by Alchemy yet
        }

        network_key = network.lower().strip()
        mapped_network = blockchain_mappings.get(network_key, network_key)

        if mapped_network is None:
            return None

        return self.NETWORK_CONFIGS.get(mapped_network)
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check for Alchemy client"""
        try:
            await self._test_endpoint()
            return {
                'status': 'healthy',
                'api_key_configured': bool(self.api_key),
                'supported_networks': len(self.supported_networks),
                'cache_entries': len(self._cache),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }


# Global instance for easy access
alchemy_client = AlchemyClient()
