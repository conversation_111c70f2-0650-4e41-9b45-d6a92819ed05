"""
Blockchain Integration Framework
Comprehensive blockchain integration for gaming data collection
"""

# Core blockchain clients
from .multi_chain_client import (
    multi_chain_manager,
    EVMChainClient,
    SolanaChainClient,
    TONChainClient,
    BaseChainClient
)

# Enhanced RPC management
from .enhanced_rpc import (
    enhanced_rpc_manager,
    EnhancedRPCManager,
    RPCEndpoint
)

# Gaming contract management
from .gaming_contracts import (
    gaming_contract_manager,
    GamingContract,
    ContractType,
    GamingContractManager
)

# Event monitoring
from .event_monitor import (
    gaming_event_monitor,
    GamingEventMonitor,
    EventType,
    GameEvent
)

# NFT tracking
from .nft_tracker import (
    nft_tracker,
    NFTTracker,
    GameAsset,
    NFTMetadata,
    NFTActivityType
)

# Market data integration
from .market_data import (
    gaming_market_data,
    GamingMarketDataManager,
    TokenPrice,
    MarketData
)

# Data synchronization
from .sync_manager import (
    blockchain_sync_manager,
    BlockchainSyncManager,
    ChainSyncState,
    SyncStatus
)

# Error handling
from .error_handling import (
    error_handler,
    BlockchainErrorHandler,
    with_error_handling,
    get_blockchain_health_status
)

# Note: Legacy rpc and data_clients modules have been replaced by multi_chain_client
__all__ = [
    # Core clients
    'multi_chain_manager',
    'EVMChainClient',
    'SolanaChainClient',
    'TONChainClient',
    'BaseChainClient',

    # Enhanced RPC
    'enhanced_rpc_manager',
    'EnhancedRPCManager',
    'RPCEndpoint',

    # Gaming contracts
    'gaming_contract_manager',
    'GamingContract',
    'ContractType',
    'GamingContractManager',

    # Event monitoring
    'gaming_event_monitor',
    'GamingEventMonitor',
    'EventType',
    'GameEvent',

    # NFT tracking
    'nft_tracker',
    'NFTTracker',
    'GameAsset',
    'NFTMetadata',
    'NFTActivityType',

    # Market data
    'gaming_market_data',
    'GamingMarketDataManager',
    'TokenPrice',
    'MarketData',

    # Synchronization
    'blockchain_sync_manager',
    'BlockchainSyncManager',
    'ChainSyncState',
    'SyncStatus',

    # Error handling
    'error_handler',
    'BlockchainErrorHandler',
    'with_error_handling',
    'get_blockchain_health_status',


]
