"""
RPC connection management for multiple blockchains
"""
import asyncio
from typing import Dict, Optional, Any
from web3 import Web3
from web3.middleware import geth_poa_middleware
import aiohttp
import json
from config.settings import get_settings

settings = get_settings()


class RPCManager:
    """Manages RPC connections to multiple blockchains"""
    
    def __init__(self):
        self.connections: Dict[str, Web3] = {}
        self.rpc_urls = {
            'ethereum': settings.blockchain.ethereum_rpc_url,
            'polygon': settings.blockchain.polygon_rpc_url,
            'bsc': settings.blockchain.bsc_rpc_url,
            'arbitrum': settings.blockchain.arbitrum_rpc_url,
            'optimism': settings.blockchain.optimism_rpc_url,
            'immutable': settings.blockchain.immutable_rpc_url,
            'ronin': settings.blockchain.ronin_rpc_url,
        }
        self._initialize_connections()
    
    def _initialize_connections(self):
        """Initialize Web3 connections for each blockchain"""
        for chain, rpc_url in self.rpc_urls.items():
            if rpc_url:
                try:
                    w3 = Web3(Web3.HTTPProvider(
                        rpc_url,
                        request_kwargs={'timeout': settings.blockchain.request_timeout}
                    ))
                    
                    # Add PoA middleware for chains that need it
                    if chain in ['bsc', 'polygon']:
                        w3.middleware_onion.inject(geth_poa_middleware, layer=0)
                    
                    self.connections[chain] = w3
                    print(f"✅ Initialized {chain} RPC connection")
                except Exception as e:
                    print(f"❌ Failed to initialize {chain} RPC: {e}")
    
    def get_connection(self, chain: str) -> Optional[Web3]:
        """Get Web3 connection for a specific chain"""
        return self.connections.get(chain)
    
    def is_connected(self, chain: str) -> bool:
        """Check if connection to chain is active"""
        w3 = self.get_connection(chain)
        if not w3:
            return False
        
        try:
            return w3.isConnected()
        except Exception:
            return False
    
    async def get_latest_block(self, chain: str) -> Optional[int]:
        """Get latest block number for a chain"""
        w3 = self.get_connection(chain)
        if not w3:
            return None
        
        try:
            return w3.eth.block_number
        except Exception as e:
            print(f"Error getting latest block for {chain}: {e}")
            return None
    
    async def get_block(self, chain: str, block_number: int) -> Optional[Dict]:
        """Get block data for a specific block"""
        w3 = self.get_connection(chain)
        if not w3:
            return None
        
        try:
            block = w3.eth.get_block(block_number, full_transactions=True)
            return dict(block)
        except Exception as e:
            print(f"Error getting block {block_number} for {chain}: {e}")
            return None
    
    async def get_transaction(self, chain: str, tx_hash: str) -> Optional[Dict]:
        """Get transaction data"""
        w3 = self.get_connection(chain)
        if not w3:
            return None
        
        try:
            tx = w3.eth.get_transaction(tx_hash)
            return dict(tx)
        except Exception as e:
            print(f"Error getting transaction {tx_hash} for {chain}: {e}")
            return None
    
    async def get_transaction_receipt(self, chain: str, tx_hash: str) -> Optional[Dict]:
        """Get transaction receipt"""
        w3 = self.get_connection(chain)
        if not w3:
            return None
        
        try:
            receipt = w3.eth.get_transaction_receipt(tx_hash)
            return dict(receipt)
        except Exception as e:
            print(f"Error getting transaction receipt {tx_hash} for {chain}: {e}")
            return None
    
    async def get_contract_events(
        self, 
        chain: str, 
        contract_address: str, 
        abi: list,
        event_name: str,
        from_block: int,
        to_block: int = 'latest'
    ) -> list:
        """Get contract events"""
        w3 = self.get_connection(chain)
        if not w3:
            return []
        
        try:
            contract = w3.eth.contract(
                address=Web3.toChecksumAddress(contract_address),
                abi=abi
            )
            
            event_filter = getattr(contract.events, event_name).createFilter(
                fromBlock=from_block,
                toBlock=to_block
            )
            
            events = event_filter.get_all_entries()
            return [dict(event) for event in events]
        except Exception as e:
            print(f"Error getting events for {contract_address} on {chain}: {e}")
            return []
    
    async def call_contract_function(
        self,
        chain: str,
        contract_address: str,
        abi: list,
        function_name: str,
        *args
    ) -> Any:
        """Call a contract function"""
        w3 = self.get_connection(chain)
        if not w3:
            return None
        
        try:
            contract = w3.eth.contract(
                address=Web3.toChecksumAddress(contract_address),
                abi=abi
            )
            
            function = getattr(contract.functions, function_name)
            result = function(*args).call()
            return result
        except Exception as e:
            print(f"Error calling {function_name} on {contract_address} ({chain}): {e}")
            return None


# Global RPC manager instance
rpc_manager = RPCManager()


async def test_all_connections() -> Dict[str, bool]:
    """Test all RPC connections"""
    results = {}
    
    for chain in rpc_manager.rpc_urls.keys():
        if rpc_manager.rpc_urls[chain]:
            results[chain] = rpc_manager.is_connected(chain)
        else:
            results[chain] = False
    
    return results


async def get_gaming_contract_data(chain: str, contract_address: str) -> Optional[Dict]:
    """Get basic data for a gaming contract"""
    # This is a placeholder - would need specific ABIs for each contract
    w3 = rpc_manager.get_connection(chain)
    if not w3:
        return None
    
    try:
        # Get basic contract info
        code = w3.eth.get_code(Web3.toChecksumAddress(contract_address))
        if code == b'':
            return None
        
        return {
            'address': contract_address,
            'chain': chain,
            'has_code': len(code) > 0,
            'code_size': len(code)
        }
    except Exception as e:
        print(f"Error getting contract data for {contract_address} on {chain}: {e}")
        return None


# Common gaming contract ABIs (simplified)
GAMING_CONTRACT_ABIS = {
    'erc721': [
        {
            "constant": True,
            "inputs": [],
            "name": "name",
            "outputs": [{"name": "", "type": "string"}],
            "type": "function"
        },
        {
            "constant": True,
            "inputs": [],
            "name": "symbol",
            "outputs": [{"name": "", "type": "string"}],
            "type": "function"
        },
        {
            "constant": True,
            "inputs": [],
            "name": "totalSupply",
            "outputs": [{"name": "", "type": "uint256"}],
            "type": "function"
        }
    ],
    'erc20': [
        {
            "constant": True,
            "inputs": [],
            "name": "name",
            "outputs": [{"name": "", "type": "string"}],
            "type": "function"
        },
        {
            "constant": True,
            "inputs": [],
            "name": "symbol",
            "outputs": [{"name": "", "type": "string"}],
            "type": "function"
        },
        {
            "constant": True,
            "inputs": [],
            "name": "totalSupply",
            "outputs": [{"name": "", "type": "uint256"}],
            "type": "function"
        }
    ]
}
