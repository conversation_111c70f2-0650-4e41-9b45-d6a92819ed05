#!/usr/bin/env python3
"""
Script to fix broken TextField components in AddGameForm.jsx
"""

import re

def fix_broken_textfields():
    file_path = "dashboard/frontend/src/components/AddGameForm.jsx"
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix the broken pattern where on<PERSON><PERSON><PERSON> got split
    # Pattern: onChange={(e) =\n                  sx={textFieldSx}> handleChange(
    pattern1 = r'onChange=\{\(e\) =\s*\n\s*sx=\{textFieldSx\}>\s*handleChange\('
    replacement1 = r'onChange={(e) => handleChange('
    
    content = re.sub(pattern1, replacement1, content)
    
    # Fix any remaining broken patterns
    # Pattern: onChange={(e) =\n                  sx={textFieldSx}> 
    pattern2 = r'onChange=\{\(e\) =\s*\n\s*sx=\{textFieldSx\}>\s*'
    replacement2 = r'onChange={(e) => '
    
    content = re.sub(pattern2, replacement2, content)
    
    # Now add sx={textFieldSx} to TextFields that don't have it
    # Find TextField components and add sx prop if missing
    def add_sx_to_textfield(match):
        textfield_content = match.group(0)
        
        # If already has sx prop, return unchanged
        if 'sx={textFieldSx}' in textfield_content:
            return textfield_content
        
        # Find the closing /> or >
        if textfield_content.endswith('/>'):
            # Replace /> with sx={textFieldSx} />
            return textfield_content[:-2] + '\n                  sx={textFieldSx}\n                />'
        elif textfield_content.endswith('>'):
            # Replace > with sx={textFieldSx} >
            return textfield_content[:-1] + '\n                  sx={textFieldSx}\n                >'
        
        return textfield_content
    
    # Pattern to match complete TextField components
    textfield_pattern = r'<TextField[^>]*(?:/>|>)'
    content = re.sub(textfield_pattern, add_sx_to_textfield, content, flags=re.DOTALL)
    
    # Write back to file
    with open(file_path, 'w') as f:
        f.write(content)
    
    print("✅ Fixed broken TextField components and added sx styling")

if __name__ == "__main__":
    fix_broken_textfields()
