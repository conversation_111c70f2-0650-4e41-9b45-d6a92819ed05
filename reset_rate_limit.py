#!/usr/bin/env python3
"""
Reset Rate Limiting Script
Clears blocked IPs and resets rate limiting counters
"""

import redis
import sys
import os
from collections import defaultdict

# Add the project root to the path
sys.path.append(os.path.dirname(__file__))

from config.settings import get_settings

def reset_rate_limiting():
    """Reset rate limiting by clearing blocked IPs and Redis counters"""
    print("🔄 Resetting rate limiting...")
    
    settings = get_settings()
    
    # Clear Redis rate limiting data
    try:
        redis_client = redis.from_url(settings.redis.url)
        
        # Get all rate limiting keys
        rate_limit_keys = redis_client.keys("rate_limit:*")
        
        if rate_limit_keys:
            # Delete all rate limiting keys
            redis_client.delete(*rate_limit_keys)
            print(f"   ✅ Cleared {len(rate_limit_keys)} Redis rate limit entries")
        else:
            print("   ℹ️  No Redis rate limit entries found")
            
    except Exception as e:
        print(f"   ⚠️  Redis reset failed: {e}")
    
    # Clear in-memory rate limiting (this requires restarting the server)
    print("   ℹ️  In-memory rate limiting will be cleared on server restart")
    
    print("\n🎯 Rate limiting reset complete!")
    print("   📝 Note: You may need to restart the API server to fully clear in-memory blocks")
    print("   🔄 Restart command: python -m uvicorn api.main:app --host 0.0.0.0 --port 8001 --reload")

if __name__ == "__main__":
    reset_rate_limiting()
