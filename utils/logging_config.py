"""
Standardized Logging Configuration for Web3 Gaming News Tracker
"""
import logging
import logging.handlers
import json
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from pythonjsonlogger import jsonlogger


class CustomJSONFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter with additional fields"""
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]):
        super().add_fields(log_record, record, message_dict)
        
        # Add timestamp
        if not log_record.get('timestamp'):
            log_record['timestamp'] = datetime.utcnow().isoformat()
        
        # Add service name
        log_record['service'] = 'web3-gaming-tracker'
        
        # Add log level
        if log_record.get('level'):
            log_record['level'] = log_record['level'].upper()
        else:
            log_record['level'] = record.levelname
        
        # Add module information
        log_record['module'] = record.module
        log_record['function'] = record.funcName
        log_record['line'] = record.lineno
        
        # Add process and thread info for debugging
        log_record['process_id'] = record.process
        log_record['thread_id'] = record.thread


class StructuredLogger:
    """Structured logging utility"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def log_with_context(
        self,
        level: int,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log message with structured context"""
        extra = context or {}
        extra.update(kwargs)
        self.logger.log(level, message, extra=extra)
    
    def info(self, message: str, **context):
        """Log info message with context"""
        self.log_with_context(logging.INFO, message, context)
    
    def warning(self, message: str, **context):
        """Log warning message with context"""
        self.log_with_context(logging.WARNING, message, context)
    
    def error(self, message: str, **context):
        """Log error message with context"""
        self.log_with_context(logging.ERROR, message, context)
    
    def critical(self, message: str, **context):
        """Log critical message with context"""
        self.log_with_context(logging.CRITICAL, message, context)
    
    def debug(self, message: str, **context):
        """Log debug message with context"""
        self.log_with_context(logging.DEBUG, message, context)


def setup_logging(
    log_level: str = "INFO",
    log_format: str = "json",
    log_file: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    enable_console: bool = True
) -> logging.Logger:
    """
    Setup standardized logging configuration
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Format type ('json' or 'text')
        log_file: Path to log file (optional)
        max_file_size: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
        enable_console: Whether to enable console logging
    
    Returns:
        Configured logger instance
    """
    
    # Convert log level string to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Setup formatters
    if log_format.lower() == "json":
        formatter = CustomJSONFormatter(
            '%(timestamp)s %(level)s %(name)s %(message)s'
        )
    else:
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    # Console handler
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Error file handler (separate file for errors)
    if log_file:
        error_log_file = log_path.parent / f"{log_path.stem}_errors{log_path.suffix}"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
    
    return root_logger


def setup_service_loggers() -> Dict[str, StructuredLogger]:
    """Setup loggers for different services"""
    
    services = [
        'api',
        'blockchain',
        'scrapers',
        'content_intelligence',
        'market_analytics',
        'competitive_analysis',
        'database',
        'notifications',
        'websocket',
        'scheduler'
    ]
    
    loggers = {}
    for service in services:
        logger_name = f"web3_gaming.{service}"
        loggers[service] = StructuredLogger(logger_name)
    
    return loggers


def log_api_request(
    method: str,
    endpoint: str,
    status_code: int,
    response_time: float,
    user_id: Optional[str] = None,
    request_id: Optional[str] = None,
    error: Optional[str] = None
):
    """Log API request with structured data"""
    logger = StructuredLogger('web3_gaming.api')
    
    context = {
        'request_method': method,
        'endpoint': endpoint,
        'status_code': status_code,
        'response_time_ms': round(response_time * 1000, 2),
        'user_id': user_id,
        'request_id': request_id
    }
    
    if error:
        context['error'] = error
        logger.error(f"API request failed: {method} {endpoint}", **context)
    else:
        logger.info(f"API request: {method} {endpoint}", **context)


def log_blockchain_operation(
    operation: str,
    blockchain: str,
    success: bool,
    duration: float,
    contract_address: Optional[str] = None,
    transaction_hash: Optional[str] = None,
    error: Optional[str] = None
):
    """Log blockchain operation with structured data"""
    logger = StructuredLogger('web3_gaming.blockchain')
    
    context = {
        'operation': operation,
        'blockchain': blockchain,
        'success': success,
        'duration_ms': round(duration * 1000, 2),
        'contract_address': contract_address,
        'transaction_hash': transaction_hash
    }
    
    if error:
        context['error'] = error
        logger.error(f"Blockchain operation failed: {operation} on {blockchain}", **context)
    else:
        logger.info(f"Blockchain operation: {operation} on {blockchain}", **context)


def log_scraper_activity(
    scraper_name: str,
    source: str,
    articles_found: int,
    success: bool,
    duration: float,
    error: Optional[str] = None
):
    """Log scraper activity with structured data"""
    logger = StructuredLogger('web3_gaming.scrapers')
    
    context = {
        'scraper_name': scraper_name,
        'source': source,
        'articles_found': articles_found,
        'success': success,
        'duration_ms': round(duration * 1000, 2)
    }
    
    if error:
        context['error'] = error
        logger.error(f"Scraper failed: {scraper_name} from {source}", **context)
    else:
        logger.info(f"Scraper completed: {scraper_name} from {source}", **context)


def log_content_analysis(
    content_type: str,
    analysis_type: str,
    content_count: int,
    success: bool,
    duration: float,
    confidence_score: Optional[float] = None,
    error: Optional[str] = None
):
    """Log content analysis with structured data"""
    logger = StructuredLogger('web3_gaming.content_intelligence')
    
    context = {
        'content_type': content_type,
        'analysis_type': analysis_type,
        'content_count': content_count,
        'success': success,
        'duration_ms': round(duration * 1000, 2),
        'confidence_score': confidence_score
    }
    
    if error:
        context['error'] = error
        logger.error(f"Content analysis failed: {analysis_type} for {content_type}", **context)
    else:
        logger.info(f"Content analysis completed: {analysis_type} for {content_type}", **context)


def log_market_analysis(
    analysis_type: str,
    projects_analyzed: int,
    success: bool,
    duration: float,
    market_trend: Optional[str] = None,
    error: Optional[str] = None
):
    """Log market analysis with structured data"""
    logger = StructuredLogger('web3_gaming.market_analytics')
    
    context = {
        'analysis_type': analysis_type,
        'projects_analyzed': projects_analyzed,
        'success': success,
        'duration_ms': round(duration * 1000, 2),
        'market_trend': market_trend
    }
    
    if error:
        context['error'] = error
        logger.error(f"Market analysis failed: {analysis_type}", **context)
    else:
        logger.info(f"Market analysis completed: {analysis_type}", **context)


def log_database_operation(
    operation: str,
    table: str,
    records_affected: int,
    success: bool,
    duration: float,
    error: Optional[str] = None
):
    """Log database operation with structured data"""
    logger = StructuredLogger('web3_gaming.database')
    
    context = {
        'operation': operation,
        'table': table,
        'records_affected': records_affected,
        'success': success,
        'duration_ms': round(duration * 1000, 2)
    }
    
    if error:
        context['error'] = error
        logger.error(f"Database operation failed: {operation} on {table}", **context)
    else:
        logger.info(f"Database operation: {operation} on {table}", **context)


# Initialize service loggers
service_loggers = setup_service_loggers()


def get_logger(service: str) -> StructuredLogger:
    """Get logger for specific service"""
    return service_loggers.get(service, StructuredLogger(f'web3_gaming.{service}'))


# Setup default logging configuration
def initialize_logging():
    """Initialize logging with default configuration"""
    log_level = "INFO"  # Can be configured via environment variable
    log_file = "logs/web3_gaming_tracker.log"
    
    setup_logging(
        log_level=log_level,
        log_format="json",
        log_file=log_file,
        enable_console=True
    )
    
    # Log initialization
    logger = get_logger('system')
    logger.info("Logging system initialized", 
                log_level=log_level, 
                log_file=log_file,
                service_loggers=list(service_loggers.keys()))


def log_security_event(event_type: str, details: dict, severity: str = "INFO"):
    """Log security-related events with structured format"""
    logger = StructuredLogger('web3_gaming.security')

    context = {
        'event_type': event_type,
        'severity': severity,
        'timestamp': datetime.utcnow().isoformat(),
        'service': 'web3-gaming-tracker',
        **details
    }

    message = f"Security Event: {event_type}"

    if severity.upper() == "CRITICAL":
        logger.critical(message, **context)
    elif severity.upper() == "ERROR":
        logger.error(message, **context)
    elif severity.upper() == "WARNING":
        logger.warning(message, **context)
    else:
        logger.info(message, **context)


# Auto-initialize when module is imported
initialize_logging()
