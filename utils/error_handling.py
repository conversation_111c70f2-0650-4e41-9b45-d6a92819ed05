"""
Standardized Error Handling System for Web3 Gaming News Tracker
"""
import logging
import traceback
import functools
import asyncio
from datetime import datetime
from typing import Any, Dict, Optional, Callable, Union, Type
from enum import Enum
from dataclasses import dataclass, field
from fastapi import HTTPException
from pydantic import BaseModel


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification"""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    NOT_FOUND = "not_found"
    RATE_LIMIT = "rate_limit"
    EXTERNAL_API = "external_api"
    DATABASE = "database"
    BLOCKCHAIN = "blockchain"
    NETWORK = "network"
    CONFIGURATION = "configuration"
    BUSINESS_LOGIC = "business_logic"
    SYSTEM = "system"
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """Context information for errors"""
    user_id: Optional[str] = None
    request_id: Optional[str] = None
    endpoint: Optional[str] = None
    method: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class StandardError:
    """Standardized error structure"""
    error_id: str
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    user_message: str
    details: Dict[str, Any]
    timestamp: datetime
    context: Optional[ErrorContext] = None
    stack_trace: Optional[str] = None
    retry_after: Optional[int] = None  # Seconds to wait before retry


class ErrorResponse(BaseModel):
    """API error response model"""
    error: bool = True
    error_id: str
    category: str
    severity: str
    message: str
    details: Dict[str, Any] = {}
    timestamp: str
    retry_after: Optional[int] = None


class StandardizedErrorHandler:
    """Centralized error handling system"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.error_mappings = self._setup_error_mappings()
        self.user_message_templates = self._setup_user_messages()
    
    def _setup_error_mappings(self) -> Dict[Type[Exception], ErrorCategory]:
        """Map exception types to error categories"""
        return {
            ValueError: ErrorCategory.VALIDATION,
            TypeError: ErrorCategory.VALIDATION,
            KeyError: ErrorCategory.VALIDATION,
            FileNotFoundError: ErrorCategory.NOT_FOUND,
            PermissionError: ErrorCategory.AUTHORIZATION,
            ConnectionError: ErrorCategory.NETWORK,
            TimeoutError: ErrorCategory.NETWORK,
            HTTPException: ErrorCategory.EXTERNAL_API,
            # Add more mappings as needed
        }
    
    def _setup_user_messages(self) -> Dict[ErrorCategory, str]:
        """Setup user-friendly error messages"""
        return {
            ErrorCategory.VALIDATION: "The provided data is invalid. Please check your input and try again.",
            ErrorCategory.AUTHENTICATION: "Authentication failed. Please log in and try again.",
            ErrorCategory.AUTHORIZATION: "You don't have permission to perform this action.",
            ErrorCategory.NOT_FOUND: "The requested resource was not found.",
            ErrorCategory.RATE_LIMIT: "Too many requests. Please wait a moment and try again.",
            ErrorCategory.EXTERNAL_API: "External service is temporarily unavailable. Please try again later.",
            ErrorCategory.DATABASE: "Database operation failed. Please try again later.",
            ErrorCategory.BLOCKCHAIN: "Blockchain operation failed. Please check the network and try again.",
            ErrorCategory.NETWORK: "Network connection failed. Please check your connection and try again.",
            ErrorCategory.CONFIGURATION: "System configuration error. Please contact support.",
            ErrorCategory.BUSINESS_LOGIC: "Operation could not be completed due to business rules.",
            ErrorCategory.SYSTEM: "System error occurred. Please try again later.",
            ErrorCategory.UNKNOWN: "An unexpected error occurred. Please try again later."
        }
    
    def classify_error(self, error: Exception) -> ErrorCategory:
        """Classify error into appropriate category"""
        error_type = type(error)
        
        # Direct mapping
        if error_type in self.error_mappings:
            return self.error_mappings[error_type]
        
        # Check error message for specific patterns
        error_str = str(error).lower()
        
        if any(keyword in error_str for keyword in ['timeout', 'connection', 'network']):
            return ErrorCategory.NETWORK
        elif any(keyword in error_str for keyword in ['rate limit', 'too many requests']):
            return ErrorCategory.RATE_LIMIT
        elif any(keyword in error_str for keyword in ['unauthorized', 'forbidden']):
            return ErrorCategory.AUTHORIZATION
        elif any(keyword in error_str for keyword in ['not found', '404']):
            return ErrorCategory.NOT_FOUND
        elif any(keyword in error_str for keyword in ['database', 'sql', 'connection pool']):
            return ErrorCategory.DATABASE
        elif any(keyword in error_str for keyword in ['blockchain', 'rpc', 'gas', 'nonce']):
            return ErrorCategory.BLOCKCHAIN
        
        return ErrorCategory.UNKNOWN
    
    def determine_severity(self, error: Exception, category: ErrorCategory) -> ErrorSeverity:
        """Determine error severity"""
        # Critical errors
        if category in [ErrorCategory.SYSTEM, ErrorCategory.DATABASE]:
            return ErrorSeverity.CRITICAL
        
        # High severity errors
        if category in [ErrorCategory.BLOCKCHAIN, ErrorCategory.CONFIGURATION]:
            return ErrorSeverity.HIGH
        
        # Medium severity errors
        if category in [ErrorCategory.EXTERNAL_API, ErrorCategory.NETWORK, ErrorCategory.BUSINESS_LOGIC]:
            return ErrorSeverity.MEDIUM
        
        # Low severity errors
        return ErrorSeverity.LOW
    
    def generate_error_id(self) -> str:
        """Generate unique error ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        import uuid
        return f"ERR_{timestamp}_{str(uuid.uuid4())[:8]}"
    
    def create_standard_error(
        self,
        error: Exception,
        context: Optional[ErrorContext] = None,
        custom_message: Optional[str] = None,
        custom_user_message: Optional[str] = None
    ) -> StandardError:
        """Create standardized error from exception"""
        
        category = self.classify_error(error)
        severity = self.determine_severity(error, category)
        error_id = self.generate_error_id()
        
        # Get user-friendly message
        user_message = custom_user_message or self.user_message_templates.get(
            category, 
            "An error occurred. Please try again later."
        )
        
        # Create error details
        details = {
            "exception_type": type(error).__name__,
            "original_message": str(error)
        }
        
        # Add context details if available
        if context:
            details.update({
                "endpoint": context.endpoint,
                "method": context.method,
                "user_id": context.user_id,
                "request_id": context.request_id
            })
        
        return StandardError(
            error_id=error_id,
            category=category,
            severity=severity,
            message=custom_message or str(error),
            user_message=user_message,
            details=details,
            timestamp=datetime.now(),
            context=context,
            stack_trace=traceback.format_exc() if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL] else None
        )
    
    def log_error(self, standard_error: StandardError):
        """Log error with appropriate level"""
        log_data = {
            "error_id": standard_error.error_id,
            "category": standard_error.category.value,
            "severity": standard_error.severity.value,
            "message": standard_error.message,
            "details": standard_error.details
        }
        
        if standard_error.severity == ErrorSeverity.CRITICAL:
            self.logger.critical("Critical error occurred", extra=log_data)
        elif standard_error.severity == ErrorSeverity.HIGH:
            self.logger.error("High severity error occurred", extra=log_data)
        elif standard_error.severity == ErrorSeverity.MEDIUM:
            self.logger.warning("Medium severity error occurred", extra=log_data)
        else:
            self.logger.info("Low severity error occurred", extra=log_data)
        
        # Log stack trace for high/critical errors
        if standard_error.stack_trace and standard_error.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            self.logger.error(f"Stack trace for {standard_error.error_id}:\n{standard_error.stack_trace}")
    
    def to_http_exception(self, standard_error: StandardError) -> HTTPException:
        """Convert standard error to HTTP exception"""
        status_code_mapping = {
            ErrorCategory.VALIDATION: 400,
            ErrorCategory.AUTHENTICATION: 401,
            ErrorCategory.AUTHORIZATION: 403,
            ErrorCategory.NOT_FOUND: 404,
            ErrorCategory.RATE_LIMIT: 429,
            ErrorCategory.EXTERNAL_API: 502,
            ErrorCategory.DATABASE: 503,
            ErrorCategory.BLOCKCHAIN: 503,
            ErrorCategory.NETWORK: 503,
            ErrorCategory.CONFIGURATION: 500,
            ErrorCategory.BUSINESS_LOGIC: 422,
            ErrorCategory.SYSTEM: 500,
            ErrorCategory.UNKNOWN: 500
        }
        
        status_code = status_code_mapping.get(standard_error.category, 500)
        
        error_response = ErrorResponse(
            error_id=standard_error.error_id,
            category=standard_error.category.value,
            severity=standard_error.severity.value,
            message=standard_error.user_message,
            details=standard_error.details,
            timestamp=standard_error.timestamp.isoformat(),
            retry_after=standard_error.retry_after
        )
        
        return HTTPException(
            status_code=status_code,
            detail=error_response.dict()
        )
    
    def handle_error(
        self,
        error: Exception,
        context: Optional[ErrorContext] = None,
        custom_message: Optional[str] = None,
        custom_user_message: Optional[str] = None,
        raise_http: bool = True
    ) -> StandardError:
        """Handle error with full processing"""
        
        standard_error = self.create_standard_error(
            error, context, custom_message, custom_user_message
        )
        
        # Log the error
        self.log_error(standard_error)
        
        # Raise HTTP exception if requested
        if raise_http:
            raise self.to_http_exception(standard_error)
        
        return standard_error


# Global error handler instance
error_handler = StandardizedErrorHandler()


def handle_errors(
    custom_message: Optional[str] = None,
    custom_user_message: Optional[str] = None,
    raise_http: bool = True
):
    """Decorator for automatic error handling"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                return error_handler.handle_error(
                    e, None, custom_message, custom_user_message, raise_http
                )
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                return error_handler.handle_error(
                    e, None, custom_message, custom_user_message, raise_http
                )
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator


def handle_api_errors(endpoint: str, method: str = "GET"):
    """Decorator for API endpoint error handling"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except HTTPException:
                # Re-raise HTTP exceptions as-is
                raise
            except Exception as e:
                context = ErrorContext(
                    endpoint=endpoint,
                    method=method,
                    request_id=kwargs.get('request_id'),
                    additional_data={"args": str(args), "kwargs": str(kwargs)}
                )
                
                return error_handler.handle_error(e, context, raise_http=True)
        
        return wrapper
    return decorator
