[tool:pytest]
# Pytest configuration for Web3 Gaming News Tracker

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Markers
markers =
    unit: Unit tests
    integration: Integration tests
    performance: Performance tests
    slow: Slow running tests
    blockchain: Blockchain-related tests
    content: Content intelligence tests
    market: Market analytics tests
    competitive: Competitive analysis tests
    api: API endpoint tests

# Output options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --show-capture=no
    --durations=10

# Async test configuration
asyncio_mode = auto

# Minimum version
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Ignore patterns
norecursedirs = 
    .git
    .tox
    dist
    build
    *.egg
    __pycache__
    .pytest_cache
    htmlcov

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*

# Coverage options (when using pytest-cov)
[coverage:run]
source = .
omit = 
    tests/*
    venv/*
    env/*
    .venv/*
    */migrations/*
    */venv/*
    */env/*
    */.venv/*
    setup.py
    conftest.py

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod

precision = 2
show_missing = True
skip_covered = False

[coverage:html]
directory = htmlcov
