"""
Tests for SQL optimization service
Validates that optimized queries produce correct results and improve performance
"""
import pytest
import time
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from unittest.mock import Mock, patch

from services.sql_optimization import SQLOptimizer, get_sql_optimizer
from services.dashboard_analytics import DashboardAnalytics
from models.gaming import Article, GamingProject, TwitterPost, RedditPost, NFTCollection, BlockchainData
from tests.conftest import test_db


class TestSQLOptimizer:
    """Test SQL optimization service"""
    
    def test_sql_optimizer_initialization(self, test_db):
        """Test SQL optimizer can be initialized"""
        optimizer = SQLOptimizer(test_db)
        assert optimizer.db == test_db
        
        # Test factory function
        optimizer2 = get_sql_optimizer(test_db)
        assert isinstance(optimizer2, SQLOptimizer)
    
    def test_dashboard_overview_optimized_structure(self, test_db):
        """Test optimized dashboard overview returns correct structure"""
        optimizer = SQLOptimizer(test_db)
        
        # Create test data
        self._create_test_dashboard_data(test_db)
        
        result = optimizer.get_dashboard_overview_optimized(hours=24)
        
        # Verify structure
        assert isinstance(result, dict)
        required_keys = [
            "total_articles", "total_sources", "total_gaming_projects", 
            "total_nft_collections", "articles_last_24h", "active_blockchain_networks",
            "average_sentiment", "top_gaming_categories", "network_activity"
        ]
        for key in required_keys:
            assert key in result
        
        # Verify data types
        assert isinstance(result["total_articles"], int)
        assert isinstance(result["active_blockchain_networks"], int)
        assert isinstance(result["average_sentiment"], float)
        assert isinstance(result["top_gaming_categories"], list)
        assert isinstance(result["network_activity"], list)
    
    def test_gaming_projects_with_metrics_optimized(self, test_db):
        """Test optimized gaming projects query"""
        optimizer = SQLOptimizer(test_db)
        
        # Create test data
        self._create_test_gaming_projects_data(test_db)
        
        result = optimizer.get_gaming_projects_with_metrics_optimized(
            blockchain="ethereum",
            category="defi",
            hours=24
        )
        
        # Verify structure
        assert isinstance(result, list)
        if result:  # If we have data
            project = result[0]
            assert "id" in project
            assert "project_name" in project
            assert "metrics" in project
            
            # Verify metrics structure
            metrics = project["metrics"]
            expected_metrics = [
                "total_articles", "recent_articles", "avg_sentiment",
                "nft_collections_count", "twitter_mentions", "reddit_mentions"
            ]
            for metric in expected_metrics:
                assert metric in metrics
    
    def test_social_media_with_gaming_context_optimized(self, test_db):
        """Test optimized social media query with gaming context"""
        optimizer = SQLOptimizer(test_db)
        
        # Create test data
        self._create_test_social_media_data(test_db)
        
        result = optimizer.get_social_media_with_gaming_context_optimized(
            platform="twitter",
            hours=24,
            min_engagement=0,
            gaming_only=True
        )
        
        # Verify structure
        assert isinstance(result, dict)
        assert "twitter" in result
        
        twitter_posts = result["twitter"]
        assert isinstance(twitter_posts, list)
        
        if twitter_posts:  # If we have data
            post = twitter_posts[0]
            assert "id" in post
            assert "text" in post
            assert "engagement" in post
            assert "project_details" in post
    
    def test_blockchain_activity_with_projects_optimized(self, test_db):
        """Test optimized blockchain activity query"""
        optimizer = SQLOptimizer(test_db)
        
        # Create test data
        self._create_test_blockchain_data(test_db)
        
        result = optimizer.get_blockchain_activity_with_projects_optimized(
            hours=24,
            networks=["ethereum", "polygon"]
        )
        
        # Verify structure
        assert isinstance(result, list)
        
        if result:  # If we have data
            activity = result[0]
            assert "id" in activity
            assert "blockchain_network" in activity
            assert "gaming_context" in activity
            
            # Verify gaming context structure
            context = activity["gaming_context"]
            expected_context_keys = [
                "project_name", "project_display_name", "project_category",
                "token_symbol", "nft_collection_name"
            ]
            for key in expected_context_keys:
                assert key in context
    
    def test_performance_improvement(self, test_db):
        """Test that optimized queries are faster than non-optimized ones"""
        optimizer = SQLOptimizer(test_db)
        analytics = DashboardAnalytics(test_db)
        
        # Create substantial test data
        self._create_large_test_dataset(test_db)
        
        # Time old approach (multiple separate queries)
        start_time = time.time()
        
        # Simulate old approach
        total_articles = test_db.query(Article).count()
        total_projects = test_db.query(GamingProject).filter(GamingProject.is_active == True).count()
        recent_articles = test_db.query(Article).filter(
            Article.published_at >= datetime.utcnow() - timedelta(hours=24)
        ).count()
        
        old_time = time.time() - start_time
        
        # Time new optimized approach
        start_time = time.time()
        overview_data = optimizer.get_dashboard_overview_optimized(hours=24)
        new_time = time.time() - start_time
        
        # Verify performance improvement (optimized should be faster)
        # Allow some tolerance for test environment variations
        assert new_time <= old_time * 1.5  # At worst, should be within 50% of old time
        
        # Verify data consistency
        assert overview_data["total_articles"] == total_articles
        assert overview_data["total_gaming_projects"] == total_projects
        assert overview_data["articles_last_24h"] == recent_articles
    
    def test_query_result_consistency(self, test_db):
        """Test that optimized queries return consistent results with original queries"""
        optimizer = SQLOptimizer(test_db)
        
        # Create test data
        self._create_test_dashboard_data(test_db)
        
        # Get results from optimized query
        optimized_result = optimizer.get_dashboard_overview_optimized(hours=24)
        
        # Get results from individual queries (old approach)
        total_articles = test_db.query(Article).count()
        total_projects = test_db.query(GamingProject).filter(GamingProject.is_active == True).count()
        recent_articles = test_db.query(Article).filter(
            Article.published_at >= datetime.utcnow() - timedelta(hours=24)
        ).count()
        
        # Verify consistency
        assert optimized_result["total_articles"] == total_articles
        assert optimized_result["total_gaming_projects"] == total_projects
        assert optimized_result["articles_last_24h"] == recent_articles
    
    def test_error_handling(self, test_db):
        """Test error handling in optimized queries"""
        optimizer = SQLOptimizer(test_db)
        
        # Test with invalid parameters
        with pytest.raises(Exception):
            optimizer.get_gaming_projects_with_metrics_optimized(hours=-1)
        
        # Test with database connection issues
        with patch.object(test_db, 'execute', side_effect=Exception("Database error")):
            with pytest.raises(Exception):
                optimizer.get_dashboard_overview_optimized(hours=24)
    
    def test_caching_integration(self, test_db):
        """Test that optimized queries work with caching"""
        optimizer = SQLOptimizer(test_db)
        
        # Create test data
        self._create_test_dashboard_data(test_db)
        
        # First call should hit database
        result1 = optimizer.get_dashboard_overview_optimized(hours=24)
        
        # Second call should return same result
        result2 = optimizer.get_dashboard_overview_optimized(hours=24)
        
        # Results should be identical
        assert result1 == result2
    
    # Helper methods for creating test data
    
    def _create_test_dashboard_data(self, db: Session):
        """Create test data for dashboard tests"""
        # Create test gaming projects
        project = GamingProject(
            project_name="test-game",
            name="Test Game",
            blockchain_network="ethereum",
            category="defi",
            is_active=True
        )
        db.add(project)
        
        # Create test articles
        article = Article(
            title="Test Article",
            content="Test content",
            url="https://test.com",
            published_at=datetime.utcnow(),
            blockchain_network="ethereum",
            gaming_category="defi",
            sentiment_score=0.5
        )
        db.add(article)
        
        db.commit()
    
    def _create_test_gaming_projects_data(self, db: Session):
        """Create test data for gaming projects tests"""
        project = GamingProject(
            project_name="test-defi-game",
            name="Test DeFi Game",
            blockchain_network="ethereum",
            category="defi",
            token_symbol="TDG",
            token_address="0x123...",
            is_active=True,
            daily_active_users=1000,
            total_value_locked=50000.0
        )
        db.add(project)
        db.commit()
    
    def _create_test_social_media_data(self, db: Session):
        """Create test data for social media tests"""
        twitter_post = TwitterPost(
            twitter_id="123456789",
            text="Test gaming tweet",
            author_username="testuser",
            created_at=datetime.utcnow(),
            like_count=10,
            retweet_count=5,
            is_gaming_related=True,
            gaming_projects=["test-game"],
            sentiment_score=0.7
        )
        db.add(twitter_post)
        db.commit()
    
    def _create_test_blockchain_data(self, db: Session):
        """Create test data for blockchain activity tests"""
        blockchain_data = BlockchainData(
            blockchain_network="ethereum",
            contract_address="0x123...",
            event_type="Transfer",
            block_number=12345678,
            transaction_hash="0xabc...",
            block_timestamp=datetime.utcnow(),
            player_address="0xdef...",
            game_action="item_purchase"
        )
        db.add(blockchain_data)
        db.commit()
    
    def _create_large_test_dataset(self, db: Session):
        """Create larger dataset for performance testing"""
        # Create multiple gaming projects
        for i in range(10):
            project = GamingProject(
                project_name=f"test-game-{i}",
                name=f"Test Game {i}",
                blockchain_network="ethereum" if i % 2 == 0 else "polygon",
                category="defi" if i % 3 == 0 else "nft",
                is_active=True
            )
            db.add(project)
        
        # Create multiple articles
        for i in range(100):
            article = Article(
                title=f"Test Article {i}",
                content=f"Test content {i}",
                url=f"https://test{i}.com",
                published_at=datetime.utcnow() - timedelta(hours=i % 48),
                blockchain_network="ethereum" if i % 2 == 0 else "polygon",
                sentiment_score=0.5
            )
            db.add(article)
        
        db.commit()


@pytest.mark.asyncio
class TestSQLOptimizationIntegration:
    """Integration tests for SQL optimization with API endpoints"""
    
    async def test_optimized_dashboard_endpoint_integration(self, test_db):
        """Test that optimized dashboard endpoint works correctly"""
        from api.dashboard_endpoints import get_dashboard_overview
        
        # Create test data
        optimizer = SQLOptimizer(test_db)
        optimizer._create_test_dashboard_data = lambda db: None  # Mock data creation
        
        # Test endpoint
        result = await get_dashboard_overview(db=test_db, hours=24)
        
        # Verify result structure
        assert hasattr(result, 'total_articles')
        assert hasattr(result, 'total_gaming_projects')
        assert hasattr(result, 'recent_activity_score')
    
    async def test_optimized_social_media_endpoint_integration(self, test_db):
        """Test that optimized social media endpoint works correctly"""
        from api.social_media_endpoints import get_twitter_posts
        
        # Test endpoint
        result = await get_twitter_posts(
            db=test_db,
            limit=10,
            hours=24,
            min_engagement=0,
            gaming_only=True
        )
        
        # Verify result structure
        assert "posts" in result
        assert "total_count" in result
        assert "filters_applied" in result
        assert isinstance(result["posts"], list)
