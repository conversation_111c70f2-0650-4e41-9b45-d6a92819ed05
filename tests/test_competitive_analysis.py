"""
Unit tests for Competitive Analysis services
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

from services.competitive_analysis import (
    CompetitiveAnalysisEngine,
    competitive_analysis_engine
)
from services.competitive_analysis import (
    CompetitiveMetrics,
    ProjectComparison,
    CompetitivePosition
)


class TestCompetitiveAnalysisEngine:
    """Test competitive analysis engine functionality"""
    
    def test_initialization(self):
        """Test CompetitiveAnalysisEngine initialization"""
        engine = CompetitiveAnalysisEngine()
        assert engine is not None
        assert hasattr(engine, 'gaming_projects')
        assert hasattr(engine, 'competitive_metrics')
        assert hasattr(engine, 'market_categories')
    
    @pytest.mark.asyncio
    async def test_analyze_competitive_landscape(self):
        """Test competitive landscape analysis"""
        engine = CompetitiveAnalysisEngine()
        
        # Mock project data
        mock_projects_data = {
            'axie-infinity': {
                'market_cap': 1000000000,
                'daily_active_users': 500000,
                'transaction_volume_24h': 50000000,
                'social_mentions': 10000,
                'github_commits_30d': 150,
                'partnerships_count': 25,
                'token_holders': 100000,
                'category': 'p2e'
            },
            'star-atlas': {
                'market_cap': 500000000,
                'daily_active_users': 200000,
                'transaction_volume_24h': 20000000,
                'social_mentions': 5000,
                'github_commits_30d': 200,
                'partnerships_count': 15,
                'token_holders': 50000,
                'category': 'metaverse'
            }
        }
        
        with patch.object(engine, '_fetch_projects_data', return_value=mock_projects_data):
            result = await engine.analyze_competitive_landscape(['axie-infinity', 'star-atlas'])
            
            assert 'competitive_metrics' in result
            assert 'market_positioning' in result
            assert 'competitive_advantages' in result
            assert 'market_share_analysis' in result
            assert 'growth_comparison' in result
            
            # Verify competitive metrics structure
            metrics = result['competitive_metrics']
            assert 'axie-infinity' in metrics
            assert 'star-atlas' in metrics
            
            # Check individual project metrics
            axie_metrics = metrics['axie-infinity']
            assert 'market_share' in axie_metrics
            assert 'user_adoption' in axie_metrics
            assert 'innovation_score' in axie_metrics
            assert 'community_strength' in axie_metrics
    
    def test_market_share_calculation(self):
        """Test market share calculation"""
        engine = CompetitiveAnalysisEngine()
        
        projects_data = {
            'project_a': {'market_cap': 1000000000, 'daily_active_users': 100000},
            'project_b': {'market_cap': 500000000, 'daily_active_users': 50000},
            'project_c': {'market_cap': 250000000, 'daily_active_users': 25000}
        }
        
        market_shares = engine._calculate_market_share(projects_data)
        
        # Verify market share calculations
        assert abs(market_shares['project_a']['market_cap_share'] - 0.571) < 0.01  # ~57.1%
        assert abs(market_shares['project_b']['market_cap_share'] - 0.286) < 0.01  # ~28.6%
        assert abs(market_shares['project_c']['market_cap_share'] - 0.143) < 0.01  # ~14.3%
        
        # Verify user share calculations
        assert abs(market_shares['project_a']['user_share'] - 0.571) < 0.01  # ~57.1%
        assert abs(market_shares['project_b']['user_share'] - 0.286) < 0.01  # ~28.6%
        assert abs(market_shares['project_c']['user_share'] - 0.143) < 0.01  # ~14.3%
    
    def test_user_adoption_scoring(self):
        """Test user adoption scoring"""
        engine = CompetitiveAnalysisEngine()
        
        # High adoption project
        high_adoption_data = {
            'daily_active_users': 1000000,
            'monthly_active_users': 2500000,
            'user_growth_30d': 0.25,  # 25% growth
            'user_retention_rate': 0.75  # 75% retention
        }
        
        high_score = engine._calculate_user_adoption_score(high_adoption_data)
        assert high_score > 0.8
        
        # Low adoption project
        low_adoption_data = {
            'daily_active_users': 10000,
            'monthly_active_users': 25000,
            'user_growth_30d': -0.10,  # 10% decline
            'user_retention_rate': 0.30  # 30% retention
        }
        
        low_score = engine._calculate_user_adoption_score(low_adoption_data)
        assert low_score < 0.3
    
    def test_innovation_score_calculation(self):
        """Test innovation score calculation"""
        engine = CompetitiveAnalysisEngine()
        
        # Highly innovative project
        innovative_data = {
            'github_commits_30d': 300,
            'new_features_released': 15,
            'technical_partnerships': 10,
            'research_publications': 5,
            'patent_applications': 3
        }
        
        innovation_score = engine._calculate_innovation_score(innovative_data)
        assert innovation_score > 0.8
        
        # Low innovation project
        stagnant_data = {
            'github_commits_30d': 10,
            'new_features_released': 1,
            'technical_partnerships': 1,
            'research_publications': 0,
            'patent_applications': 0
        }
        
        innovation_score = engine._calculate_innovation_score(stagnant_data)
        assert innovation_score < 0.3
    
    def test_community_strength_assessment(self):
        """Test community strength assessment"""
        engine = CompetitiveAnalysisEngine()
        
        # Strong community
        strong_community_data = {
            'social_mentions': 50000,
            'discord_members': 100000,
            'telegram_members': 75000,
            'twitter_followers': 500000,
            'community_sentiment': 0.8,
            'community_engagement_rate': 0.15
        }
        
        community_score = engine._calculate_community_strength(strong_community_data)
        assert community_score > 0.8
        
        # Weak community
        weak_community_data = {
            'social_mentions': 1000,
            'discord_members': 5000,
            'telegram_members': 3000,
            'twitter_followers': 10000,
            'community_sentiment': 0.3,
            'community_engagement_rate': 0.02
        }
        
        community_score = engine._calculate_community_strength(weak_community_data)
        assert community_score < 0.3
    
    def test_technical_advancement_scoring(self):
        """Test technical advancement scoring"""
        engine = CompetitiveAnalysisEngine()
        
        # Technically advanced project
        advanced_tech_data = {
            'blockchain_efficiency': 0.9,
            'scalability_score': 0.85,
            'security_audit_score': 0.95,
            'interoperability_features': 8,
            'smart_contract_complexity': 0.8
        }
        
        tech_score = engine._calculate_technical_advancement(advanced_tech_data)
        assert tech_score > 0.8
        
        # Basic technology project
        basic_tech_data = {
            'blockchain_efficiency': 0.4,
            'scalability_score': 0.3,
            'security_audit_score': 0.6,
            'interoperability_features': 2,
            'smart_contract_complexity': 0.3
        }
        
        tech_score = engine._calculate_technical_advancement(basic_tech_data)
        assert tech_score < 0.4
    
    def test_partnership_ecosystem_evaluation(self):
        """Test partnership ecosystem evaluation"""
        engine = CompetitiveAnalysisEngine()
        
        # Strong partnership ecosystem
        strong_partnerships = {
            'strategic_partnerships': 20,
            'technical_integrations': 15,
            'exchange_listings': 25,
            'institutional_investors': 10,
            'partnership_quality_score': 0.85
        }
        
        partnership_score = engine._calculate_partnership_strength(strong_partnerships)
        assert partnership_score > 0.8
        
        # Weak partnership ecosystem
        weak_partnerships = {
            'strategic_partnerships': 3,
            'technical_integrations': 2,
            'exchange_listings': 5,
            'institutional_investors': 1,
            'partnership_quality_score': 0.4
        }
        
        partnership_score = engine._calculate_partnership_strength(weak_partnerships)
        assert partnership_score < 0.4
    
    def test_tokenomics_analysis(self):
        """Test tokenomics analysis"""
        engine = CompetitiveAnalysisEngine()
        
        # Well-designed tokenomics
        good_tokenomics = {
            'token_distribution_fairness': 0.8,
            'utility_score': 0.9,
            'inflation_rate': 0.05,  # 5% annual inflation
            'staking_participation': 0.6,  # 60% staked
            'token_velocity': 0.3,  # Moderate velocity
            'governance_participation': 0.4  # 40% participation
        }
        
        tokenomics_score = engine._analyze_tokenomics(good_tokenomics)
        assert tokenomics_score > 0.7
        
        # Poor tokenomics design
        poor_tokenomics = {
            'token_distribution_fairness': 0.3,
            'utility_score': 0.4,
            'inflation_rate': 0.25,  # 25% annual inflation
            'staking_participation': 0.1,  # 10% staked
            'token_velocity': 0.8,  # High velocity (bad for value accrual)
            'governance_participation': 0.05  # 5% participation
        }
        
        tokenomics_score = engine._analyze_tokenomics(poor_tokenomics)
        assert tokenomics_score < 0.4
    
    def test_development_activity_assessment(self):
        """Test development activity assessment"""
        engine = CompetitiveAnalysisEngine()
        
        # High development activity
        high_activity = {
            'github_commits_30d': 250,
            'active_developers': 50,
            'code_quality_score': 0.85,
            'release_frequency': 2.5,  # Releases per month
            'bug_fix_time_avg': 2.5,  # Days to fix bugs
            'feature_completion_rate': 0.9
        }
        
        dev_score = engine._assess_development_activity(high_activity)
        assert dev_score > 0.8
        
        # Low development activity
        low_activity = {
            'github_commits_30d': 20,
            'active_developers': 3,
            'code_quality_score': 0.5,
            'release_frequency': 0.2,  # One release every 5 months
            'bug_fix_time_avg': 15,  # 15 days to fix bugs
            'feature_completion_rate': 0.4
        }
        
        dev_score = engine._assess_development_activity(low_activity)
        assert dev_score < 0.3
    
    @pytest.mark.asyncio
    async def test_competitive_positioning_analysis(self):
        """Test competitive positioning analysis"""
        engine = CompetitiveAnalysisEngine()
        
        mock_competitive_data = {
            'axie-infinity': {
                'market_share': 0.4,
                'user_adoption': 0.8,
                'innovation_score': 0.7,
                'community_strength': 0.9,
                'technical_advancement': 0.6,
                'partnership_strength': 0.8,
                'tokenomics_score': 0.7,
                'development_activity': 0.6
            },
            'star-atlas': {
                'market_share': 0.2,
                'user_adoption': 0.6,
                'innovation_score': 0.9,
                'community_strength': 0.7,
                'technical_advancement': 0.8,
                'partnership_strength': 0.6,
                'tokenomics_score': 0.8,
                'development_activity': 0.9
            }
        }
        
        positioning = engine._analyze_competitive_positioning(mock_competitive_data)
        
        assert 'market_leaders' in positioning
        assert 'innovation_leaders' in positioning
        assert 'community_leaders' in positioning
        assert 'technical_leaders' in positioning
        
        # Axie should be market leader due to higher market share
        assert 'axie-infinity' in positioning['market_leaders']
        
        # Star Atlas should be innovation leader due to higher innovation score
        assert 'star-atlas' in positioning['innovation_leaders']
    
    @pytest.mark.asyncio
    async def test_competitive_advantages_identification(self):
        """Test competitive advantages identification"""
        engine = CompetitiveAnalysisEngine()
        
        project_metrics = {
            'market_share': 0.35,
            'user_adoption': 0.9,  # Strong advantage
            'innovation_score': 0.6,
            'community_strength': 0.95,  # Strong advantage
            'technical_advancement': 0.4,  # Weakness
            'partnership_strength': 0.7,
            'tokenomics_score': 0.8,
            'development_activity': 0.5
        }
        
        advantages = engine._identify_competitive_advantages('test-project', project_metrics)
        
        assert 'strengths' in advantages
        assert 'weaknesses' in advantages
        assert 'opportunities' in advantages
        assert 'threats' in advantages
        
        # Should identify user adoption and community as strengths
        strengths = advantages['strengths']
        assert any('user adoption' in strength.lower() for strength in strengths)
        assert any('community' in strength.lower() for strength in strengths)
        
        # Should identify technical advancement as weakness
        weaknesses = advantages['weaknesses']
        assert any('technical' in weakness.lower() for weakness in weaknesses)


class TestIntegrationScenarios:
    """Test integration scenarios for competitive analysis"""
    
    @pytest.mark.asyncio
    async def test_full_competitive_analysis_pipeline(self):
        """Test full competitive analysis pipeline"""
        engine = competitive_analysis_engine
        
        # Mock comprehensive project data
        mock_projects_data = {
            'axie-infinity': {
                'market_cap': 1000000000,
                'daily_active_users': 500000,
                'transaction_volume_24h': 50000000,
                'social_mentions': 10000,
                'github_commits_30d': 150,
                'partnerships_count': 25,
                'token_holders': 100000,
                'category': 'p2e',
                'sentiment_score': 0.7
            },
            'star-atlas': {
                'market_cap': 500000000,
                'daily_active_users': 200000,
                'transaction_volume_24h': 20000000,
                'social_mentions': 5000,
                'github_commits_30d': 200,
                'partnerships_count': 15,
                'token_holders': 50000,
                'category': 'metaverse',
                'sentiment_score': 0.8
            },
            'decentraland': {
                'market_cap': 800000000,
                'daily_active_users': 300000,
                'transaction_volume_24h': 30000000,
                'social_mentions': 8000,
                'github_commits_30d': 100,
                'partnerships_count': 20,
                'token_holders': 80000,
                'category': 'metaverse',
                'sentiment_score': 0.6
            }
        }
        
        with patch.object(engine, '_fetch_projects_data', return_value=mock_projects_data):
            result = await engine.analyze_competitive_landscape(['axie-infinity', 'star-atlas', 'decentraland'])
            
            # Verify comprehensive analysis results
            assert 'competitive_metrics' in result
            assert 'market_positioning' in result
            assert 'competitive_advantages' in result
            assert 'market_share_analysis' in result
            assert 'growth_comparison' in result
            
            # Verify all projects are analyzed
            metrics = result['competitive_metrics']
            assert len(metrics) == 3
            assert 'axie-infinity' in metrics
            assert 'star-atlas' in metrics
            assert 'decentraland' in metrics
            
            # Verify market positioning identifies leaders
            positioning = result['market_positioning']
            assert len(positioning['market_leaders']) > 0
            assert len(positioning['innovation_leaders']) > 0
    
    @pytest.mark.asyncio
    async def test_category_based_competitive_analysis(self):
        """Test competitive analysis within specific categories"""
        engine = CompetitiveAnalysisEngine()
        
        # Mock metaverse category projects
        metaverse_projects = {
            'decentraland': {
                'market_cap': 800000000,
                'daily_active_users': 300000,
                'virtual_land_sales': 50000,
                'creator_tools_usage': 0.7,
                'category': 'metaverse'
            },
            'the-sandbox': {
                'market_cap': 600000000,
                'daily_active_users': 250000,
                'virtual_land_sales': 40000,
                'creator_tools_usage': 0.8,
                'category': 'metaverse'
            }
        }
        
        with patch.object(engine, '_fetch_projects_data', return_value=metaverse_projects):
            result = await engine.analyze_competitive_landscape(['decentraland', 'the-sandbox'])
            
            # Should provide category-specific insights
            assert result is not None
            
            # Verify category-specific metrics are considered
            metrics = result['competitive_metrics']
            assert 'decentraland' in metrics
            assert 'the-sandbox' in metrics
    
    @pytest.mark.asyncio
    async def test_competitive_trend_analysis(self):
        """Test competitive trend analysis over time"""
        engine = CompetitiveAnalysisEngine()
        
        # Mock historical competitive data
        historical_data = {
            'axie-infinity': {
                '30d_ago': {'market_share': 0.45, 'user_adoption': 0.8},
                '7d_ago': {'market_share': 0.42, 'user_adoption': 0.82},
                'current': {'market_share': 0.40, 'user_adoption': 0.85}
            },
            'star-atlas': {
                '30d_ago': {'market_share': 0.15, 'user_adoption': 0.5},
                '7d_ago': {'market_share': 0.18, 'user_adoption': 0.55},
                'current': {'market_share': 0.20, 'user_adoption': 0.60}
            }
        }
        
        with patch.object(engine, '_fetch_historical_competitive_data', return_value=historical_data):
            trends = engine._analyze_competitive_trends(['axie-infinity', 'star-atlas'], '30d')
            
            assert 'market_share_trends' in trends
            assert 'user_adoption_trends' in trends
            assert 'competitive_momentum' in trends
            
            # Verify trend direction detection
            axie_trend = trends['market_share_trends']['axie-infinity']
            star_atlas_trend = trends['market_share_trends']['star-atlas']
            
            assert axie_trend['direction'] == 'declining'  # Losing market share
            assert star_atlas_trend['direction'] == 'growing'  # Gaining market share
