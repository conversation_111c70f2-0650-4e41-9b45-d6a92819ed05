"""
Test suite for Alchemy API client
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timedelta

from blockchain.data_clients.alchemy import AlchemyClient, AlchemyNetworkConfig
from config.settings import get_settings


class TestAlchemyClient:
    """Test cases for Alchemy API client"""
    
    @pytest.fixture
    def alchemy_client(self):
        """Create Alchemy client for testing"""
        with patch('blockchain.data_clients.alchemy.settings') as mock_settings:
            mock_settings.blockchain_data.alchemy_api_key = "test-api-key"
            mock_settings.blockchain_data.alchemy_base_url = "https://api.g.alchemy.com/data/v1"
            mock_settings.blockchain_data.alchemy_supported_networks = ["eth-mainnet", "polygon-mainnet"]
            mock_settings.blockchain_data.rate_limit_per_minute = 60
            
            client = AlchemyClient()
            return client
    
    def test_client_initialization(self, alchemy_client):
        """Test client initialization"""
        assert alchemy_client.api_key == "test-api-key"
        assert alchemy_client.base_url == "https://api.g.alchemy.com/data/v1"
        assert len(alchemy_client.supported_networks) == 2
        assert "eth-mainnet" in alchemy_client.supported_networks
    
    def test_network_configs(self, alchemy_client):
        """Test network configuration mappings"""
        # Test Ethereum config
        eth_config = alchemy_client.get_network_config("ethereum")
        assert eth_config is not None
        assert eth_config.name == "Ethereum"
        assert eth_config.alchemy_id == "eth-mainnet"
        assert eth_config.chain_id == 1
        assert eth_config.gaming_focus is True
        
        # Test Polygon config
        polygon_config = alchemy_client.get_network_config("polygon")
        assert polygon_config is not None
        assert polygon_config.name == "Polygon"
        assert polygon_config.alchemy_id == "polygon-mainnet"
        assert polygon_config.chain_id == 137
        
        # Test invalid network
        invalid_config = alchemy_client.get_network_config("invalid")
        assert invalid_config is None
    
    def test_supported_networks(self, alchemy_client):
        """Test supported networks list"""
        networks = alchemy_client.get_supported_networks()
        assert isinstance(networks, list)
        assert "ethereum" in networks
        assert "polygon" in networks
        assert "arbitrum" in networks
        assert "base" in networks
    
    def test_auth_headers(self, alchemy_client):
        """Test authentication headers"""
        headers = alchemy_client._get_auth_headers()
        assert headers['Content-Type'] == 'application/json'
        assert headers['Accept'] == 'application/json'
    
    def test_api_url_construction(self, alchemy_client):
        """Test API URL construction"""
        url = alchemy_client._get_api_url("assets/tokens/by-address")
        expected = "https://api.g.alchemy.com/data/v1/test-api-key/assets/tokens/by-address"
        assert url == expected
    
    def test_cache_functionality(self, alchemy_client):
        """Test caching mechanism"""
        # Test cache miss
        assert not alchemy_client._is_cache_valid("test_key")
        assert alchemy_client._get_cached_data("test_key") is None
        
        # Test cache set and hit
        test_data = {"test": "data"}
        alchemy_client._cache_data("test_key", test_data)
        assert alchemy_client._is_cache_valid("test_key")
        assert alchemy_client._get_cached_data("test_key") == test_data
        
        # Test cache expiry (simulate old timestamp)
        old_time = datetime.utcnow() - timedelta(minutes=10)
        alchemy_client._cache["test_key"] = (old_time, test_data)
        assert not alchemy_client._is_cache_valid("test_key")
    
    @pytest.mark.asyncio
    async def test_gaming_wallet_portfolio(self, alchemy_client):
        """Test gaming wallet portfolio method"""
        mock_response = {
            'data': [
                {
                    'network': 'eth-mainnet',
                    'address': '0x123',
                    'tokenBalance': '1000000000000000000',
                    'tokenMetadata': {'symbol': 'ETH', 'name': 'Ethereum'},
                    'tokenPrices': {'currency': 'usd', 'value': '2000'}
                }
            ]
        }
        
        with patch.object(alchemy_client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response
            
            result = await alchemy_client.get_gaming_wallet_portfolio(
                addresses=['0x123'],
                networks=['eth-mainnet']
            )
            
            assert 'portfolio_data' in result
            assert result['addresses'] == ['0x123']
            assert result['networks'] == ['eth-mainnet']
            assert len(result['portfolio_data']) == 1
            
            # Verify API call
            mock_request.assert_called_once()
            call_args = mock_request.call_args
            assert call_args[0][0] == 'POST'  # method
            assert 'assets/tokens/by-address' in call_args[0][1]  # endpoint
    
    @pytest.mark.asyncio
    async def test_transaction_history(self, alchemy_client):
        """Test transaction history method"""
        mock_response = {
            'transactions': [
                {
                    'hash': '0xabc123',
                    'network': 'eth-mainnet',
                    'fromAddress': '0x123',
                    'toAddress': '0x456',
                    'value': '1000000000000000000'
                }
            ],
            'totalCount': 1
        }
        
        with patch.object(alchemy_client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response
            
            result = await alchemy_client.get_transaction_history(
                addresses=['0x123'],
                networks=['eth-mainnet']
            )
            
            assert 'transactions' in result
            assert result['address'] == '0x123'
            assert result['network'] == 'eth-mainnet'
            assert result['total_count'] == 1
            assert len(result['transactions']) == 1
    
    @pytest.mark.asyncio
    async def test_health_check_success(self, alchemy_client):
        """Test successful health check"""
        with patch.object(alchemy_client, '_test_endpoint', new_callable=AsyncMock) as mock_test:
            mock_test.return_value = None  # No exception means success
            
            result = await alchemy_client.health_check()
            
            assert result['status'] == 'healthy'
            assert result['api_key_configured'] is True
            assert 'supported_networks' in result
            assert 'timestamp' in result
    
    @pytest.mark.asyncio
    async def test_health_check_failure(self, alchemy_client):
        """Test failed health check"""
        with patch.object(alchemy_client, '_test_endpoint', new_callable=AsyncMock) as mock_test:
            mock_test.side_effect = Exception("API connection failed")
            
            result = await alchemy_client.health_check()
            
            assert result['status'] == 'unhealthy'
            assert 'error' in result
            assert 'API connection failed' in result['error']
    
    @pytest.mark.asyncio
    async def test_test_endpoint(self, alchemy_client):
        """Test the test endpoint functionality"""
        mock_response = {'data': []}
        
        with patch.object(alchemy_client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response
            
            # Should not raise exception
            await alchemy_client._test_endpoint()
            
            # Verify correct API call
            mock_request.assert_called_once()
            call_args = mock_request.call_args
            assert call_args[0][0] == 'POST'
            assert 'assets/tokens/by-address' in call_args[0][1]
    
    @pytest.mark.asyncio
    async def test_test_endpoint_invalid_response(self, alchemy_client):
        """Test test endpoint with invalid response"""
        mock_response = {'invalid': 'response'}
        
        with patch.object(alchemy_client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = mock_response
            
            with pytest.raises(Exception, match="Invalid response format"):
                await alchemy_client._test_endpoint()
    
    @pytest.mark.asyncio
    async def test_gaming_tokens_data_placeholder(self, alchemy_client):
        """Test gaming tokens data method (placeholder implementation)"""
        result = await alchemy_client.get_gaming_tokens_data(['ETH', 'MATIC'])
        
        # Currently returns empty list as placeholder
        assert isinstance(result, list)
        assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_nft_collection_data_placeholder(self, alchemy_client):
        """Test NFT collection data method (placeholder implementation)"""
        result = await alchemy_client.get_nft_collection_data('0x123')
        
        assert isinstance(result, dict)
        assert result['collection_address'] == '0x123'
        assert result['placeholder'] is True
    
    @pytest.mark.asyncio
    async def test_gaming_protocol_metrics_placeholder(self, alchemy_client):
        """Test gaming protocol metrics method (placeholder implementation)"""
        result = await alchemy_client.get_gaming_protocol_metrics('axie-infinity')
        
        assert isinstance(result, dict)
        assert result['protocol_name'] == 'axie-infinity'
        assert result['placeholder'] is True
    
    def test_network_config_dataclass(self):
        """Test AlchemyNetworkConfig dataclass"""
        config = AlchemyNetworkConfig(
            name="Test Network",
            alchemy_id="test-mainnet",
            chain_id=999,
            native_token="TEST",
            gaming_focus=True
        )
        
        assert config.name == "Test Network"
        assert config.alchemy_id == "test-mainnet"
        assert config.chain_id == 999
        assert config.native_token == "TEST"
        assert config.gaming_focus is True
    
    @pytest.mark.asyncio
    async def test_empty_addresses_handling(self, alchemy_client):
        """Test handling of empty address lists"""
        # Test wallet portfolio with empty addresses
        result = await alchemy_client.get_gaming_wallet_portfolio([])
        assert result == {}
        
        # Test transaction history with empty addresses
        result = await alchemy_client.get_transaction_history([])
        assert result == {}
    
    @pytest.mark.asyncio
    async def test_error_handling(self, alchemy_client):
        """Test error handling in API methods"""
        with patch.object(alchemy_client, '_make_request', new_callable=AsyncMock) as mock_request:
            mock_request.side_effect = Exception("API Error")
            
            # Test wallet portfolio error handling
            result = await alchemy_client.get_gaming_wallet_portfolio(['0x123'])
            assert result == {}
            
            # Test transaction history error handling
            result = await alchemy_client.get_transaction_history(['0x123'])
            assert result == {}


if __name__ == "__main__":
    pytest.main([__file__])
