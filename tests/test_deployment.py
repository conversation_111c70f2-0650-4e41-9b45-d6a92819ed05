"""
Deployment Testing Suite
Comprehensive tests for application deployment validation
"""

import pytest
import requests
import time
import subprocess
import os
import json
from typing import Dict, List
from unittest.mock import patch, MagicMock

# Test configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"
TEST_TIMEOUT = 30


class TestDeploymentValidation:
    """Test deployment validation and service health"""
    
    def test_backend_health_check(self):
        """Test backend health endpoint"""
        try:
            response = requests.get(f"{BACKEND_URL}/health", timeout=10)
            assert response.status_code == 200
            
            data = response.json()
            assert "status" in data
            assert data["status"] in ["healthy", "degraded"]
            
            # Check required health components
            assert "database" in data
            assert "redis" in data
            assert "blockchain" in data
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Backend health check failed: {e}")
    
    def test_api_documentation_accessible(self):
        """Test API documentation is accessible"""
        try:
            response = requests.get(f"{BACKEND_URL}/docs", timeout=10)
            assert response.status_code == 200
            assert "swagger" in response.text.lower() or "openapi" in response.text.lower()
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"API documentation not accessible: {e}")
    
    def test_metrics_endpoint(self):
        """Test Prometheus metrics endpoint"""
        try:
            response = requests.get(f"{BACKEND_URL}/metrics", timeout=10)
            assert response.status_code == 200
            
            # Check for basic Prometheus metrics
            metrics_text = response.text
            assert "http_requests_total" in metrics_text or "process_" in metrics_text
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Metrics endpoint not accessible: {e}")
    
    def test_frontend_accessibility(self):
        """Test frontend is accessible"""
        try:
            response = requests.get(FRONTEND_URL, timeout=10)
            assert response.status_code == 200
            
            # Check for React app indicators
            content = response.text
            assert "Web3 Gaming" in content or "root" in content
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Frontend not accessible: {e}")


class TestAPIEndpoints:
    """Test critical API endpoints"""
    
    def test_gaming_projects_endpoint(self):
        """Test gaming projects endpoint"""
        try:
            response = requests.get(f"{BACKEND_URL}/api/v1/gaming/projects", timeout=10)
            assert response.status_code == 200
            
            data = response.json()
            assert isinstance(data, list) or isinstance(data, dict)
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Gaming projects endpoint failed: {e}")
    
    def test_content_intelligence_health(self):
        """Test content intelligence health endpoint"""
        try:
            response = requests.get(f"{BACKEND_URL}/api/v1/content-intelligence/health", timeout=10)
            assert response.status_code == 200
            
            data = response.json()
            assert "status" in data
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Content intelligence health check failed: {e}")
    
    def test_blockchain_endpoints(self):
        """Test blockchain data endpoints"""
        try:
            response = requests.get(f"{BACKEND_URL}/api/v1/blockchain/health", timeout=10)
            # Accept both 200 (healthy) and 503 (degraded but functional)
            assert response.status_code in [200, 503]
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Blockchain endpoints failed: {e}")
    
    def test_social_media_endpoints(self):
        """Test social media endpoints"""
        try:
            response = requests.get(f"{BACKEND_URL}/api/v1/social/health", timeout=10)
            # Accept both 200 (healthy) and 503 (degraded but functional)
            assert response.status_code in [200, 503]
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Social media endpoints failed: {e}")


class TestSecurity:
    """Test security configurations"""
    
    def test_security_headers(self):
        """Test security headers are present"""
        try:
            response = requests.get(f"{BACKEND_URL}/health", timeout=10)
            headers = response.headers
            
            # Check for important security headers
            security_headers = [
                'X-Content-Type-Options',
                'X-Frame-Options',
                'X-XSS-Protection',
                'Strict-Transport-Security'
            ]
            
            present_headers = []
            for header in security_headers:
                if header in headers:
                    present_headers.append(header)
            
            # At least some security headers should be present
            assert len(present_headers) > 0, f"No security headers found. Expected: {security_headers}"
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Security headers test failed: {e}")
    
    def test_cors_configuration(self):
        """Test CORS configuration"""
        try:
            # Test preflight request
            response = requests.options(f"{BACKEND_URL}/health", timeout=10)
            assert response.status_code == 200
            
            headers = response.headers
            assert 'Access-Control-Allow-Origin' in headers
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"CORS configuration test failed: {e}")
    
    def test_rate_limiting(self):
        """Test rate limiting functionality"""
        try:
            # Make multiple requests quickly
            responses = []
            for i in range(10):
                response = requests.get(f"{BACKEND_URL}/health", timeout=5)
                responses.append(response.status_code)
            
            # All requests should succeed or some should be rate limited
            success_count = sum(1 for status in responses if status == 200)
            rate_limited_count = sum(1 for status in responses if status == 429)
            
            # Either all succeed (rate limiting not triggered) or some are rate limited
            assert success_count > 0, "No successful requests"
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Rate limiting test failed: {e}")


class TestPerformance:
    """Test performance characteristics"""
    
    def test_api_response_times(self):
        """Test API response times are acceptable"""
        endpoints = [
            "/health",
            "/api/v1/gaming/projects",
            "/api/v1/content-intelligence/health"
        ]
        
        for endpoint in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{BACKEND_URL}{endpoint}", timeout=10)
                response_time = time.time() - start_time
                
                # Response time should be under 5 seconds for health checks
                assert response_time < 5.0, f"Endpoint {endpoint} took {response_time:.2f}s (> 5s)"
                assert response.status_code in [200, 503], f"Endpoint {endpoint} returned {response.status_code}"
                
            except requests.exceptions.RequestException as e:
                pytest.fail(f"Performance test failed for {endpoint}: {e}")
    
    def test_concurrent_requests(self):
        """Test handling of concurrent requests"""
        import threading
        import queue
        
        results = queue.Queue()
        
        def make_request():
            try:
                response = requests.get(f"{BACKEND_URL}/health", timeout=10)
                results.put(response.status_code)
            except Exception as e:
                results.put(f"Error: {e}")
        
        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        success_count = 0
        while not results.empty():
            result = results.get()
            if result == 200:
                success_count += 1
        
        # At least some requests should succeed
        assert success_count > 0, "No concurrent requests succeeded"


class TestDataIntegrity:
    """Test data integrity and consistency"""
    
    def test_database_connectivity(self):
        """Test database connectivity through API"""
        try:
            response = requests.get(f"{BACKEND_URL}/health", timeout=10)
            assert response.status_code == 200
            
            data = response.json()
            assert "database" in data
            
            # Database should be healthy or at least connected
            db_status = data.get("database", {})
            assert db_status.get("status") in ["healthy", "connected", "degraded"]
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Database connectivity test failed: {e}")
    
    def test_redis_connectivity(self):
        """Test Redis connectivity through API"""
        try:
            response = requests.get(f"{BACKEND_URL}/health", timeout=10)
            assert response.status_code == 200
            
            data = response.json()
            assert "redis" in data
            
            # Redis should be healthy or at least connected
            redis_status = data.get("redis", {})
            assert redis_status.get("status") in ["healthy", "connected", "degraded"]
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Redis connectivity test failed: {e}")


class TestPhase7Features:
    """Test Phase 7 specific features"""
    
    def test_content_intelligence_classification(self):
        """Test content intelligence classification endpoint"""
        test_data = {
            "title": "New P2E Game Launch",
            "content": "Revolutionary play-to-earn gaming experience",
            "summary": "Gaming announcement"
        }
        
        try:
            response = requests.post(
                f"{BACKEND_URL}/api/v1/content-intelligence/classify",
                json=test_data,
                timeout=15
            )
            
            # Accept both success and service unavailable
            assert response.status_code in [200, 503], f"Unexpected status: {response.status_code}"
            
            if response.status_code == 200:
                data = response.json()
                assert "primary_category" in data or "error" in data
                
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Content intelligence classification test failed: {e}")
    
    def test_market_analytics_endpoints(self):
        """Test market analytics endpoints"""
        try:
            response = requests.get(
                f"{BACKEND_URL}/api/v1/content-intelligence/market/sector-analysis",
                timeout=15
            )
            
            # Accept both success and service unavailable
            assert response.status_code in [200, 503], f"Unexpected status: {response.status_code}"
            
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Market analytics test failed: {e}")


class TestIntegrationWorkflows:
    """Test complete integration workflows"""
    
    def test_dashboard_data_flow(self):
        """Test data flow from backend to dashboard"""
        try:
            # Test that dashboard can get data from backend
            response = requests.get(
                f"{BACKEND_URL}/api/v1/content-intelligence/analytics/dashboard",
                timeout=15
            )
            
            # Accept both success and service unavailable
            assert response.status_code in [200, 503], f"Unexpected status: {response.status_code}"
            
            if response.status_code == 200:
                data = response.json()
                # Should have some dashboard structure
                assert isinstance(data, dict)
                
        except requests.exceptions.RequestException as e:
            pytest.fail(f"Dashboard data flow test failed: {e}")
    
    def test_websocket_connectivity(self):
        """Test WebSocket connectivity (basic check)"""
        try:
            # Try to connect to WebSocket endpoint
            import websocket
            
            def on_error(ws, error):
                pass
            
            def on_close(ws, close_status_code, close_msg):
                pass
            
            ws = websocket.WebSocketApp(
                f"ws://localhost:8000/ws/test",
                on_error=on_error,
                on_close=on_close
            )
            
            # Just test that we can attempt connection
            # Actual WebSocket testing would require more complex setup
            
        except ImportError:
            # websocket-client not installed, skip test
            pytest.skip("websocket-client not installed")
        except Exception as e:
            # WebSocket connection issues are acceptable in basic deployment test
            pass


def run_deployment_tests():
    """Run all deployment tests"""
    print("🧪 Running deployment validation tests...")
    
    # Run pytest with specific test file
    result = subprocess.run([
        "python", "-m", "pytest", 
        "tests/test_deployment.py", 
        "-v", 
        "--tb=short"
    ], capture_output=True, text=True)
    
    print("Test Results:")
    print(result.stdout)
    if result.stderr:
        print("Errors:")
        print(result.stderr)
    
    return result.returncode == 0


if __name__ == "__main__":
    # Run tests when script is executed directly
    success = run_deployment_tests()
    exit(0 if success else 1)
