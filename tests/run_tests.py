"""
Test runner for Web3 Gaming News Tracker
"""
import pytest
import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_all_tests():
    """Run all test suites"""
    print("🧪 Running Web3 Gaming News Tracker Test Suite")
    print("=" * 60)
    
    # Test configuration
    test_args = [
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--strict-markers",  # Strict marker checking
        "--disable-warnings",  # Disable warnings for cleaner output
        str(Path(__file__).parent),  # Test directory
    ]
    
    # Run tests
    exit_code = pytest.main(test_args)
    
    if exit_code == 0:
        print("\n✅ All tests passed successfully!")
    else:
        print(f"\n❌ Tests failed with exit code: {exit_code}")
    
    return exit_code

def run_specific_test_suite(suite_name):
    """Run a specific test suite"""
    test_files = {
        "blockchain": "test_blockchain_components.py",
        "content": "test_content_intelligence.py",
        "market": "test_market_analytics.py",
        "competitive": "test_competitive_analysis.py",
        "api": "test_api_endpoints.py"
    }
    
    if suite_name not in test_files:
        print(f"❌ Unknown test suite: {suite_name}")
        print(f"Available suites: {', '.join(test_files.keys())}")
        return 1
    
    test_file = Path(__file__).parent / test_files[suite_name]
    
    print(f"🧪 Running {suite_name} test suite")
    print("=" * 40)
    
    test_args = [
        "-v",
        "--tb=short",
        str(test_file)
    ]
    
    exit_code = pytest.main(test_args)
    
    if exit_code == 0:
        print(f"\n✅ {suite_name} tests passed!")
    else:
        print(f"\n❌ {suite_name} tests failed with exit code: {exit_code}")
    
    return exit_code

def run_coverage_report():
    """Run tests with coverage report"""
    print("🧪 Running tests with coverage analysis")
    print("=" * 50)
    
    test_args = [
        "--cov=services",
        "--cov=blockchain",
        "--cov=api",
        "--cov-report=html",
        "--cov-report=term-missing",
        "-v",
        str(Path(__file__).parent),
    ]
    
    exit_code = pytest.main(test_args)
    
    if exit_code == 0:
        print("\n✅ Coverage report generated successfully!")
        print("📊 Open htmlcov/index.html to view detailed coverage report")
    else:
        print(f"\n❌ Coverage analysis failed with exit code: {exit_code}")
    
    return exit_code

def run_performance_tests():
    """Run performance-focused tests"""
    print("🚀 Running performance tests")
    print("=" * 30)
    
    test_args = [
        "-v",
        "-m", "performance",  # Only run tests marked with @pytest.mark.performance
        str(Path(__file__).parent),
    ]
    
    exit_code = pytest.main(test_args)
    
    if exit_code == 0:
        print("\n✅ Performance tests passed!")
    else:
        print(f"\n❌ Performance tests failed with exit code: {exit_code}")
    
    return exit_code

def main():
    """Main test runner entry point"""
    if len(sys.argv) < 2:
        print("Usage: python run_tests.py [all|blockchain|content|market|competitive|api|coverage|performance]")
        return 1
    
    command = sys.argv[1].lower()
    
    if command == "all":
        return run_all_tests()
    elif command == "coverage":
        return run_coverage_report()
    elif command == "performance":
        return run_performance_tests()
    elif command in ["blockchain", "content", "market", "competitive", "api"]:
        return run_specific_test_suite(command)
    else:
        print(f"❌ Unknown command: {command}")
        print("Available commands: all, blockchain, content, market, competitive, api, coverage, performance")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
