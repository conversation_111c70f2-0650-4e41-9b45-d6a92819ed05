"""
Pytest configuration and fixtures for Web3 Gaming News Tracker tests
"""
import pytest
import asyncio
import sys
import os
from unittest.mock import Mock, AsyncMock
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.base import Base
from config.settings import get_settings


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_settings():
    """Test settings configuration"""
    settings = get_settings()
    # Override for testing
    settings.database.url = "sqlite:///./test.db"
    settings.redis.url = "redis://localhost:6379/1"  # Use different DB for tests
    return settings


@pytest.fixture(scope="session")
def test_engine(test_settings):
    """Create test database engine"""
    engine = create_engine(
        test_settings.database.url,
        connect_args={"check_same_thread": False}  # SQLite specific
    )
    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)


@pytest.fixture
def test_db_session(test_engine):
    """Create test database session"""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def test_client():
    """Create test FastAPI client"""
    from api.main import app
    return TestClient(app)


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing"""
    mock_redis = Mock()
    mock_redis.get = Mock(return_value=None)
    mock_redis.set = Mock(return_value=True)
    mock_redis.delete = Mock(return_value=True)
    mock_redis.exists = Mock(return_value=False)
    mock_redis.ping = Mock(return_value=True)
    return mock_redis


@pytest.fixture
def mock_blockchain_client():
    """Mock blockchain client for testing"""
    mock_client = AsyncMock()
    mock_client.get_latest_block_number = AsyncMock(return_value=18000000)
    mock_client.get_transaction_count = AsyncMock(return_value=100)
    mock_client.get_balance = AsyncMock(return_value=1000000000000000000)  # 1 ETH in wei
    mock_client.get_contract_code = AsyncMock(return_value="0x608060405234801561001057600080fd5b50...")
    return mock_client


@pytest.fixture
def sample_gaming_project():
    """Sample gaming project data for testing"""
    return {
        "name": "Test Gaming Project",
        "slug": "test-gaming-project",
        "description": "A test gaming project for unit tests",
        "website": "https://testgaming.com",
        "blockchain": "ethereum",
        "category": "p2e",
        "token_symbol": "TGP",
        "contract_address": "******************************************",
        "is_active": True
    }


@pytest.fixture
def sample_article():
    """Sample article data for testing"""
    return {
        "title": "Test Gaming Article",
        "content": "This is a test article about blockchain gaming and NFTs.",
        "summary": "Test article summary",
        "url": "https://example.com/test-article",
        "source": "test-source",
        "published_at": "2025-07-07T12:00:00Z",
        "sentiment_score": 0.5,
        "gaming_relevance": 0.8
    }


@pytest.fixture
def sample_blockchain_data():
    """Sample blockchain data for testing"""
    return {
        "chain": "ethereum",
        "contract_address": "******************************************",
        "transaction_hash": "0xabcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890",
        "block_number": 18000000,
        "event_type": "Transfer",
        "from_address": "******************************************",
        "to_address": "******************************************",
        "value": "1000000000000000000",  # 1 ETH in wei
        "gas_used": 21000,
        "gas_price": "20000000000",  # 20 gwei
        "timestamp": "2025-07-07T12:00:00Z"
    }


@pytest.fixture
def sample_market_data():
    """Sample market data for testing"""
    return {
        "project_id": "test-gaming-project",
        "price_usd": 1.50,
        "market_cap": 150000000,
        "volume_24h": 5000000,
        "price_change_24h": 0.05,  # 5% increase
        "volume_change_24h": -0.10,  # 10% decrease
        "sentiment_score": 0.65,
        "social_mentions": 1250,
        "developer_activity": 85
    }


@pytest.fixture
def sample_content_classification():
    """Sample content classification data for testing"""
    return {
        "title": "Axie Infinity Introduces New Breeding Mechanics",
        "content": "Players can now breed Axies with enhanced genetic algorithms for better battle performance",
        "summary": "New breeding system improves gameplay",
        "expected_category": "play-to-earn",
        "expected_entities": ["axie infinity"],
        "expected_sentiment": "positive"
    }


@pytest.fixture
def sample_portfolio():
    """Sample investment portfolio for testing"""
    return {
        "axie-infinity": 10000,  # $10k allocation
        "star-atlas": 5000,     # $5k allocation
        "decentraland": 3000,   # $3k allocation
        "gala-games": 2000      # $2k allocation
    }


# Async test helpers
@pytest.fixture
def async_mock():
    """Helper for creating async mocks"""
    def _async_mock(*args, **kwargs):
        mock = AsyncMock(*args, **kwargs)
        return mock
    return _async_mock


# Test data cleanup
@pytest.fixture(autouse=True)
def cleanup_test_data(test_db_session):
    """Automatically cleanup test data after each test"""
    yield
    # Cleanup logic here if needed
    test_db_session.rollback()


# Mock external services
@pytest.fixture
def mock_external_apis():
    """Mock external API services"""
    return {
        "coingecko": Mock(),
        "etherscan": Mock(),
        "solscan": Mock(),
        "dune": Mock(),
        "twitter": Mock(),
        "reddit": Mock()
    }
