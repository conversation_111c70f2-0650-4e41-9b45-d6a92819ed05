"""
Unit tests for blockchain components
"""
import pytest
import asyncio
from unittest.mock import Mock, Async<PERSON>ock, patch
from datetime import datetime, timedelta

from blockchain.rpc import <PERSON>ChainManager, EthereumClient, SolanaClient
from blockchain.gaming_contracts import GamingContractDetector
from blockchain.gaming_analytics import GamingAnalytics
from blockchain.data_clients.manager import BlockchainDataManager


class TestMultiChainManager:
    """Test MultiChainManager functionality"""
    
    def test_initialization(self):
        """Test MultiChainManager initialization"""
        manager = MultiChainManager()
        assert manager is not None
        assert hasattr(manager, 'clients')
        assert hasattr(manager, 'supported_chains')
    
    def test_get_client(self):
        """Test getting blockchain clients"""
        manager = MultiChainManager()
        
        # Test getting Ethereum client
        eth_client = manager.get_client('ethereum')
        assert eth_client is not None
        
        # Test getting Solana client
        sol_client = manager.get_client('solana')
        assert sol_client is not None
        
        # Test invalid chain
        invalid_client = manager.get_client('invalid_chain')
        assert invalid_client is None
    
    @pytest.mark.asyncio
    async def test_connection_testing(self, mock_blockchain_client):
        """Test blockchain connection testing"""
        manager = MultiChainManager()
        
        with patch.object(manager, 'get_client', return_value=mock_blockchain_client):
            # Test successful connection
            mock_blockchain_client.test_connection = AsyncMock(return_value=True)
            result = await manager.test_connection('ethereum')
            assert result is True
            
            # Test failed connection
            mock_blockchain_client.test_connection = AsyncMock(return_value=False)
            result = await manager.test_connection('ethereum')
            assert result is False


class TestEthereumClient:
    """Test Ethereum client functionality"""
    
    @pytest.mark.asyncio
    async def test_get_latest_block_number(self, mock_blockchain_client):
        """Test getting latest block number"""
        client = EthereumClient("https://test-rpc.com")
        
        with patch.object(client, '_make_request', return_value={'result': '0x112a880'}):
            block_number = await client.get_latest_block_number()
            assert block_number == 18000000
    
    @pytest.mark.asyncio
    async def test_get_transaction_count(self, mock_blockchain_client):
        """Test getting transaction count"""
        client = EthereumClient("https://test-rpc.com")
        address = "******************************************"
        
        with patch.object(client, '_make_request', return_value={'result': '0x64'}):
            tx_count = await client.get_transaction_count(address)
            assert tx_count == 100
    
    @pytest.mark.asyncio
    async def test_get_balance(self, mock_blockchain_client):
        """Test getting account balance"""
        client = EthereumClient("https://test-rpc.com")
        address = "******************************************"
        
        with patch.object(client, '_make_request', return_value={'result': '0xde0b6b3a7640000'}):
            balance = await client.get_balance(address)
            assert balance == **********000000000  # 1 ETH in wei
    
    @pytest.mark.asyncio
    async def test_get_contract_code(self, mock_blockchain_client):
        """Test getting contract code"""
        client = EthereumClient("https://test-rpc.com")
        address = "******************************************"
        
        with patch.object(client, '_make_request', return_value={'result': '0x608060405234801561001057600080fd5b50'}):
            code = await client.get_contract_code(address)
            assert code.startswith('0x608060405234801561001057600080fd5b50')
    
    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling in Ethereum client"""
        client = EthereumClient("https://invalid-rpc.com")
        
        with patch.object(client, '_make_request', side_effect=Exception("Connection failed")):
            with pytest.raises(Exception):
                await client.get_latest_block_number()


class TestSolanaClient:
    """Test Solana client functionality"""
    
    @pytest.mark.asyncio
    async def test_get_account_info(self):
        """Test getting Solana account info"""
        client = SolanaClient("https://test-solana-rpc.com")
        address = "********************************"
        
        mock_response = {
            'result': {
                'value': {
                    'lamports': **********,  # 1 SOL
                    'owner': '********************************',
                    'executable': False,
                    'data': ['', 'base64']
                }
            }
        }
        
        with patch.object(client, '_make_request', return_value=mock_response):
            account_info = await client.get_account_info(address)
            assert account_info['lamports'] == **********
    
    @pytest.mark.asyncio
    async def test_get_token_accounts(self):
        """Test getting token accounts"""
        client = SolanaClient("https://test-solana-rpc.com")
        owner = "********************************"
        
        mock_response = {
            'result': {
                'value': [
                    {
                        'account': {
                            'data': {
                                'parsed': {
                                    'info': {
                                        'mint': 'So111111111********************************',
                                        'tokenAmount': {'amount': '**********', 'decimals': 9}
                                    }
                                }
                            }
                        }
                    }
                ]
            }
        }
        
        with patch.object(client, '_make_request', return_value=mock_response):
            token_accounts = await client.get_token_accounts(owner)
            assert len(token_accounts) == 1
            assert token_accounts[0]['account']['data']['parsed']['info']['mint'] == 'So111111111********************************'


class TestGamingContractDetector:
    """Test gaming contract detection functionality"""
    
    def test_initialization(self):
        """Test GamingContractDetector initialization"""
        detector = GamingContractDetector()
        assert detector is not None
        assert hasattr(detector, 'gaming_patterns')
        assert hasattr(detector, 'nft_patterns')
    
    @pytest.mark.asyncio
    async def test_analyze_contract(self, mock_blockchain_client):
        """Test contract analysis"""
        detector = GamingContractDetector()
        contract_address = "******************************************"
        
        # Mock contract code with gaming-related patterns
        gaming_contract_code = "0x608060405234801561001057600080fd5b50" + "mint" + "transfer" + "game"
        
        with patch('blockchain.rpc.multi_chain_manager.get_client', return_value=mock_blockchain_client):
            mock_blockchain_client.get_contract_code = AsyncMock(return_value=gaming_contract_code)
            
            analysis = await detector.analyze_contract(contract_address, "ethereum")
            
            assert analysis is not None
            assert 'is_gaming_related' in analysis
            assert 'confidence_score' in analysis
            assert 'detected_patterns' in analysis
    
    def test_pattern_matching(self):
        """Test gaming pattern matching"""
        detector = GamingContractDetector()
        
        # Test gaming-related code
        gaming_code = "function mint() public { /* gaming logic */ }"
        patterns = detector._extract_patterns(gaming_code)
        assert len(patterns) > 0
        
        # Test non-gaming code
        non_gaming_code = "function transfer() public { /* basic transfer */ }"
        patterns = detector._extract_patterns(non_gaming_code)
        # Should have fewer or no gaming-specific patterns
        assert isinstance(patterns, list)
    
    def test_confidence_scoring(self):
        """Test confidence score calculation"""
        detector = GamingContractDetector()
        
        # High confidence gaming patterns
        high_confidence_patterns = ['mint', 'breed', 'battle', 'quest', 'nft']
        score = detector._calculate_confidence(high_confidence_patterns)
        assert score > 0.7
        
        # Low confidence patterns
        low_confidence_patterns = ['transfer']
        score = detector._calculate_confidence(low_confidence_patterns)
        assert score < 0.3


class TestGamingAnalytics:
    """Test gaming analytics functionality"""
    
    def test_initialization(self):
        """Test GamingAnalytics initialization"""
        analytics = GamingAnalytics()
        assert analytics is not None
        assert hasattr(analytics, 'projects')
    
    @pytest.mark.asyncio
    async def test_calculate_metrics(self, sample_gaming_project, sample_blockchain_data):
        """Test metrics calculation"""
        analytics = GamingAnalytics()
        
        with patch.object(analytics, '_fetch_project_data', return_value=sample_blockchain_data):
            metrics = await analytics.calculate_project_metrics(sample_gaming_project['slug'])
            
            assert metrics is not None
            assert 'transaction_volume' in metrics
            assert 'user_activity' in metrics
            assert 'token_metrics' in metrics
    
    @pytest.mark.asyncio
    async def test_trend_analysis(self, sample_gaming_project):
        """Test trend analysis"""
        analytics = GamingAnalytics()
        
        # Mock historical data
        mock_historical_data = [
            {'date': '2025-07-01', 'volume': 1000000, 'users': 5000},
            {'date': '2025-07-02', 'volume': 1100000, 'users': 5200},
            {'date': '2025-07-03', 'volume': 1200000, 'users': 5400},
        ]
        
        with patch.object(analytics, '_fetch_historical_data', return_value=mock_historical_data):
            trends = await analytics.analyze_trends(sample_gaming_project['slug'], days=7)
            
            assert trends is not None
            assert 'volume_trend' in trends
            assert 'user_trend' in trends
            assert 'growth_rate' in trends


class TestBlockchainDataManager:
    """Test blockchain data manager functionality"""
    
    def test_initialization(self):
        """Test BlockchainDataManager initialization"""
        manager = BlockchainDataManager()
        assert manager is not None
        assert hasattr(manager, 'clients')
    
    @pytest.mark.asyncio
    async def test_initialize_clients(self, mock_external_apis):
        """Test client initialization"""
        manager = BlockchainDataManager()
        
        with patch('blockchain.data_clients.manager.EtherscanClient') as mock_etherscan:
            with patch('blockchain.data_clients.manager.SolscanClient') as mock_solscan:
                mock_etherscan.return_value = mock_external_apis['etherscan']
                mock_solscan.return_value = mock_external_apis['solscan']
                
                await manager.initialize_clients()
                
                assert 'etherscan' in manager.clients
                assert 'solscan' in manager.clients
    
    @pytest.mark.asyncio
    async def test_get_client_status(self):
        """Test getting client status"""
        manager = BlockchainDataManager()
        manager.clients = {
            'etherscan': Mock(),
            'solscan': Mock()
        }
        
        status = await manager.get_client_status()
        
        assert 'initialized_clients' in status
        assert 'etherscan' in status['initialized_clients']
        assert 'solscan' in status['initialized_clients']
    
    @pytest.mark.asyncio
    async def test_fetch_contract_data(self, mock_external_apis):
        """Test fetching contract data"""
        manager = BlockchainDataManager()
        manager.clients = {'etherscan': mock_external_apis['etherscan']}
        
        contract_address = "******************************************"
        mock_external_apis['etherscan'].get_contract_abi = AsyncMock(return_value={'status': '1', 'result': '[]'})
        
        data = await manager.fetch_contract_data(contract_address, 'ethereum')
        
        assert data is not None
        mock_external_apis['etherscan'].get_contract_abi.assert_called_once()


class TestIntegrationScenarios:
    """Test integration scenarios between blockchain components"""
    
    @pytest.mark.asyncio
    async def test_full_contract_analysis_pipeline(self, mock_blockchain_client, sample_gaming_project):
        """Test full contract analysis pipeline"""
        # Initialize components
        detector = GamingContractDetector()
        analytics = GamingAnalytics()
        
        contract_address = sample_gaming_project['contract_address']
        
        # Mock the full pipeline
        with patch('blockchain.rpc.multi_chain_manager.get_client', return_value=mock_blockchain_client):
            mock_blockchain_client.get_contract_code = AsyncMock(return_value="0x608060405234801561001057600080fd5b50mint")
            
            # Step 1: Detect if contract is gaming-related
            analysis = await detector.analyze_contract(contract_address, "ethereum")
            assert analysis['is_gaming_related'] is True
            
            # Step 2: If gaming-related, analyze metrics
            if analysis['is_gaming_related']:
                with patch.object(analytics, '_fetch_project_data', return_value={'volume': 1000000}):
                    metrics = await analytics.calculate_project_metrics(sample_gaming_project['slug'])
                    assert metrics is not None
    
    @pytest.mark.asyncio
    async def test_multi_chain_data_aggregation(self, mock_external_apis):
        """Test aggregating data from multiple chains"""
        manager = BlockchainDataManager()
        manager.clients = {
            'etherscan': mock_external_apis['etherscan'],
            'solscan': mock_external_apis['solscan']
        }
        
        # Mock responses from different chains
        mock_external_apis['etherscan'].get_contract_transactions = AsyncMock(return_value=[
            {'hash': '0xeth1', 'value': '**********000000000'}
        ])
        mock_external_apis['solscan'].get_account_transactions = AsyncMock(return_value=[
            {'signature': 'sol1', 'amount': **********}
        ])
        
        # Aggregate data from both chains
        eth_data = await manager.fetch_contract_data("******************************************", 'ethereum')
        sol_data = await manager.fetch_contract_data("********************************", 'solana')
        
        assert eth_data is not None
        assert sol_data is not None
    
    @pytest.mark.asyncio
    async def test_error_recovery_scenarios(self, mock_blockchain_client):
        """Test error recovery in blockchain operations"""
        detector = GamingContractDetector()
        
        with patch('blockchain.rpc.multi_chain_manager.get_client', return_value=mock_blockchain_client):
            # Test network timeout recovery
            mock_blockchain_client.get_contract_code = AsyncMock(side_effect=asyncio.TimeoutError())
            
            analysis = await detector.analyze_contract("******************************************", "ethereum")
            # Should handle timeout gracefully
            assert analysis is not None
            assert analysis['is_gaming_related'] is False  # Default when error occurs
            
            # Test invalid contract address
            mock_blockchain_client.get_contract_code = AsyncMock(return_value="0x")  # Empty code
            
            analysis = await detector.analyze_contract("******************************************", "ethereum")
            assert analysis['is_gaming_related'] is False
