"""
Test suite for Alchemy integration with BlockchainDataManager
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock

from blockchain.data_clients.manager import BlockchainDataManager
from blockchain.data_clients.alchemy import AlchemyClient


class TestAlchemyManagerIntegration:
    """Test cases for Alchemy integration with manager"""
    
    @pytest.fixture
    def mock_settings(self):
        """Mock settings for testing"""
        with patch('blockchain.data_clients.manager.settings') as mock_settings:
            # Configure Alchemy as available
            mock_settings.blockchain_data.alchemy_api_key = "test-alchemy-key"
            mock_settings.blockchain_data.alchemy_base_url = "https://api.g.alchemy.com/data/v1"
            mock_settings.blockchain_data.alchemy_supported_networks = ["eth-mainnet", "polygon-mainnet"]
            mock_settings.blockchain_data.rate_limit_per_minute = 60
            
            # Disable other clients for focused testing
            mock_settings.blockchain_data.bitquery_api_key = ""
            mock_settings.blockchain_data.flipside_api_key = ""
            mock_settings.blockchain_data.cryptorank_api_key = ""
            mock_settings.blockchain_data.dextools_api_key = ""
            mock_settings.blockchain_data.etherscan_api_key = ""
            mock_settings.blockchain_data.solscan_api_key = ""
            mock_settings.gaming_api.coingecko_api_key = ""
            
            yield mock_settings
    
    @pytest.fixture
    def manager(self, mock_settings):
        """Create manager instance for testing"""
        return BlockchainDataManager()
    
    @pytest.mark.asyncio
    async def test_alchemy_client_initialization(self, manager, mock_settings):
        """Test that Alchemy client is properly initialized"""
        await manager.initialize_clients()
        
        assert 'alchemy' in manager.clients
        assert isinstance(manager.clients['alchemy'], AlchemyClient)
        assert len(manager.clients) == 1  # Only Alchemy should be initialized
    
    @pytest.mark.asyncio
    async def test_alchemy_priority_over_bitquery(self, mock_settings):
        """Test that Alchemy takes priority over BitQuery when both are available"""
        # Enable both Alchemy and BitQuery
        mock_settings.blockchain_data.alchemy_api_key = "test-alchemy-key"
        mock_settings.blockchain_data.bitquery_api_key = "test-bitquery-key"
        
        manager = BlockchainDataManager()
        await manager.initialize_clients()
        
        # Should have Alchemy but not BitQuery (due to priority logic)
        assert 'alchemy' in manager.clients
        assert 'bitquery' not in manager.clients
    
    @pytest.mark.asyncio
    async def test_bitquery_fallback_when_alchemy_unavailable(self, mock_settings):
        """Test BitQuery fallback when Alchemy is not available"""
        # Disable Alchemy, enable BitQuery
        mock_settings.blockchain_data.alchemy_api_key = ""
        mock_settings.blockchain_data.bitquery_api_key = "test-bitquery-key"
        
        with patch('blockchain.data_clients.manager.BitQueryClient') as mock_bitquery:
            manager = BlockchainDataManager()
            await manager.initialize_clients()
            
            # Should have BitQuery but not Alchemy
            assert 'bitquery' in manager.clients
            assert 'alchemy' not in manager.clients
    
    @pytest.mark.asyncio
    async def test_gaming_wallet_portfolio_method(self, manager, mock_settings):
        """Test new gaming wallet portfolio method"""
        await manager.initialize_clients()
        
        mock_portfolio_data = {
            'addresses': ['0x123'],
            'networks': ['eth-mainnet'],
            'portfolio_data': [{'token': 'ETH', 'balance': '1.0'}],
            'timestamp': '2024-01-01T00:00:00'
        }
        
        with patch.object(manager.clients['alchemy'], 'get_gaming_wallet_portfolio', new_callable=AsyncMock) as mock_method:
            mock_method.return_value = mock_portfolio_data
            
            result = await manager.get_gaming_wallet_portfolio(['0x123'], ['eth-mainnet'])
            
            assert result == mock_portfolio_data
            mock_method.assert_called_once_with(['0x123'], ['eth-mainnet'])
    
    @pytest.mark.asyncio
    async def test_transaction_history_method(self, manager, mock_settings):
        """Test new transaction history method"""
        await manager.initialize_clients()
        
        mock_tx_data = {
            'address': '0x123',
            'network': 'eth-mainnet',
            'transactions': [{'hash': '0xabc', 'value': '1.0'}],
            'total_count': 1
        }
        
        with patch.object(manager.clients['alchemy'], 'get_transaction_history', new_callable=AsyncMock) as mock_method:
            mock_method.return_value = mock_tx_data
            
            result = await manager.get_transaction_history(['0x123'], ['eth-mainnet'])
            
            assert result == mock_tx_data
            mock_method.assert_called_once_with(['0x123'], ['eth-mainnet'])
    
    @pytest.mark.asyncio
    async def test_alchemy_health_status(self, manager, mock_settings):
        """Test Alchemy health status method"""
        await manager.initialize_clients()
        
        mock_health_data = {
            'status': 'healthy',
            'api_key_configured': True,
            'supported_networks': 6,
            'timestamp': '2024-01-01T00:00:00'
        }
        
        with patch.object(manager.clients['alchemy'], 'health_check', new_callable=AsyncMock) as mock_method:
            mock_method.return_value = mock_health_data
            
            result = await manager.get_alchemy_health_status()
            
            assert result == mock_health_data
            mock_method.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_alchemy_health_status_not_configured(self, mock_settings):
        """Test Alchemy health status when not configured"""
        # Disable Alchemy
        mock_settings.blockchain_data.alchemy_api_key = ""
        
        manager = BlockchainDataManager()
        await manager.initialize_clients()
        
        result = await manager.get_alchemy_health_status()
        
        assert result['status'] == 'not_configured'
        assert 'Alchemy client not initialized' in result['message']
    
    @pytest.mark.asyncio
    async def test_wallet_portfolio_without_alchemy(self, mock_settings):
        """Test wallet portfolio method when Alchemy is not available"""
        # Disable Alchemy
        mock_settings.blockchain_data.alchemy_api_key = ""
        
        manager = BlockchainDataManager()
        await manager.initialize_clients()
        
        result = await manager.get_gaming_wallet_portfolio(['0x123'])
        
        assert result == {}
    
    @pytest.mark.asyncio
    async def test_transaction_history_without_alchemy(self, mock_settings):
        """Test transaction history method when Alchemy is not available"""
        # Disable Alchemy
        mock_settings.blockchain_data.alchemy_api_key = ""
        
        manager = BlockchainDataManager()
        await manager.initialize_clients()
        
        result = await manager.get_transaction_history(['0x123'])
        
        assert result == {}
    
    @pytest.mark.asyncio
    async def test_caching_in_new_methods(self, manager, mock_settings):
        """Test caching functionality in new Alchemy methods"""
        await manager.initialize_clients()
        
        mock_portfolio_data = {'test': 'data'}
        
        with patch.object(manager.clients['alchemy'], 'get_gaming_wallet_portfolio', new_callable=AsyncMock) as mock_method:
            mock_method.return_value = mock_portfolio_data
            
            # First call should hit the API
            result1 = await manager.get_gaming_wallet_portfolio(['0x123'])
            assert result1 == mock_portfolio_data
            assert mock_method.call_count == 1
            
            # Second call should use cache
            result2 = await manager.get_gaming_wallet_portfolio(['0x123'])
            assert result2 == mock_portfolio_data
            assert mock_method.call_count == 1  # No additional API call
    
    @pytest.mark.asyncio
    async def test_error_handling_in_new_methods(self, manager, mock_settings):
        """Test error handling in new Alchemy methods"""
        await manager.initialize_clients()
        
        with patch.object(manager.clients['alchemy'], 'get_gaming_wallet_portfolio', new_callable=AsyncMock) as mock_method:
            mock_method.side_effect = Exception("API Error")
            
            result = await manager.get_gaming_wallet_portfolio(['0x123'])
            
            assert result == {}
    
    @pytest.mark.asyncio
    async def test_client_status_with_alchemy(self, manager, mock_settings):
        """Test client status includes Alchemy"""
        await manager.initialize_clients()
        
        with patch.object(manager, 'test_all_connections', new_callable=AsyncMock) as mock_test:
            mock_test.return_value = {'alchemy': True}
            
            status = await manager.get_client_status()
            
            assert 'alchemy' in status['initialized_clients']
            assert status['total_clients'] == 1
            assert status['connection_status']['alchemy'] is True
            assert status['healthy_clients'] == 1
    
    @pytest.mark.asyncio
    async def test_comprehensive_gaming_data_with_alchemy(self, manager, mock_settings):
        """Test comprehensive gaming data collection with Alchemy"""
        await manager.initialize_clients()
        
        # Mock gaming configuration
        with patch('blockchain.data_clients.manager.gaming_project_manager') as mock_gpm:
            mock_gpm.get_enabled_projects.return_value = {}
            mock_gpm.get_live_projects.return_value = {'axie-infinity': {}}
            mock_gpm.get_all_token_symbols.return_value = ['AXS', 'SLP']
            mock_gpm.get_project_summary.return_value = {'total_projects': 1}
            
            # Mock Alchemy client methods
            with patch.object(manager.clients['alchemy'], 'get_gaming_tokens_data', new_callable=AsyncMock) as mock_tokens:
                mock_tokens.return_value = []
                
                with patch.object(manager.clients['alchemy'], 'get_gaming_protocol_metrics', new_callable=AsyncMock) as mock_metrics:
                    mock_metrics.return_value = {'protocol': 'axie-infinity'}
                    
                    with patch.object(manager, 'get_coingecko_gaming_data', new_callable=AsyncMock) as mock_coingecko:
                        mock_coingecko.return_value = {}
                        
                        result = await manager.get_comprehensive_gaming_data()
                        
                        assert 'gaming_tokens' in result
                        assert 'protocol_metrics' in result
                        assert 'data_sources' in result
                        assert 'alchemy' in result['data_sources']


if __name__ == "__main__":
    pytest.main([__file__])
