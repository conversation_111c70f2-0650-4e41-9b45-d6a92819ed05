"""
Tests for Alchemy Phase 3 Enhanced Gaming Features
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timezone
from typing import Dict, List, Any

from blockchain.data_clients.alchemy import (
    AlchemyClient, 
    GamingAsset, 
    GamingPortfolio, 
    NFTCollectionMetrics, 
    CrossCollectionAnalysis
)
from blockchain.data_clients.manager import BlockchainDataManager


class TestGamingWalletPortfolio:
    """Test gaming wallet portfolio functionality"""
    
    @pytest.fixture
    def alchemy_client(self):
        """Create Alchemy client for testing"""
        with patch('blockchain.data_clients.alchemy.get_settings') as mock_settings:
            mock_settings.return_value.blockchain_data.alchemy_api_key = "test_key"
            mock_settings.return_value.blockchain_data.alchemy_base_url = "https://api.alchemy.com"
            mock_settings.return_value.blockchain_data.rate_limit_per_minute = 60
            mock_settings.return_value.blockchain_data.alchemy_supported_networks = [
                "eth-mainnet", "polygon-mainnet", "arbitrum-mainnet"
            ]
            return AlchemyClient()
    
    @pytest.mark.asyncio
    async def test_get_gaming_wallet_portfolio(self, alchemy_client):
        """Test enhanced gaming wallet portfolio retrieval"""
        test_addresses = ["******************************************"]
        test_networks = ["eth-mainnet", "polygon-mainnet"]
        
        # Mock the internal methods
        mock_portfolio = GamingPortfolio(
            wallet_address=test_addresses[0],
            total_value_usd=1000.0,
            gaming_value_usd=750.0,
            gaming_percentage=75.0,
            networks=test_networks,
            assets=[
                GamingAsset(
                    token_address="0xtoken1",
                    symbol="AXS",
                    name="Axie Infinity",
                    balance="100",
                    balance_usd=500.0,
                    price_usd=5.0,
                    network="eth-mainnet",
                    is_gaming_token=True,
                    gaming_project="Axie Infinity"
                )
            ],
            gaming_projects=["Axie Infinity"],
            nft_collections=["0xnft1"],
            last_updated=datetime.now(timezone.utc)
        )
        
        with patch.object(alchemy_client, '_get_single_wallet_portfolio', new_callable=AsyncMock) as mock_method:
            mock_method.return_value = mock_portfolio
            
            result = await alchemy_client.get_gaming_wallet_portfolio(test_addresses, test_networks)
            
            assert result is not None
            assert 'portfolios' in result
            assert 'summary' in result
            assert len(result['portfolios']) == 1
            
            portfolio = result['portfolios'][0]
            assert portfolio.wallet_address == test_addresses[0]
            assert portfolio.gaming_percentage == 75.0
            assert len(portfolio.gaming_projects) == 1
    
    @pytest.mark.asyncio
    async def test_identify_gaming_token(self, alchemy_client):
        """Test gaming token identification"""
        # Test known gaming token
        is_gaming, project = await alchemy_client._identify_gaming_token("0xtoken1", "AXS")
        assert is_gaming is True or is_gaming is False  # Depends on config
        
        # Test non-gaming token
        is_gaming, project = await alchemy_client._identify_gaming_token("0xtoken2", "USDC")
        assert is_gaming is False
    
    @pytest.mark.asyncio
    async def test_portfolio_summary_calculation(self, alchemy_client):
        """Test portfolio summary calculation"""
        portfolios = [
            GamingPortfolio(
                wallet_address="0x1",
                total_value_usd=1000.0,
                gaming_value_usd=750.0,
                gaming_percentage=75.0,
                networks=["eth-mainnet"],
                assets=[],
                gaming_projects=["Axie Infinity"],
                nft_collections=["0xnft1"],
                last_updated=datetime.now(timezone.utc)
            ),
            GamingPortfolio(
                wallet_address="0x2",
                total_value_usd=500.0,
                gaming_value_usd=250.0,
                gaming_percentage=50.0,
                networks=["polygon-mainnet"],
                assets=[],
                gaming_projects=["Star Atlas"],
                nft_collections=["0xnft2"],
                last_updated=datetime.now(timezone.utc)
            )
        ]
        
        summary = alchemy_client._calculate_portfolio_summary(portfolios)
        
        assert summary['total_wallets'] == 2
        assert summary['total_value_usd'] == 1500.0
        assert summary['total_gaming_value_usd'] == 1000.0
        assert summary['gaming_percentage'] == pytest.approx(66.67, rel=1e-2)
        assert summary['unique_gaming_projects'] == 2
        assert summary['unique_nft_collections'] == 2


class TestNFTIntelligence:
    """Test NFT intelligence functionality"""
    
    @pytest.fixture
    def alchemy_client(self):
        """Create Alchemy client for testing"""
        with patch('blockchain.data_clients.alchemy.get_settings') as mock_settings:
            mock_settings.return_value.blockchain_data.alchemy_api_key = "test_key"
            mock_settings.return_value.blockchain_data.alchemy_base_url = "https://api.alchemy.com"
            mock_settings.return_value.blockchain_data.rate_limit_per_minute = 60
            mock_settings.return_value.blockchain_data.alchemy_supported_networks = [
                "eth-mainnet", "polygon-mainnet"
            ]
            return AlchemyClient()
    
    @pytest.mark.asyncio
    async def test_nft_collection_analysis(self, alchemy_client):
        """Test NFT collection analysis"""
        collection_address = "******************************************"
        network = "eth-mainnet"
        
        # Mock the internal methods
        mock_metadata = {"name": "Test Collection", "description": "Test NFT collection"}
        mock_stats = {
            "totalSupply": 10000,
            "floorPrice": 0.1,
            "floorPriceUsd": 200.0,
            "volume24h": 1000.0,
            "holdersCount": 5000
        }
        mock_rarity = {
            "rarity_scores": {"1": 10.5, "2": 8.3},
            "trait_distribution": {"Background": {"Blue": 100, "Red": 50}}
        }
        
        with patch.object(alchemy_client, '_get_nft_collection_metadata', new_callable=AsyncMock) as mock_metadata_method, \
             patch.object(alchemy_client, '_get_nft_collection_stats', new_callable=AsyncMock) as mock_stats_method, \
             patch.object(alchemy_client, '_analyze_nft_rarity', new_callable=AsyncMock) as mock_rarity_method, \
             patch.object(alchemy_client, '_identify_gaming_nft', new_callable=AsyncMock) as mock_gaming_method:
            
            mock_metadata_method.return_value = mock_metadata
            mock_stats_method.return_value = mock_stats
            mock_rarity_method.return_value = mock_rarity
            mock_gaming_method.return_value = (True, "Test Game")
            
            result = await alchemy_client.get_gaming_nft_collection_analysis(collection_address, network)
            
            assert result is not None
            assert result.collection_address == collection_address
            assert result.collection_name == "Test Collection"
            assert result.total_supply == 10000
            assert result.floor_price == 0.1
            assert result.gaming_project == "Test Game"
            assert len(result.rarity_scores) == 2
    
    @pytest.mark.asyncio
    async def test_cross_collection_holder_analysis(self, alchemy_client):
        """Test cross-collection holder analysis"""
        collection_addresses = [
            "******************************************",
            "******************************************"
        ]
        network = "eth-mainnet"
        
        # Mock holder data
        mock_holders_1 = {"0xholder1": 5, "0xholder2": 3, "0xholder3": 1}
        mock_holders_2 = {"0xholder1": 2, "0xholder4": 7}
        
        with patch.object(alchemy_client, '_get_collection_holders', new_callable=AsyncMock) as mock_holders_method:
            mock_holders_method.side_effect = [mock_holders_1, mock_holders_2]
            
            result = await alchemy_client.get_cross_collection_holder_analysis(collection_addresses, network)
            
            assert len(result) > 0
            
            # Check for cross-collection holder
            cross_holder = next((h for h in result if h.wallet_address == "0xholder1"), None)
            assert cross_holder is not None
            assert len(cross_holder.collections_held) == 2
            assert cross_holder.total_nfts == 7
            assert cross_holder.whale_score > 0
    
    def test_determine_nft_utility_type(self, alchemy_client):
        """Test NFT utility type determination"""
        # Test character NFT
        metadata = {"name": "Hero Character", "description": "A powerful character for battle"}
        utility_type = alchemy_client._determine_nft_utility_type(metadata, "Test Game")
        assert utility_type == "character"
        
        # Test land NFT
        metadata = {"name": "Virtual Land", "description": "A plot of land in the metaverse"}
        utility_type = alchemy_client._determine_nft_utility_type(metadata, "Test Game")
        assert utility_type == "land"
        
        # Test equipment NFT
        metadata = {"name": "Magic Sword", "description": "A legendary weapon"}
        utility_type = alchemy_client._determine_nft_utility_type(metadata, "Test Game")
        assert utility_type == "equipment"
        
        # Test non-gaming NFT
        metadata = {"name": "Art Piece", "description": "Beautiful digital art"}
        utility_type = alchemy_client._determine_nft_utility_type(metadata, None)
        assert utility_type == "collectible"


class TestEnhancedProtocolMetrics:
    """Test enhanced gaming protocol metrics"""
    
    @pytest.fixture
    def alchemy_client(self):
        """Create Alchemy client for testing"""
        with patch('blockchain.data_clients.alchemy.get_settings') as mock_settings:
            mock_settings.return_value.blockchain_data.alchemy_api_key = "test_key"
            mock_settings.return_value.blockchain_data.alchemy_base_url = "https://api.alchemy.com"
            mock_settings.return_value.blockchain_data.rate_limit_per_minute = 60
            mock_settings.return_value.blockchain_data.alchemy_supported_networks = [
                "eth-mainnet", "polygon-mainnet"
            ]
            return AlchemyClient()
    
    @pytest.mark.asyncio
    async def test_enhanced_gaming_protocol_metrics(self, alchemy_client):
        """Test enhanced gaming protocol metrics collection"""
        protocol_name = "axie-infinity"
        networks = ["eth-mainnet", "polygon-mainnet"]
        
        # Mock project configuration
        mock_project = MagicMock()
        mock_project.tokens = [MagicMock(contract_address="0xtoken1", symbol="AXS")]
        mock_project.nfts = [MagicMock(contract_address="0xnft1", name="Axie")]
        
        mock_network_metrics = {
            'network': 'eth-mainnet',
            'transactions_24h': 1000,
            'unique_users_24h': 500,
            'volume_24h_usd': 50000.0,
            'gas_used_24h': 1000000,
            'token_transfers': {'AXS': {'transfer_count_24h': 800}},
            'nft_activity': {'Axie': {'transfer_count_24h': 200}}
        }
        
        with patch('blockchain.data_clients.alchemy.gaming_project_manager') as mock_manager, \
             patch.object(alchemy_client, '_get_network_protocol_metrics', new_callable=AsyncMock) as mock_network_method:
            
            mock_manager.projects = {protocol_name: mock_project}
            mock_network_method.return_value = mock_network_metrics
            
            result = await alchemy_client.get_enhanced_gaming_protocol_metrics(protocol_name, networks)
            
            assert result is not None
            assert result['protocol_name'] == protocol_name
            assert 'multi_chain_metrics' in result
            assert 'aggregated_metrics' in result
            assert 'cross_chain_insights' in result
            
            aggregated = result['aggregated_metrics']
            assert aggregated['total_transactions_24h'] >= 1000
            assert len(aggregated['active_networks']) > 0
    
    def test_cross_chain_insights_calculation(self, alchemy_client):
        """Test cross-chain insights calculation"""
        multi_chain_metrics = {
            'eth-mainnet': {
                'transactions_24h': 1000,
                'gas_used_24h': 1000000
            },
            'polygon-mainnet': {
                'transactions_24h': 500,
                'gas_used_24h': 100000
            }
        }
        
        insights = alchemy_client._calculate_cross_chain_insights(multi_chain_metrics)
        
        assert 'network_distribution' in insights
        assert 'dominant_network' in insights
        assert 'network_efficiency' in insights
        
        # Check network distribution
        assert insights['network_distribution']['eth-mainnet'] == pytest.approx(66.67, rel=1e-2)
        assert insights['network_distribution']['polygon-mainnet'] == pytest.approx(33.33, rel=1e-2)
        
        # Check dominant network
        assert insights['dominant_network'] == 'eth-mainnet'
        
        # Check network efficiency
        assert insights['network_efficiency']['polygon-mainnet'] > insights['network_efficiency']['eth-mainnet']


class TestBlockchainDataManagerIntegration:
    """Test integration with BlockchainDataManager"""
    
    @pytest.mark.asyncio
    async def test_manager_gaming_wallet_portfolio(self):
        """Test manager integration for gaming wallet portfolio"""
        manager = BlockchainDataManager()
        
        # Mock Alchemy client
        mock_client = AsyncMock()
        mock_portfolio_data = {
            'portfolios': [],
            'summary': {'total_wallets': 1},
            'timestamp': datetime.now().isoformat()
        }
        mock_client.get_gaming_wallet_portfolio.return_value = mock_portfolio_data
        
        with patch.object(manager, 'clients', {'alchemy': mock_client}):
            result = await manager.get_gaming_wallet_portfolio(['0x123'])
            
            assert result == mock_portfolio_data
            mock_client.get_gaming_wallet_portfolio.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_manager_nft_collection_analysis(self):
        """Test manager integration for NFT collection analysis"""
        manager = BlockchainDataManager()
        
        # Mock Alchemy client
        mock_client = AsyncMock()
        mock_analysis = NFTCollectionMetrics(
            collection_address="0x123",
            collection_name="Test Collection",
            network="eth-mainnet",
            total_supply=1000,
            floor_price=0.1,
            floor_price_usd=200.0,
            volume_24h=1000.0,
            holders_count=500,
            rarity_scores={},
            trait_distribution={},
            gaming_project="Test Game",
            utility_type="character"
        )
        mock_client.get_gaming_nft_collection_analysis.return_value = mock_analysis
        
        with patch.object(manager, 'clients', {'alchemy': mock_client}):
            result = await manager.get_gaming_nft_collection_analysis('0x123')
            
            assert result['collection_address'] == '0x123'
            assert result['gaming_project'] == 'Test Game'
            mock_client.get_gaming_nft_collection_analysis.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
