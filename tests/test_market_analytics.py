"""
Unit tests for Market Analytics services
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta
import json

from services.market_analytics import (
    GamingSectorAnalyzer,
    InvestmentTracker,
    MarketAlertSystem,
    gaming_sector_analyzer,
    investment_tracker,
    market_alert_system
)
from services.market_analytics import (
    MarketAlert,
    AlertType,
    AlertSeverity,
    InvestmentSignal,
    RiskLevel
)


class TestGamingSectorAnalyzer:
    """Test gaming sector analysis functionality"""
    
    def test_initialization(self):
        """Test GamingSectorAnalyzer initialization"""
        analyzer = GamingSectorAnalyzer()
        assert analyzer is not None
        assert hasattr(analyzer, 'gaming_projects')
        assert hasattr(analyzer, 'sector_categories')
    
    @pytest.mark.asyncio
    async def test_cross_protocol_performance_analysis(self):
        """Test cross-protocol performance analysis"""
        analyzer = GamingSectorAnalyzer()
        
        # Mock market data
        mock_market_data = {
            'axie-infinity': {
                'price_change_24h': 0.05,
                'volume_change_24h': 0.15,
                'market_cap': 1000000000,
                'sentiment_score': 0.7
            },
            'star-atlas': {
                'price_change_24h': -0.02,
                'volume_change_24h': -0.05,
                'market_cap': 500000000,
                'sentiment_score': 0.6
            }
        }
        
        with patch.object(analyzer, '_fetch_market_data', return_value=mock_market_data):
            result = await analyzer.analyze_cross_protocol_performance("7d")
            
            assert 'timeframe' in result
            assert 'correlations' in result
            assert 'sector_performance' in result
            assert 'leading_indicators' in result
            assert 'cross_chain_analysis' in result
            
            # Verify correlation matrix structure
            correlations = result['correlations']['correlation_matrix']
            assert 'axie-infinity' in correlations
            assert 'star-atlas' in correlations
    
    @pytest.mark.asyncio
    async def test_sector_performance_calculation(self):
        """Test sector performance calculation"""
        analyzer = GamingSectorAnalyzer()
        
        mock_projects = {
            'axie-infinity': {'category': 'p2e', 'market_cap': 1000000000, 'price_change_24h': 0.05},
            'decentraland': {'category': 'metaverse', 'market_cap': 800000000, 'price_change_24h': 0.03},
            'the-sandbox': {'category': 'metaverse', 'market_cap': 600000000, 'price_change_24h': 0.07}
        }
        
        sector_performance = analyzer._calculate_sector_performance(mock_projects)
        
        assert 'p2e' in sector_performance
        assert 'metaverse' in sector_performance
        
        # Verify metaverse sector has multiple projects
        metaverse_sector = sector_performance['metaverse']
        assert len(metaverse_sector['projects']) == 2
        assert metaverse_sector['total_market_cap'] == 1400000000
    
    def test_correlation_calculation(self):
        """Test correlation calculation between projects"""
        analyzer = GamingSectorAnalyzer()
        
        # Mock price data for correlation calculation
        price_data = {
            'project_a': [1.0, 1.1, 1.05, 1.15, 1.2],
            'project_b': [2.0, 2.2, 2.1, 2.3, 2.4],  # Positively correlated
            'project_c': [3.0, 2.8, 2.9, 2.7, 2.6]   # Negatively correlated
        }
        
        correlation_matrix = analyzer._calculate_correlations(price_data)
        
        assert 'project_a' in correlation_matrix
        assert 'project_b' in correlation_matrix
        assert 'project_c' in correlation_matrix
        
        # Verify positive correlation between A and B
        assert correlation_matrix['project_a']['project_b'] > 0.5
        
        # Verify negative correlation between A and C
        assert correlation_matrix['project_a']['project_c'] < -0.5


class TestInvestmentTracker:
    """Test investment tracking functionality"""
    
    def test_initialization(self):
        """Test InvestmentTracker initialization"""
        tracker = InvestmentTracker()
        assert tracker is not None
        assert hasattr(tracker, 'risk_weights')
        assert hasattr(tracker, 'signal_thresholds')
    
    @pytest.mark.asyncio
    async def test_portfolio_tracking(self, sample_portfolio):
        """Test portfolio tracking functionality"""
        tracker = InvestmentTracker()
        
        # Mock current prices and metrics
        mock_price_data = {
            'axie-infinity': {
                'current_price': 10.50,
                'price_change_24h': 0.05,
                'volume_24h': 25000000,
                'market_cap': 1000000000,
                'sentiment_score': 0.7
            },
            'star-atlas': {
                'current_price': 0.25,
                'price_change_24h': -0.02,
                'volume_24h': 5000000,
                'market_cap': 500000000,
                'sentiment_score': 0.6
            }
        }
        
        with patch.object(tracker, '_fetch_current_prices', return_value=mock_price_data):
            result = await tracker.track_gaming_portfolio(sample_portfolio)
            
            assert 'portfolio_value' in result
            assert 'change_24h' in result
            assert 'change_24h_pct' in result
            assert 'risk_assessment' in result
            assert 'positions' in result
            assert 'recommendations' in result
            
            # Verify portfolio value calculation
            assert result['portfolio_value'] > 0
            assert isinstance(result['change_24h'], (int, float))
    
    def test_risk_assessment(self):
        """Test portfolio risk assessment"""
        tracker = InvestmentTracker()
        
        # High risk portfolio (concentrated in one asset)
        high_risk_portfolio = {'axie-infinity': 20000}  # All in one project
        risk_assessment = tracker._assess_portfolio_risk(high_risk_portfolio, {})
        
        assert risk_assessment['overall_risk'] > 0.7
        assert risk_assessment['risk_level'] in ['High', 'Very High']
        assert 'concentration risk' in ' '.join(risk_assessment['risk_factors']).lower()
        
        # Diversified portfolio
        diversified_portfolio = {
            'axie-infinity': 5000,
            'star-atlas': 5000,
            'decentraland': 5000,
            'gala-games': 5000
        }
        risk_assessment = tracker._assess_portfolio_risk(diversified_portfolio, {})
        
        assert risk_assessment['overall_risk'] < 0.5
        assert risk_assessment['risk_level'] in ['Low', 'Medium']
    
    def test_investment_signal_generation(self):
        """Test investment signal generation"""
        tracker = InvestmentTracker()
        
        # Strong positive metrics should generate buy signal
        positive_metrics = {
            'price_change_24h': 0.15,  # 15% gain
            'volume_change_24h': 0.25,  # 25% volume increase
            'sentiment_score': 0.8,     # Very positive sentiment
            'developer_activity': 90    # High development activity
        }
        
        signal = tracker._generate_investment_signal(positive_metrics)
        assert signal in [InvestmentSignal.BUY, InvestmentSignal.STRONG_BUY]
        
        # Strong negative metrics should generate sell signal
        negative_metrics = {
            'price_change_24h': -0.20,  # 20% loss
            'volume_change_24h': -0.30,  # 30% volume decrease
            'sentiment_score': 0.2,      # Very negative sentiment
            'developer_activity': 10     # Low development activity
        }
        
        signal = tracker._generate_investment_signal(negative_metrics)
        assert signal in [InvestmentSignal.SELL, InvestmentSignal.STRONG_SELL]
    
    def test_diversification_scoring(self):
        """Test portfolio diversification scoring"""
        tracker = InvestmentTracker()
        
        # Well-diversified portfolio
        diversified_portfolio = {
            'axie-infinity': 2500,    # P2E
            'decentraland': 2500,     # Metaverse
            'gala-games': 2500,       # Gaming Platform
            'star-atlas': 2500        # Metaverse/Space
        }
        
        diversification_score = tracker._calculate_diversification_score(diversified_portfolio)
        assert diversification_score > 0.7
        
        # Concentrated portfolio
        concentrated_portfolio = {
            'axie-infinity': 9000,
            'star-atlas': 1000
        }
        
        diversification_score = tracker._calculate_diversification_score(concentrated_portfolio)
        assert diversification_score < 0.3


class TestMarketAlertSystem:
    """Test market alert system functionality"""
    
    def test_initialization(self):
        """Test MarketAlertSystem initialization"""
        alert_system = MarketAlertSystem()
        assert alert_system is not None
        assert hasattr(alert_system, 'alert_thresholds')
        assert hasattr(alert_system, 'active_alerts')
    
    @pytest.mark.asyncio
    async def test_price_movement_alerts(self):
        """Test price movement alert generation"""
        alert_system = MarketAlertSystem()
        
        # Mock current and historical prices
        mock_current_data = {
            'axie-infinity': {
                'price': 12.0,
                'price_change_24h': 0.20,  # 20% increase - should trigger alert
                'volume_24h': 30000000
            }
        }
        
        mock_historical_data = {
            'axie-infinity': {
                'price_24h_ago': 10.0,
                'avg_volume_7d': 20000000
            }
        }
        
        with patch.object(alert_system, '_fetch_current_market_data', return_value=mock_current_data):
            with patch.object(alert_system, '_fetch_historical_data', return_value=mock_historical_data):
                alerts = await alert_system.monitor_market_conditions(['axie-infinity'])
                
                # Should generate price surge alert
                price_alerts = [alert for alert in alerts if alert.alert_type == AlertType.PRICE_SURGE]
                assert len(price_alerts) > 0
                
                price_alert = price_alerts[0]
                assert price_alert.project_name == 'Axie Infinity'
                assert price_alert.severity in [AlertSeverity.MEDIUM, AlertSeverity.HIGH]
    
    @pytest.mark.asyncio
    async def test_volume_spike_alerts(self):
        """Test volume spike alert generation"""
        alert_system = MarketAlertSystem()
        
        mock_current_data = {
            'star-atlas': {
                'price': 0.25,
                'price_change_24h': 0.05,
                'volume_24h': 50000000  # High volume
            }
        }
        
        mock_historical_data = {
            'star-atlas': {
                'avg_volume_7d': 10000000  # Much lower average
            }
        }
        
        with patch.object(alert_system, '_fetch_current_market_data', return_value=mock_current_data):
            with patch.object(alert_system, '_fetch_historical_data', return_value=mock_historical_data):
                alerts = await alert_system.monitor_market_conditions(['star-atlas'])
                
                # Should generate volume spike alert
                volume_alerts = [alert for alert in alerts if alert.alert_type == AlertType.VOLUME_SPIKE]
                assert len(volume_alerts) > 0
    
    @pytest.mark.asyncio
    async def test_sentiment_shift_alerts(self):
        """Test sentiment shift alert generation"""
        alert_system = MarketAlertSystem()
        
        mock_sentiment_data = {
            'decentraland': {
                'current_sentiment': 0.2,  # Very negative
                'sentiment_change_24h': -0.6,  # Large negative shift
                'sentiment_sources': ['news', 'social_media']
            }
        }
        
        with patch.object(alert_system, '_fetch_sentiment_data', return_value=mock_sentiment_data):
            alerts = await alert_system.monitor_market_conditions(['decentraland'])
            
            # Should generate sentiment shift alert
            sentiment_alerts = [alert for alert in alerts if alert.alert_type == AlertType.NEWS_SENTIMENT]
            assert len(sentiment_alerts) > 0
            
            sentiment_alert = sentiment_alerts[0]
            assert 'negative sentiment' in sentiment_alert.message.lower()
    
    def test_alert_severity_calculation(self):
        """Test alert severity calculation"""
        alert_system = MarketAlertSystem()
        
        # High severity scenario
        high_severity_metrics = {
            'price_change_magnitude': 0.30,  # 30% change
            'volume_spike_ratio': 5.0,       # 5x normal volume
            'sentiment_shift': -0.8          # Large negative shift
        }
        
        severity = alert_system._calculate_alert_severity(high_severity_metrics)
        assert severity in [AlertSeverity.HIGH, AlertSeverity.CRITICAL]
        
        # Low severity scenario
        low_severity_metrics = {
            'price_change_magnitude': 0.05,  # 5% change
            'volume_spike_ratio': 1.2,       # 20% above normal
            'sentiment_shift': -0.1          # Small shift
        }
        
        severity = alert_system._calculate_alert_severity(low_severity_metrics)
        assert severity in [AlertSeverity.LOW, AlertSeverity.MEDIUM]
    
    def test_alert_management(self):
        """Test alert management functionality"""
        alert_system = MarketAlertSystem()
        
        # Create test alert
        test_alert = MarketAlert(
            alert_type=AlertType.PRICE_SURGE,
            project_name="Test Project",
            message="Test alert message",
            severity=AlertSeverity.MEDIUM,
            timestamp=datetime.now(),
            metadata={'price_change': 0.15}
        )
        
        # Add alert
        alert_system.add_alert(test_alert)
        assert len(alert_system.active_alerts) == 1
        
        # Get alert summary
        summary = alert_system.get_alert_summary()
        assert summary['total_active_alerts'] == 1
        assert summary['severity_breakdown']['medium'] == 1
        assert summary['alert_type_breakdown']['price_surge'] == 1
        
        # Clear old alerts
        old_alert = MarketAlert(
            alert_type=AlertType.PRICE_DROP,
            project_name="Old Project",
            message="Old alert",
            severity=AlertSeverity.LOW,
            timestamp=datetime.now() - timedelta(hours=25),  # Older than 24 hours
            metadata={}
        )
        
        alert_system.add_alert(old_alert)
        alert_system.clear_old_alerts(hours=24)
        
        # Should only have the recent alert
        assert len(alert_system.active_alerts) == 1
        assert alert_system.active_alerts[0].project_name == "Test Project"


class TestIntegrationScenarios:
    """Test integration scenarios between market analytics components"""
    
    @pytest.mark.asyncio
    async def test_full_market_monitoring_pipeline(self, sample_portfolio):
        """Test full market monitoring pipeline"""
        # Initialize all components
        sector_analyzer = gaming_sector_analyzer
        investment_tracker_instance = investment_tracker
        alert_system = market_alert_system
        
        # Mock data for integration test
        mock_market_data = {
            'axie-infinity': {
                'price': 11.0,
                'price_change_24h': 0.10,
                'volume_24h': 25000000,
                'market_cap': 1100000000,
                'sentiment_score': 0.75
            }
        }
        
        with patch.object(sector_analyzer, '_fetch_market_data', return_value=mock_market_data):
            with patch.object(investment_tracker_instance, '_fetch_current_prices', return_value=mock_market_data):
                with patch.object(alert_system, '_fetch_current_market_data', return_value=mock_market_data):
                    
                    # Step 1: Analyze sector performance
                    sector_analysis = await sector_analyzer.analyze_cross_protocol_performance("24h")
                    assert sector_analysis is not None
                    
                    # Step 2: Track portfolio performance
                    portfolio_result = await investment_tracker_instance.track_gaming_portfolio(sample_portfolio)
                    assert portfolio_result['portfolio_value'] > 0
                    
                    # Step 3: Monitor for alerts
                    alerts = await alert_system.monitor_market_conditions(['axie-infinity'])
                    # May or may not generate alerts depending on thresholds
                    assert isinstance(alerts, list)
    
    @pytest.mark.asyncio
    async def test_risk_correlation_analysis(self):
        """Test risk correlation between different analytics components"""
        sector_analyzer = GamingSectorAnalyzer()
        investment_tracker_instance = InvestmentTracker()
        
        # Mock correlated market data (all projects moving together)
        correlated_data = {
            'axie-infinity': {'price_change_24h': 0.15, 'sentiment_score': 0.8},
            'star-atlas': {'price_change_24h': 0.14, 'sentiment_score': 0.75},
            'decentraland': {'price_change_24h': 0.13, 'sentiment_score': 0.78}
        }
        
        with patch.object(sector_analyzer, '_fetch_market_data', return_value=correlated_data):
            sector_analysis = await sector_analyzer.analyze_cross_protocol_performance("24h")
            
            # High correlation should be detected
            correlations = sector_analysis['correlations']['correlation_matrix']
            avg_correlation = sector_analysis['correlations']['average_correlation']
            assert avg_correlation > 0.7  # High correlation
        
        # Portfolio risk should reflect high correlation
        correlated_portfolio = {'axie-infinity': 5000, 'star-atlas': 5000, 'decentraland': 5000}
        
        with patch.object(investment_tracker_instance, '_fetch_current_prices', return_value=correlated_data):
            portfolio_result = await investment_tracker_instance.track_gaming_portfolio(correlated_portfolio)
            
            # Risk should be elevated due to correlation
            risk_assessment = portfolio_result['risk_assessment']
            # Even though diversified by project, correlation increases risk
            assert risk_assessment['overall_risk'] > 0.4
