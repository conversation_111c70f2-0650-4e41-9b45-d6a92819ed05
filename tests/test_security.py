"""
Security Testing Suite for Web3 Gaming News Tracker
Tests authentication, rate limiting, input validation, and security headers
"""

import pytest
import asyncio
import json
import time
from typing import Dict, List
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from api.main import app
from api.security_middleware import SecurityMiddleware, RateLimiter, InputValidator, APIKeyValidator
from config.settings import get_settings

settings = get_settings()
client = TestClient(app)


class TestSecurityHeaders:
    """Test security headers implementation"""
    
    def test_security_headers_present(self):
        """Test that security headers are present in responses"""
        response = client.get("/health")
        
        # Check for security headers
        assert "X-Content-Type-Options" in response.headers
        assert response.headers["X-Content-Type-Options"] == "nosniff"
        
        assert "X-Frame-Options" in response.headers
        assert response.headers["X-Frame-Options"] == "DENY"
        
        assert "X-XSS-Protection" in response.headers
        assert response.headers["X-XSS-Protection"] == "1; mode=block"
        
        assert "Content-Security-Policy" in response.headers
        assert "default-src 'self'" in response.headers["Content-Security-Policy"]
        
        assert "Referrer-Policy" in response.headers
        assert response.headers["Referrer-Policy"] == "strict-origin-when-cross-origin"
    
    def test_cors_headers_configured(self):
        """Test CORS headers are properly configured"""
        response = client.options("/api/v1/articles/")
        
        assert "Access-Control-Allow-Origin" in response.headers
        assert "Access-Control-Allow-Methods" in response.headers
        assert "Access-Control-Allow-Headers" in response.headers


class TestRateLimiting:
    """Test rate limiting functionality"""
    
    def setUp(self):
        self.rate_limiter = RateLimiter()
    
    def test_rate_limit_headers(self):
        """Test rate limit headers are included"""
        response = client.get("/api/v1/articles/")
        
        assert "X-Rate-Limit-Limit" in response.headers
        assert "X-Rate-Limit-Remaining" in response.headers
        assert "X-Rate-Limit-Reset" in response.headers
        assert "X-Rate-Limit-Window" in response.headers
    
    def test_rate_limit_enforcement(self):
        """Test rate limiting enforcement"""
        # This test would require mocking the rate limiter
        # to simulate rate limit exceeded scenarios
        
        with patch.object(RateLimiter, 'check_rate_limit') as mock_check:
            mock_check.return_value = {
                "blocked": True,
                "reason": "Rate limit exceeded",
                "retry_after": 3600
            }
            
            response = client.get("/api/v1/articles/")
            assert response.status_code == 429
            assert "Rate limit exceeded" in response.json()["error"]
    
    def test_client_identification(self):
        """Test client identification for rate limiting"""
        rate_limiter = RateLimiter()
        
        # Mock request with API key
        mock_request = MagicMock()
        mock_request.headers.get.return_value = "test_api_key"
        
        client_id = rate_limiter.get_client_id(mock_request)
        assert client_id.startswith("api_key:")
        
        # Mock request without API key
        mock_request.headers.get.return_value = None
        mock_request.client.host = "***********"
        
        client_id = rate_limiter.get_client_id(mock_request)
        assert client_id.startswith("ip:")


class TestInputValidation:
    """Test input validation and sanitization"""
    
    def setUp(self):
        self.validator = InputValidator()
    
    def test_sql_injection_detection(self):
        """Test SQL injection pattern detection"""
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "UNION SELECT * FROM passwords",
            "admin'--",
            "1; DELETE FROM articles"
        ]
        
        for malicious_input in malicious_inputs:
            result = self.validator.validate_input(malicious_input, "test_field")
            assert not result["valid"]
            assert any(threat["type"] == "sql_injection" for threat in result["threats"])
    
    def test_xss_detection(self):
        """Test XSS attack detection"""
        malicious_inputs = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "<iframe src='javascript:alert(1)'></iframe>",
            "onclick='alert(1)'"
        ]
        
        for malicious_input in malicious_inputs:
            result = self.validator.validate_input(malicious_input, "test_field")
            assert not result["valid"]
            assert any(threat["type"] == "xss" for threat in result["threats"])
    
    def test_path_traversal_detection(self):
        """Test path traversal attack detection"""
        malicious_inputs = [
            "../../../etc/passwd",
            "..\\..\\windows\\system32",
            "....//....//etc/passwd"
        ]
        
        for malicious_input in malicious_inputs:
            result = self.validator.validate_input(malicious_input, "test_field")
            assert not result["valid"]
            assert any(threat["type"] == "path_traversal" for threat in result["threats"])
    
    def test_command_injection_detection(self):
        """Test command injection detection"""
        malicious_inputs = [
            "rm -rf /",
            "del C:\\Windows",
            "format C:",
            "shutdown -h now"
        ]
        
        for malicious_input in malicious_inputs:
            result = self.validator.validate_input(malicious_input, "test_field")
            assert not result["valid"]
            assert any(threat["type"] == "command_injection" for threat in result["threats"])
    
    def test_input_sanitization(self):
        """Test input sanitization functionality"""
        malicious_input = "<script>alert('xss')</script>Hello World"
        result = self.validator.validate_input(malicious_input, "test_field")
        
        sanitized = result["sanitized_data"]
        assert "<script>" not in sanitized
        assert "Hello World" in sanitized
    
    def test_nested_data_validation(self):
        """Test validation of nested data structures"""
        malicious_data = {
            "user": {
                "name": "John",
                "email": "'; DROP TABLE users; --"
            },
            "comments": [
                "Normal comment",
                "<script>alert('xss')</script>"
            ]
        }
        
        result = self.validator.validate_input(malicious_data, "request_data")
        assert not result["valid"]
        assert len(result["threats"]) >= 2  # SQL injection + XSS
    
    def test_valid_input_passes(self):
        """Test that valid input passes validation"""
        valid_inputs = [
            "Hello World",
            {"name": "John", "age": 30},
            ["item1", "item2", "item3"],
            "<EMAIL>",
            "https://example.com"
        ]
        
        for valid_input in valid_inputs:
            result = self.validator.validate_input(valid_input, "test_field")
            assert result["valid"]
            assert len(result["threats"]) == 0


class TestAPIKeyValidation:
    """Test API key validation system"""
    
    def setUp(self):
        self.api_validator = APIKeyValidator()
    
    def test_missing_api_key(self):
        """Test handling of missing API key"""
        result = self.api_validator.validate_api_key(None)
        assert not result["valid"]
        assert "No API key provided" in result["reason"]
        
        result = self.api_validator.validate_api_key("")
        assert not result["valid"]
        assert "No API key provided" in result["reason"]
    
    def test_invalid_api_key(self):
        """Test handling of invalid API key"""
        result = self.api_validator.validate_api_key("invalid_key_12345")
        assert not result["valid"]
        assert "Invalid API key" in result["reason"]
    
    def test_valid_api_key(self):
        """Test handling of valid API key"""
        # This test assumes development environment with dev key
        if settings.environment == "development":
            result = self.api_validator.validate_api_key("dev_key_12345")
            assert result["valid"]
            assert "permissions" in result
            assert "usage_count" in result


class TestSecurityMiddleware:
    """Test security middleware integration"""
    
    def test_public_endpoints_accessible(self):
        """Test that public endpoints are accessible without authentication"""
        public_endpoints = [
            "/health",
            "/stats",
            "/docs",
            "/redoc",
            "/openapi.json"
        ]
        
        for endpoint in public_endpoints:
            response = client.get(endpoint)
            # Should not be blocked by security middleware
            assert response.status_code != 401
            assert response.status_code != 429  # Assuming no rate limiting for public endpoints
    
    def test_protected_endpoints_require_auth(self):
        """Test that protected endpoints require authentication"""
        # This test would need to be adjusted based on actual protected endpoints
        # and authentication requirements
        
        protected_endpoints = [
            "/api/v1/content-intelligence/analyze",
            "/api/v1/market-analytics/trends"
        ]
        
        for endpoint in protected_endpoints:
            # Test without API key
            response = client.get(endpoint)
            # Should require authentication (if endpoint exists)
            if response.status_code != 404:  # Endpoint exists
                assert response.status_code in [401, 403]
    
    def test_malicious_request_blocked(self):
        """Test that malicious requests are blocked"""
        malicious_data = {
            "query": "'; DROP TABLE articles; --",
            "content": "<script>alert('xss')</script>"
        }
        
        response = client.post("/api/v1/articles/", json=malicious_data)
        
        # Should be blocked by input validation
        assert response.status_code == 400
        assert "Invalid input" in response.json().get("error", "")


class TestSecurityConfiguration:
    """Test security configuration and settings"""
    
    def test_security_settings_loaded(self):
        """Test that security settings are properly loaded"""
        assert hasattr(settings, 'security')
        assert hasattr(settings.security, 'enable_rate_limiting')
        assert hasattr(settings.security, 'enable_input_validation')
        assert hasattr(settings.security, 'enable_security_headers')
    
    def test_environment_based_security(self):
        """Test environment-based security configuration"""
        # Development environment should have relaxed settings
        if settings.environment == "development":
            # CORS should allow all origins in development
            response = client.options("/api/v1/articles/")
            assert response.headers.get("Access-Control-Allow-Origin") == "*"


class TestSecurityLogging:
    """Test security event logging"""
    
    def test_security_events_logged(self):
        """Test that security events are properly logged"""
        # This would require mocking the logging system
        # and verifying that security events are recorded
        
        with patch('api.security_middleware.log_security_event') as mock_log:
            # Trigger a security event (e.g., invalid API key)
            response = client.get("/api/v1/content-intelligence/test", 
                                headers={"X-API-Key": "invalid_key"})
            
            # Verify security event was logged
            mock_log.assert_called()


# Integration tests
class TestSecurityIntegration:
    """Integration tests for complete security system"""
    
    def test_complete_security_flow(self):
        """Test complete security validation flow"""
        # Test a request that goes through all security checks
        response = client.get("/api/v1/articles/", 
                            headers={"User-Agent": "SecurityTest/1.0"})
        
        # Should have security headers
        assert "X-Content-Type-Options" in response.headers
        assert "X-Rate-Limit-Limit" in response.headers
        
        # Should be successful for valid request
        assert response.status_code in [200, 404]  # 404 if endpoint doesn't exist
    
    def test_security_under_load(self):
        """Test security system under load"""
        # Simulate multiple requests to test rate limiting
        responses = []
        for i in range(10):
            response = client.get("/api/v1/articles/")
            responses.append(response)
        
        # All requests should have rate limit headers
        for response in responses:
            assert "X-Rate-Limit-Remaining" in response.headers


if __name__ == "__main__":
    # Run security tests
    pytest.main([__file__, "-v", "--tb=short"])
