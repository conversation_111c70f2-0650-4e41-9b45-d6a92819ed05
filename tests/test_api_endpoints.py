"""
Unit tests for API endpoints
"""
import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, AsyncMock, patch
import json

from api.main import app
from services.content_intelligence import ContentClassificationResult, GamingCategory
from services.market_analytics import MarketAlert, AlertType, AlertSeverity
from services.competitive_analysis import CompetitiveMetrics


class TestContentIntelligenceEndpoints:
    """Test content intelligence API endpoints"""
    
    def test_classify_content_endpoint(self, test_client):
        """Test content classification endpoint"""
        # Mock classification result
        mock_result = ContentClassificationResult(
            primary_category=GamingCategory.PLAY_TO_EARN,
            category_confidence=0.85,
            gaming_entities=['axie infinity'],
            blockchain_networks=['ethereum'],
            sentiment_score=0.7,
            key_themes=['breeding', 'battles', 'tokens']
        )
        
        with patch('api.content_intelligence_endpoints.gaming_content_classifier.classify_content', return_value=mock_result):
            response = test_client.post(
                "/api/content-intelligence/classify",
                json={
                    "title": "Axie Infinity Introduces New Breeding Mechanics",
                    "content": "Players can now breed Axies with enhanced genetic algorithms",
                    "summary": "New breeding system improves gameplay"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['primary_category'] == 'play-to-earn'
            assert data['category_confidence'] == 0.85
            assert 'axie infinity' in data['gaming_entities']
            assert 'ethereum' in data['blockchain_networks']
            assert data['sentiment_score'] == 0.7
    
    def test_analyze_sentiment_endpoint(self, test_client):
        """Test sentiment analysis endpoint"""
        mock_sentiment_result = {
            'overall_sentiment': 0.75,
            'gaming_sentiment': 0.8,
            'market_sentiment': 0.7,
            'community_sentiment': 0.75,
            'sentiment_category': 'positive',
            'key_sentiment_drivers': ['fun', 'profitable', 'engaging']
        }
        
        with patch('api.content_intelligence_endpoints.sentiment_scoring_engine.analyze_gaming_sentiment', return_value=mock_sentiment_result):
            response = test_client.post(
                "/api/content-intelligence/sentiment",
                json={
                    "text": "This game is absolutely amazing and so much fun to play!"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['overall_sentiment'] == 0.75
            assert data['gaming_sentiment'] == 0.8
            assert data['sentiment_category'] == 'positive'
            assert 'fun' in data['key_sentiment_drivers']
    
    def test_detect_trends_endpoint(self, test_client):
        """Test trend detection endpoint"""
        mock_trend_result = {
            'trend_scores': {
                'adoption_keywords': 0.8,
                'innovation_keywords': 0.7,
                'partnership_keywords': 0.6
            },
            'market_phase': {
                'dominant_phase': 'bull_market',
                'confidence': 0.75
            },
            'trend_momentum': 0.65,
            'emerging_themes': [
                {'theme': 'NFT Gaming', 'frequency': 15, 'relevance_score': 0.9},
                {'theme': 'Metaverse', 'frequency': 12, 'relevance_score': 0.8}
            ]
        }
        
        with patch('api.content_intelligence_endpoints.trend_detection_engine.detect_trends', return_value=mock_trend_result):
            response = test_client.post(
                "/api/content-intelligence/trends",
                json={
                    "content_batch": [
                        "NFT gaming market shows strong growth",
                        "Metaverse platforms gain popularity",
                        "Play-to-earn games attract new players"
                    ],
                    "timeframe": "24h"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['trend_scores']['adoption_keywords'] == 0.8
            assert data['market_phase']['dominant_phase'] == 'bull_market'
            assert len(data['emerging_themes']) == 2
    
    def test_recognize_entities_endpoint(self, test_client):
        """Test entity recognition endpoint"""
        mock_entity_result = {
            'projects': ['axie infinity', 'star atlas'],
            'tokens': ['AXS', 'ATLAS'],
            'blockchains': ['ethereum', 'solana'],
            'categories': ['play-to-earn', 'metaverse'],
            'confidence_scores': {
                'projects': 0.9,
                'tokens': 0.85,
                'blockchains': 0.95,
                'categories': 0.8
            }
        }
        
        with patch('api.content_intelligence_endpoints.entity_recognition_engine.recognize_entities', return_value=mock_entity_result):
            response = test_client.post(
                "/api/content-intelligence/entities",
                json={
                    "text": "Axie Infinity and Star Atlas are leading gaming projects on Ethereum and Solana"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert 'axie infinity' in data['projects']
            assert 'star atlas' in data['projects']
            assert 'AXS' in data['tokens']
            assert 'ethereum' in data['blockchains']
            assert data['confidence_scores']['projects'] == 0.9
    
    def test_market_intelligence_endpoint(self, test_client):
        """Test market intelligence endpoint"""
        mock_market_result = {
            'market_sentiment': 0.7,
            'trend_direction': 'uptrend',
            'volatility_score': 0.4,
            'investment_signals': {
                'dominant_signal': 'buy',
                'signal_strength': 0.75,
                'confidence': 0.8
            },
            'risk_assessment': {
                'overall_risk': 0.3,
                'risk_factors': ['market volatility'],
                'risk_level': 'Medium'
            }
        }
        
        with patch('api.content_intelligence_endpoints.market_intelligence_engine.analyze_gaming_sector', return_value=mock_market_result):
            response = test_client.post(
                "/api/content-intelligence/market-intelligence",
                json={
                    "content_data": [
                        {
                            "title": "Gaming Sector Shows Strong Performance",
                            "content": "Revenue growth continues across major gaming projects",
                            "summary": "Positive market trends",
                            "source": "gaming_news"
                        }
                    ],
                    "timeframe": "7d"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['market_sentiment'] == 0.7
            assert data['trend_direction'] == 'uptrend'
            assert data['investment_signals']['dominant_signal'] == 'buy'
            assert data['risk_assessment']['risk_level'] == 'Medium'


class TestMarketAnalyticsEndpoints:
    """Test market analytics API endpoints"""
    
    @pytest.mark.asyncio
    async def test_sector_analysis_endpoint(self, test_client):
        """Test gaming sector analysis endpoint"""
        mock_sector_result = {
            'timeframe': '7d',
            'correlations': {
                'correlation_matrix': {
                    'axie-infinity': {'star-atlas': 0.75},
                    'star-atlas': {'axie-infinity': 0.75}
                },
                'average_correlation': 0.75
            },
            'sector_performance': {
                'p2e': {'total_market_cap': 2000000000, 'avg_change_24h': 0.05},
                'metaverse': {'total_market_cap': 1500000000, 'avg_change_24h': 0.03}
            },
            'leading_indicators': ['user_growth', 'transaction_volume'],
            'cross_chain_analysis': {
                'ethereum': {'dominance': 0.6, 'growth_rate': 0.02},
                'solana': {'dominance': 0.3, 'growth_rate': 0.08}
            }
        }
        
        with patch('api.market_analytics_endpoints.gaming_sector_analyzer.analyze_cross_protocol_performance', return_value=mock_sector_result):
            response = test_client.get("/api/market-analytics/sector-analysis?timeframe=7d")
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['timeframe'] == '7d'
            assert data['correlations']['average_correlation'] == 0.75
            assert 'p2e' in data['sector_performance']
            assert 'ethereum' in data['cross_chain_analysis']
    
    @pytest.mark.asyncio
    async def test_portfolio_tracking_endpoint(self, test_client, sample_portfolio):
        """Test portfolio tracking endpoint"""
        mock_portfolio_result = {
            'portfolio_value': 20000,
            'change_24h': 1000,
            'change_24h_pct': 0.05,
            'risk_assessment': {
                'overall_risk': 0.4,
                'risk_level': 'Medium',
                'risk_factors': ['market volatility', 'concentration risk']
            },
            'positions': {
                'axie-infinity': {
                    'value': 10500,
                    'change_24h': 500,
                    'weight': 0.525
                }
            },
            'recommendations': [
                'Consider diversifying into other gaming categories',
                'Monitor market sentiment for entry/exit signals'
            ]
        }
        
        with patch('api.market_analytics_endpoints.investment_tracker.track_gaming_portfolio', return_value=mock_portfolio_result):
            response = test_client.post(
                "/api/market-analytics/portfolio-tracking",
                json={"portfolio": sample_portfolio}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data['portfolio_value'] == 20000
            assert data['change_24h_pct'] == 0.05
            assert data['risk_assessment']['risk_level'] == 'Medium'
            assert 'axie-infinity' in data['positions']
    
    @pytest.mark.asyncio
    async def test_market_alerts_endpoint(self, test_client):
        """Test market alerts endpoint"""
        mock_alerts = [
            MarketAlert(
                alert_type=AlertType.PRICE_SURGE,
                project_name="Axie Infinity",
                message="Price increased by 15% in the last hour",
                severity=AlertSeverity.MEDIUM,
                timestamp="2025-07-07T12:00:00Z",
                metadata={'price_change': 0.15}
            )
        ]
        
        with patch('api.market_analytics_endpoints.market_alert_system.monitor_market_conditions', return_value=mock_alerts):
            response = test_client.post(
                "/api/market-analytics/market-alerts",
                json={"projects": ["axie-infinity", "star-atlas"]}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert len(data['alerts']) == 1
            alert = data['alerts'][0]
            assert alert['alert_type'] == 'price_surge'
            assert alert['project_name'] == 'Axie Infinity'
            assert alert['severity'] == 'medium'


class TestCompetitiveAnalysisEndpoints:
    """Test competitive analysis API endpoints"""
    
    @pytest.mark.asyncio
    async def test_competitive_landscape_endpoint(self, test_client):
        """Test competitive landscape analysis endpoint"""
        mock_competitive_result = {
            'competitive_metrics': {
                'axie-infinity': {
                    'market_share': 0.4,
                    'user_adoption': 0.8,
                    'innovation_score': 0.7,
                    'community_strength': 0.9,
                    'technical_advancement': 0.6,
                    'partnership_strength': 0.8,
                    'tokenomics_score': 0.7,
                    'development_activity': 0.6
                }
            },
            'market_positioning': {
                'market_leaders': ['axie-infinity'],
                'innovation_leaders': ['star-atlas'],
                'community_leaders': ['axie-infinity'],
                'technical_leaders': ['star-atlas']
            },
            'competitive_advantages': {
                'axie-infinity': {
                    'strengths': ['Strong community', 'Market leadership'],
                    'weaknesses': ['Technical advancement'],
                    'opportunities': ['New market expansion'],
                    'threats': ['Increased competition']
                }
            },
            'market_share_analysis': {
                'total_market_size': 5000000000,
                'concentration_ratio': 0.65
            },
            'growth_comparison': {
                'fastest_growing': 'star-atlas',
                'growth_rates': {
                    'axie-infinity': 0.02,
                    'star-atlas': 0.08
                }
            }
        }
        
        with patch('api.competitive_analysis_endpoints.competitive_analysis_engine.analyze_competitive_landscape', return_value=mock_competitive_result):
            response = test_client.post(
                "/api/competitive-analysis/landscape",
                json={"projects": ["axie-infinity", "star-atlas"]}
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert 'competitive_metrics' in data
            assert 'market_positioning' in data
            assert 'axie-infinity' in data['competitive_metrics']
            assert 'axie-infinity' in data['market_positioning']['market_leaders']


class TestErrorHandling:
    """Test API error handling"""
    
    def test_invalid_request_data(self, test_client):
        """Test handling of invalid request data"""
        # Missing required fields
        response = test_client.post(
            "/api/content-intelligence/classify",
            json={"title": "Test"}  # Missing content and summary
        )
        
        assert response.status_code == 422  # Validation error
    
    def test_service_error_handling(self, test_client):
        """Test handling of service errors"""
        with patch('api.content_intelligence_endpoints.gaming_content_classifier.classify_content', side_effect=Exception("Service error")):
            response = test_client.post(
                "/api/content-intelligence/classify",
                json={
                    "title": "Test",
                    "content": "Test content",
                    "summary": "Test summary"
                }
            )
            
            assert response.status_code == 500
            data = response.json()
            assert 'error' in data
    
    def test_rate_limiting(self, test_client):
        """Test rate limiting functionality"""
        # This would require implementing rate limiting in the actual API
        # For now, just test that the endpoint responds normally
        response = test_client.post(
            "/api/content-intelligence/classify",
            json={
                "title": "Test",
                "content": "Test content",
                "summary": "Test summary"
            }
        )
        
        # Should not be rate limited for single request
        assert response.status_code in [200, 500]  # 500 if service not mocked


class TestAPIIntegration:
    """Test API integration scenarios"""
    
    def test_full_content_analysis_workflow(self, test_client):
        """Test full content analysis workflow through API"""
        # Mock all required services
        mock_classification = ContentClassificationResult(
            primary_category=GamingCategory.PLAY_TO_EARN,
            category_confidence=0.85,
            gaming_entities=['axie infinity'],
            blockchain_networks=['ethereum'],
            sentiment_score=0.7,
            key_themes=['breeding', 'battles']
        )
        
        mock_sentiment = {
            'overall_sentiment': 0.7,
            'gaming_sentiment': 0.8,
            'market_sentiment': 0.6,
            'community_sentiment': 0.7,
            'sentiment_category': 'positive',
            'key_sentiment_drivers': ['fun', 'profitable']
        }
        
        mock_entities = {
            'projects': ['axie infinity'],
            'tokens': ['AXS'],
            'blockchains': ['ethereum'],
            'categories': ['play-to-earn'],
            'confidence_scores': {'projects': 0.9}
        }
        
        content_data = {
            "title": "Axie Infinity New Update",
            "content": "Amazing new features for players",
            "summary": "Great update"
        }
        
        with patch('api.content_intelligence_endpoints.gaming_content_classifier.classify_content', return_value=mock_classification):
            with patch('api.content_intelligence_endpoints.sentiment_scoring_engine.analyze_gaming_sentiment', return_value=mock_sentiment):
                with patch('api.content_intelligence_endpoints.entity_recognition_engine.recognize_entities', return_value=mock_entities):
                    
                    # Step 1: Classify content
                    classify_response = test_client.post("/api/content-intelligence/classify", json=content_data)
                    assert classify_response.status_code == 200
                    
                    # Step 2: Analyze sentiment
                    sentiment_response = test_client.post(
                        "/api/content-intelligence/sentiment",
                        json={"text": content_data["content"]}
                    )
                    assert sentiment_response.status_code == 200
                    
                    # Step 3: Extract entities
                    entities_response = test_client.post(
                        "/api/content-intelligence/entities",
                        json={"text": content_data["content"]}
                    )
                    assert entities_response.status_code == 200
                    
                    # Verify consistent results across endpoints
                    classify_data = classify_response.json()
                    sentiment_data = sentiment_response.json()
                    entities_data = entities_response.json()
                    
                    assert classify_data['primary_category'] == 'play-to-earn'
                    assert sentiment_data['sentiment_category'] == 'positive'
                    assert 'axie infinity' in entities_data['projects']
