"""
Phase 7 Integration Tests
Tests for Content Intelligence and Market Analytics Dashboard Integration
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

from api.main import app
from services.content_intelligence import (
    gaming_content_classifier,
    sentiment_scoring_engine,
    trend_detection_engine,
    market_intelligence_engine,
    entity_recognition_engine
)
from services.market_analytics import (
    gaming_sector_analyzer,
    investment_tracker,
    market_alert_system
)

client = TestClient(app)


class TestContentIntelligenceEndpoints:
    """Test content intelligence API endpoints"""
    
    def test_content_classification_endpoint(self):
        """Test content classification endpoint"""
        test_data = {
            "title": "New Play-to-Earn Game Launches on Ethereum",
            "content": "A revolutionary blockchain gaming experience with NFT rewards",
            "summary": "Gaming project announcement with P2E mechanics"
        }
        
        with patch.object(gaming_content_classifier, 'classify_content') as mock_classify:
            # Mock classification result
            mock_result = MagicMock()
            mock_result.primary_category.value = "p2e"
            mock_result.category_confidence = 0.95
            mock_result.all_categories = {"p2e": 0.95, "nft": 0.8}
            mock_result.sentiment_score = 0.7
            mock_result.sentiment_category.value = "positive"
            mock_result.gaming_entities = ["Ethereum", "NFT"]
            mock_result.blockchain_networks = ["ethereum"]
            mock_result.market_signals = ["bullish"]
            mock_result.trend_indicators = ["gaming_growth"]
            mock_result.classification_timestamp = "2024-01-01T00:00:00Z"
            
            mock_classify.return_value = mock_result
            
            response = client.post("/api/v1/content-intelligence/classify", json=test_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["primary_category"] == "p2e"
            assert data["category_confidence"] == 0.95
            assert "ethereum" in data["blockchain_networks"]
    
    def test_sentiment_analysis_endpoint(self):
        """Test sentiment analysis endpoint"""
        test_data = {
            "title": "Exciting new gaming developments",
            "content": "The gaming community is thrilled about upcoming releases"
        }
        
        with patch.object(sentiment_scoring_engine, 'analyze_gaming_sentiment') as mock_sentiment:
            mock_sentiment.return_value = {
                'overall_sentiment': 0.8,
                'base_sentiment': 0.7,
                'gaming_sentiment': 0.9,
                'market_sentiment': 0.6,
                'community_sentiment': 0.8,
                'sentiment_confidence': 0.95,
                'sentiment_category': MagicMock(value='positive'),
                'key_sentiment_drivers': ['excitement', 'community'],
                'analysis_timestamp': '2024-01-01T00:00:00Z'
            }
            
            response = client.post("/api/v1/content-intelligence/sentiment", json=test_data)
            
            assert response.status_code == 200
            data = response.json()
            assert data["overall_sentiment"] == 0.8
            assert data["sentiment_category"] == "positive"
    
    def test_dashboard_analytics_endpoint(self):
        """Test analytics dashboard endpoint"""
        with patch('api.content_intelligence_endpoints.get_db') as mock_db:
            # Mock database session
            mock_session = MagicMock()
            mock_db.return_value = mock_session
            
            # Mock query results
            mock_articles = [
                MagicMock(title="Test Article 1", content="P2E gaming content", summary="Gaming"),
                MagicMock(title="Test Article 2", content="NFT marketplace news", summary="NFT")
            ]
            mock_session.query.return_value.filter.return_value.all.return_value = mock_articles
            
            # Mock analysis engines
            with patch.object(trend_detection_engine, 'detect_trends') as mock_trends, \
                 patch.object(market_intelligence_engine, 'analyze_gaming_sector') as mock_market, \
                 patch.object(gaming_content_classifier, 'classify_content') as mock_classify:
                
                # Mock trend analysis
                mock_trends.return_value = {
                    'trend_scores': {'confidence': 0.8, 'momentum': 0.6},
                    'market_phase': 'growth',
                    'emerging_themes': ['defi-gaming', 'nft-integration']
                }
                
                # Mock market analysis
                mock_market_result = MagicMock()
                mock_market_result.market_sentiment = 0.7
                mock_market_result.trend_direction = 'bullish'
                mock_market_result.investment_signals = ['positive_momentum']
                mock_market_result.risk_assessment = {'volatility': 'medium'}
                mock_market_result.competitive_analysis = {'market_leaders': ['axie-infinity']}
                mock_market.return_value = mock_market_result
                
                # Mock classification
                mock_classify_result = MagicMock()
                mock_classify_result.primary_category.value = "p2e"
                mock_classify_result.sentiment_score = 0.8
                mock_classify.return_value = mock_classify_result
                
                response = client.get("/api/v1/content-intelligence/analytics/dashboard?timeframe=24h")
                
                assert response.status_code == 200
                data = response.json()
                assert "summary" in data
                assert "category_distribution" in data
                assert "sentiment_analysis" in data
                assert "trend_analysis" in data
                assert "market_intelligence" in data
    
    def test_health_check_endpoint(self):
        """Test content intelligence health check"""
        with patch.object(gaming_content_classifier, 'classify_content') as mock_classify:
            mock_result = MagicMock()
            mock_result.primary_category.value = "test"
            mock_result.category_confidence = 0.9
            mock_classify.return_value = mock_result
            
            response = client.get("/api/v1/content-intelligence/health")
            
            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "healthy"
            assert "services" in data
            assert "test_classification" in data


class TestMarketAnalyticsEndpoints:
    """Test market analytics API endpoints"""
    
    def test_sector_analysis_endpoint(self):
        """Test sector analysis endpoint"""
        with patch.object(gaming_sector_analyzer, 'analyze_cross_protocol_performance') as mock_analyze:
            mock_analyze.return_value = {
                'total_market_cap': **********,
                'market_change_24h': 0.05,
                'active_projects': 15,
                'performance_data': [
                    {'date': '2024-01-01', 'market_cap': **********, 'volume': 50000000}
                ],
                'top_performers': [
                    {'name': 'Axie Infinity', 'change': 0.15}
                ],
                'health_metrics': {
                    'liquidity_score': 0.8,
                    'adoption_rate': 0.7,
                    'development_activity': 0.9
                }
            }
            
            response = client.get("/api/v1/content-intelligence/market/sector-analysis?timeframe=7d")
            
            assert response.status_code == 200
            data = response.json()
            assert data["total_market_cap"] == **********
            assert data["active_projects"] == 15
    
    def test_market_alerts_endpoint(self):
        """Test market alerts endpoint"""
        with patch.object(market_alert_system, 'get_active_alerts') as mock_alerts:
            mock_alerts.return_value = [
                {
                    'alert_type': 'price_movement',
                    'project_name': 'axie-infinity',
                    'message': 'Significant price increase detected',
                    'severity': 'medium',
                    'timestamp': '2024-01-01T00:00:00Z'
                }
            ]
            
            response = client.get("/api/v1/content-intelligence/market/alerts")
            
            assert response.status_code == 200
            data = response.json()
            assert "alerts" in data
            assert len(data["alerts"]) == 1
            assert data["alerts"][0]["project_name"] == "axie-infinity"
    
    def test_project_monitoring_endpoint(self):
        """Test project monitoring endpoint"""
        test_projects = ["axie-infinity", "star-atlas", "gala-games"]
        
        with patch.object(market_alert_system, 'monitor_market_conditions') as mock_monitor:
            mock_alert = MagicMock()
            mock_alert.alert_type.value = 'volume_spike'
            mock_alert.project_name = 'axie-infinity'
            mock_alert.message = 'Volume spike detected'
            mock_alert.severity = 'high'
            mock_alert.data = {'volume_change': 2.5}
            mock_alert.timestamp = '2024-01-01T00:00:00Z'
            
            mock_monitor.return_value = [mock_alert]
            
            response = client.post("/api/v1/content-intelligence/market/monitor", json=test_projects)
            
            assert response.status_code == 200
            data = response.json()
            assert "new_alerts" in data
            assert data["alert_count"] == 1
            assert data["monitored_projects"] == test_projects


class TestDashboardIntegration:
    """Test dashboard integration functionality"""
    
    def test_api_service_integration(self):
        """Test that API service methods are properly configured"""
        # This would be tested in the frontend, but we can verify the API structure
        
        # Test content intelligence endpoints
        response = client.get("/api/v1/content-intelligence/health")
        assert response.status_code == 200
        
        # Test that the endpoints return expected structure
        with patch('api.content_intelligence_endpoints.get_db'):
            response = client.get("/api/v1/content-intelligence/analytics/dashboard")
            # Should return 200 or proper error structure
            assert response.status_code in [200, 500]  # 500 if services not fully initialized
    
    def test_cors_configuration(self):
        """Test CORS configuration for dashboard integration"""
        # Test preflight request
        response = client.options("/api/v1/content-intelligence/health")
        assert response.status_code == 200
        
        # Check CORS headers are present
        headers = response.headers
        assert "access-control-allow-origin" in headers
        assert "access-control-allow-methods" in headers
    
    def test_error_handling_integration(self):
        """Test error handling for dashboard integration"""
        # Test invalid endpoint
        response = client.get("/api/v1/content-intelligence/invalid-endpoint")
        assert response.status_code == 404
        
        # Test malformed request
        response = client.post("/api/v1/content-intelligence/classify", json={"invalid": "data"})
        # Should return validation error
        assert response.status_code in [400, 422]


class TestPhase7FeatureIntegration:
    """Test complete Phase 7 feature integration"""
    
    def test_content_intelligence_workflow(self):
        """Test complete content intelligence workflow"""
        # Test classification -> sentiment -> trends -> market intelligence
        test_content = {
            "title": "Revolutionary P2E Game Launches",
            "content": "New blockchain gaming platform with innovative tokenomics",
            "summary": "Gaming platform launch announcement"
        }
        
        with patch.object(gaming_content_classifier, 'classify_content') as mock_classify, \
             patch.object(sentiment_scoring_engine, 'analyze_gaming_sentiment') as mock_sentiment:
            
            # Mock classification
            mock_classify_result = MagicMock()
            mock_classify_result.primary_category.value = "p2e"
            mock_classify_result.category_confidence = 0.9
            mock_classify_result.sentiment_score = 0.8
            mock_classify.return_value = mock_classify_result
            
            # Mock sentiment
            mock_sentiment.return_value = {
                'overall_sentiment': 0.8,
                'sentiment_category': MagicMock(value='positive'),
                'sentiment_confidence': 0.9,
                'key_sentiment_drivers': ['innovation', 'gaming'],
                'analysis_timestamp': '2024-01-01T00:00:00Z'
            }
            
            # Test classification
            classify_response = client.post("/api/v1/content-intelligence/classify", json=test_content)
            assert classify_response.status_code == 200
            
            # Test sentiment
            sentiment_response = client.post("/api/v1/content-intelligence/sentiment", json=test_content)
            assert sentiment_response.status_code == 200
            
            # Verify workflow integration
            classify_data = classify_response.json()
            sentiment_data = sentiment_response.json()
            
            assert classify_data["primary_category"] == "p2e"
            assert sentiment_data["overall_sentiment"] == 0.8
    
    def test_market_analytics_workflow(self):
        """Test complete market analytics workflow"""
        with patch.object(gaming_sector_analyzer, 'analyze_cross_protocol_performance') as mock_sector, \
             patch.object(market_alert_system, 'monitor_market_conditions') as mock_monitor:
            
            # Mock sector analysis
            mock_sector.return_value = {
                'total_market_cap': 2000000000,
                'market_change_24h': 0.08,
                'active_projects': 20
            }
            
            # Mock monitoring
            mock_monitor.return_value = []
            
            # Test sector analysis
            sector_response = client.get("/api/v1/content-intelligence/market/sector-analysis")
            assert sector_response.status_code == 200
            
            # Test project monitoring
            monitor_response = client.post("/api/v1/content-intelligence/market/monitor", 
                                         json=["axie-infinity", "star-atlas"])
            assert monitor_response.status_code == 200
            
            # Verify data consistency
            sector_data = sector_response.json()
            monitor_data = monitor_response.json()
            
            assert sector_data["total_market_cap"] == 2000000000
            assert "monitored_projects" in monitor_data


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
