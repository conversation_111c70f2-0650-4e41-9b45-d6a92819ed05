"""
Unit tests for Phase 7 Content Intelligence services
"""
import pytest
from unittest.mock import Mo<PERSON>, patch, MagicMock
import numpy as np

from services.content_intelligence import (
    AdvancedGamingContentClassifier,
    GamingSentimentEngine,
    TrendDetectionEngine,
    MarketIntelligenceEngine,
    EntityRecognitionEngine,
    gaming_content_classifier,
    sentiment_scoring_engine,
    trend_detection_engine,
    market_intelligence_engine,
    entity_recognition_engine
)
from services.content_intelligence import (
    GamingCategory,
    SentimentCategory,
    ContentClassificationResult,
    SentimentAnalysisResult,
    TrendAnalysisResult,
    MarketIntelligenceResult,
    EntityRecognitionResult
)


class TestAdvancedGamingContentClassifier:
    """Test gaming content classification functionality"""
    
    def test_initialization(self):
        """Test classifier initialization"""
        classifier = AdvancedGamingContentClassifier()
        assert classifier is not None
        assert hasattr(classifier, 'gaming_patterns')
        assert hasattr(classifier, 'category_keywords')
        assert len(classifier.category_keywords) == 11  # 11 gaming categories
    
    def test_pattern_classification(self):
        """Test pattern-based classification"""
        classifier = AdvancedGamingContentClassifier()
        
        # Test P2E content
        p2e_text = "Players can earn tokens by completing quests and battles in this play-to-earn game"
        result = classifier._pattern_classify(p2e_text)
        assert GamingCategory.PLAY_TO_EARN in result
        assert result[GamingCategory.PLAY_TO_EARN] > 0.5
        
        # Test NFT Gaming content
        nft_text = "Collect and trade unique NFT characters with special abilities"
        result = classifier._pattern_classify(nft_text)
        assert GamingCategory.NFT_GAMING in result
        assert result[GamingCategory.NFT_GAMING] > 0.5
        
        # Test Metaverse content
        metaverse_text = "Virtual world where players can build, explore, and socialize"
        result = classifier._pattern_classify(metaverse_text)
        assert GamingCategory.VIRTUAL_WORLDS in result
        assert result[GamingCategory.VIRTUAL_WORLDS] > 0.5
    
    def test_ml_classification_training(self):
        """Test ML model training"""
        classifier = AdvancedGamingContentClassifier()
        
        # Mock training data
        training_data = [
            ("Play to earn tokens by battling monsters", GamingCategory.PLAY_TO_EARN),
            ("Collect rare NFT cards and trade them", GamingCategory.NFT_GAMING),
            ("Build your virtual house in the metaverse", GamingCategory.VIRTUAL_WORLDS),
            ("Stake tokens to earn DeFi rewards", GamingCategory.DEFI_GAMING),
            ("Join esports tournaments for crypto prizes", GamingCategory.ESPORTS_CRYPTO)
        ]
        
        classifier.train_ml_model(training_data)
        
        assert classifier.is_trained is True
        assert classifier.model is not None
        assert classifier.vectorizer is not None
    
    def test_content_classification_result(self):
        """Test complete content classification"""
        classifier = AdvancedGamingContentClassifier()
        
        title = "Axie Infinity Introduces New Breeding Mechanics"
        content = "Players can now breed Axies with enhanced genetic algorithms for better battle performance"
        summary = "New breeding system improves gameplay"
        
        result = classifier.classify_content(title, content, summary)
        
        assert isinstance(result, ContentClassificationResult)
        assert result.primary_category in GamingCategory
        assert 0 <= result.category_confidence <= 1
        assert isinstance(result.gaming_entities, list)
        assert isinstance(result.blockchain_networks, list)
        assert -1 <= result.sentiment_score <= 1
    
    def test_confidence_calculation(self):
        """Test confidence score calculation"""
        classifier = AdvancedGamingContentClassifier()
        
        # High confidence scenario
        high_conf_scores = {
            GamingCategory.PLAY_TO_EARN: 0.9,
            GamingCategory.NFT_GAMING: 0.1,
            GamingCategory.GENERAL_GAMING: 0.0
        }
        confidence = classifier._calculate_confidence(high_conf_scores)
        assert confidence > 0.8
        
        # Low confidence scenario (similar scores)
        low_conf_scores = {
            GamingCategory.PLAY_TO_EARN: 0.35,
            GamingCategory.NFT_GAMING: 0.33,
            GamingCategory.GENERAL_GAMING: 0.32
        }
        confidence = classifier._calculate_confidence(low_conf_scores)
        assert confidence < 0.5


class TestGamingSentimentEngine:
    """Test gaming sentiment analysis functionality"""
    
    def test_initialization(self):
        """Test sentiment engine initialization"""
        engine = GamingSentimentEngine()
        assert engine is not None
        assert hasattr(engine, 'gaming_sentiment_weights')
        assert hasattr(engine, 'market_sentiment_weights')
        assert hasattr(engine, 'community_sentiment_weights')
    
    def test_base_sentiment_analysis(self):
        """Test base sentiment analysis using TextBlob"""
        engine = GamingSentimentEngine()
        
        # Positive sentiment
        positive_text = "This game is absolutely amazing and so much fun to play!"
        sentiment = engine._analyze_base_sentiment(positive_text)
        assert sentiment > 0
        
        # Negative sentiment
        negative_text = "This game is terrible and boring, complete waste of time"
        sentiment = engine._analyze_base_sentiment(negative_text)
        assert sentiment < 0
        
        # Neutral sentiment
        neutral_text = "The game has standard features and average gameplay"
        sentiment = engine._analyze_base_sentiment(neutral_text)
        assert -0.2 <= sentiment <= 0.2
    
    def test_gaming_specific_sentiment(self):
        """Test gaming-specific sentiment analysis"""
        engine = GamingSentimentEngine()
        
        # Gaming positive indicators
        fun_text = "This game is so fun and engaging with amazing gameplay mechanics"
        gaming_sentiment = engine._analyze_gaming_sentiment(fun_text)
        assert gaming_sentiment > 0.5
        
        # Gaming negative indicators
        boring_text = "This game is boring and repetitive with terrible mechanics"
        gaming_sentiment = engine._analyze_gaming_sentiment(boring_text)
        assert gaming_sentiment < -0.5
    
    def test_market_sentiment_analysis(self):
        """Test market sentiment analysis"""
        engine = GamingSentimentEngine()
        
        # Bullish market sentiment
        bullish_text = "Token price is mooning, great investment opportunity, bullish on this project"
        market_sentiment = engine._analyze_market_sentiment(bullish_text)
        assert market_sentiment > 0.5
        
        # Bearish market sentiment
        bearish_text = "Price is dumping, bearish outlook, avoid this investment"
        market_sentiment = engine._analyze_market_sentiment(bearish_text)
        assert market_sentiment < -0.5
    
    def test_community_sentiment_analysis(self):
        """Test community sentiment analysis"""
        engine = GamingSentimentEngine()
        
        # Supportive community
        supportive_text = "Great community support, helpful developers, amazing team"
        community_sentiment = engine._analyze_community_sentiment(supportive_text)
        assert community_sentiment > 0.5
        
        # Toxic community
        toxic_text = "Toxic community, scam project, terrible support"
        community_sentiment = engine._analyze_community_sentiment(toxic_text)
        assert community_sentiment < -0.5
    
    def test_temporal_factors(self):
        """Test temporal sentiment factors"""
        engine = GamingSentimentEngine()
        
        # Urgent language
        urgent_text = "Breaking news! Urgent update! Must act now!"
        factors = engine._analyze_temporal_factors(urgent_text)
        assert factors['urgency_factor'] > 0.5
        
        # Future outlook
        future_text = "Upcoming features will revolutionize gaming, future looks bright"
        factors = engine._analyze_temporal_factors(future_text)
        assert factors['future_outlook'] > 0.5
    
    def test_complete_sentiment_analysis(self):
        """Test complete sentiment analysis pipeline"""
        engine = GamingSentimentEngine()
        
        text = "Axie Infinity's new update is absolutely amazing! The breeding mechanics are so much fun and profitable!"
        result = engine.analyze_gaming_sentiment(text)
        
        assert isinstance(result, dict)
        assert 'overall_sentiment' in result
        assert 'gaming_sentiment' in result
        assert 'market_sentiment' in result
        assert 'community_sentiment' in result
        assert 'sentiment_category' in result
        assert 'key_sentiment_drivers' in result
        assert isinstance(result['sentiment_category'], SentimentCategory)


class TestTrendDetectionEngine:
    """Test trend detection functionality"""
    
    def test_initialization(self):
        """Test trend detection engine initialization"""
        engine = TrendDetectionEngine()
        assert engine is not None
        assert hasattr(engine, 'trend_keywords')
        assert hasattr(engine, 'market_cycle_indicators')
    
    def test_keyword_trend_analysis(self):
        """Test keyword-based trend analysis"""
        engine = TrendDetectionEngine()
        
        content_batch = [
            "Axie Infinity sees massive player growth",
            "Star Atlas announces major partnership",
            "Gaming adoption is accelerating rapidly",
            "Innovation in blockchain gaming continues"
        ]
        
        trend_scores = engine._analyze_keyword_trends(content_batch)
        
        assert 'adoption_keywords' in trend_scores
        assert 'innovation_keywords' in trend_scores
        assert 'partnership_keywords' in trend_scores
        assert trend_scores['adoption_keywords'] > 0
        assert trend_scores['innovation_keywords'] > 0
    
    def test_market_cycle_detection(self):
        """Test market cycle phase detection"""
        engine = TrendDetectionEngine()
        
        # Bull market indicators
        bull_content = [
            "Prices are surging across the board",
            "Massive adoption and growth",
            "Bullish momentum continues"
        ]
        
        market_phase = engine._detect_market_cycle(bull_content)
        assert market_phase['dominant_phase'] in ['bull_market', 'accumulation']
        
        # Bear market indicators
        bear_content = [
            "Prices are falling dramatically",
            "Market correction underway",
            "Bearish sentiment dominates"
        ]
        
        market_phase = engine._detect_market_cycle(bear_content)
        assert market_phase['dominant_phase'] in ['bear_market', 'distribution']
    
    def test_emerging_themes_extraction(self):
        """Test emerging themes identification"""
        engine = TrendDetectionEngine()
        
        content_batch = [
            "Gaming NFTs are becoming mainstream",
            "Metaverse platforms show strong growth",
            "Play-to-earn games gain popularity",
            "Gaming guilds expand globally"
        ]
        
        themes = engine._extract_emerging_themes(content_batch)
        
        assert isinstance(themes, list)
        assert len(themes) > 0
        
        # Check theme structure
        for theme in themes:
            assert 'theme' in theme
            assert 'frequency' in theme
            assert 'relevance_score' in theme
    
    def test_complete_trend_detection(self):
        """Test complete trend detection pipeline"""
        engine = TrendDetectionEngine()
        
        content_batch = [
            "Axie Infinity sees massive player growth in Southeast Asia",
            "Star Atlas announces major partnership with gaming studio",
            "The Sandbox launches creator fund for developers",
            "NFT gaming market shows strong bullish momentum"
        ]
        
        result = engine.detect_trends(content_batch, "24h")
        
        assert isinstance(result, dict)
        assert 'trend_scores' in result
        assert 'market_phase' in result
        assert 'trend_momentum' in result
        assert 'emerging_themes' in result


class TestMarketIntelligenceEngine:
    """Test market intelligence functionality"""
    
    def test_initialization(self):
        """Test market intelligence engine initialization"""
        engine = MarketIntelligenceEngine()
        assert engine is not None
        assert hasattr(engine, 'risk_factors')
        assert hasattr(engine, 'investment_signals')
    
    def test_investment_signal_generation(self):
        """Test investment signal generation"""
        engine = MarketIntelligenceEngine()
        
        # Positive content should generate buy signals
        positive_content = [
            {'title': 'Major partnership announced', 'sentiment': 0.8},
            {'title': 'Revenue growth accelerates', 'sentiment': 0.7},
            {'title': 'User adoption surges', 'sentiment': 0.9}
        ]
        
        signals = engine._generate_investment_signals(positive_content)
        assert signals['dominant_signal'] in ['buy', 'strong_buy']
        assert signals['signal_strength'] > 0.5
        
        # Negative content should generate sell signals
        negative_content = [
            {'title': 'Security breach reported', 'sentiment': -0.8},
            {'title': 'Revenue declines sharply', 'sentiment': -0.7},
            {'title': 'Users leaving platform', 'sentiment': -0.9}
        ]
        
        signals = engine._generate_investment_signals(negative_content)
        assert signals['dominant_signal'] in ['sell', 'strong_sell']
    
    def test_risk_assessment(self):
        """Test risk assessment functionality"""
        engine = MarketIntelligenceEngine()
        
        # Low risk scenario
        low_risk_content = [
            {'title': 'Regulatory approval received', 'sentiment': 0.5},
            {'title': 'Technical audit completed', 'sentiment': 0.6},
            {'title': 'Strong market position', 'sentiment': 0.7}
        ]
        
        risk_assessment = engine._assess_risks(low_risk_content)
        assert risk_assessment['overall_risk'] < 0.5
        
        # High risk scenario
        high_risk_content = [
            {'title': 'Regulatory investigation launched', 'sentiment': -0.8},
            {'title': 'Technical vulnerabilities found', 'sentiment': -0.7},
            {'title': 'Market share declining', 'sentiment': -0.6}
        ]
        
        risk_assessment = engine._assess_risks(high_risk_content)
        assert risk_assessment['overall_risk'] > 0.5
    
    def test_complete_market_intelligence(self):
        """Test complete market intelligence analysis"""
        engine = MarketIntelligenceEngine()
        
        content_data = [
            {
                'title': 'Axie Infinity Revenue Surges',
                'content': 'Play-to-earn leader shows strong Q4 performance',
                'summary': 'Revenue growth continues',
                'source': 'gaming_news'
            }
        ]
        
        result = engine.analyze_gaming_sector(content_data, "7d")
        
        assert isinstance(result, MarketIntelligenceResult)
        assert hasattr(result, 'market_sentiment')
        assert hasattr(result, 'trend_direction')
        assert hasattr(result, 'volatility_score')
        assert hasattr(result, 'investment_signals')
        assert hasattr(result, 'risk_assessment')


class TestEntityRecognitionEngine:
    """Test entity recognition functionality"""
    
    def test_initialization(self):
        """Test entity recognition engine initialization"""
        engine = EntityRecognitionEngine()
        assert engine is not None
        assert hasattr(engine, 'gaming_projects')
        assert hasattr(engine, 'token_symbols')
        assert hasattr(engine, 'blockchain_networks')
    
    def test_project_recognition(self):
        """Test gaming project recognition"""
        engine = EntityRecognitionEngine()
        
        text = "Axie Infinity and Star Atlas are leading the play-to-earn gaming revolution"
        projects = engine._extract_gaming_projects(text)
        
        assert 'axie infinity' in projects
        assert 'star atlas' in projects
    
    def test_token_recognition(self):
        """Test token symbol recognition"""
        engine = EntityRecognitionEngine()
        
        text = "AXS and ATLAS tokens surge as gaming adoption grows"
        tokens = engine._extract_tokens(text)
        
        assert 'axs' in [token.lower() for token in tokens]
        assert 'atlas' in [token.lower() for token in tokens]
    
    def test_blockchain_recognition(self):
        """Test blockchain network recognition"""
        engine = EntityRecognitionEngine()
        
        text = "Gaming projects on Ethereum and Solana show strong performance"
        blockchains = engine._extract_blockchains(text)
        
        assert 'ethereum' in blockchains
        assert 'solana' in blockchains
    
    def test_category_recognition(self):
        """Test gaming category recognition"""
        engine = EntityRecognitionEngine()
        
        text = "Play-to-earn and metaverse games dominate the NFT gaming space"
        categories = engine._extract_categories(text)
        
        assert 'play-to-earn' in categories
        assert 'metaverse' in categories
        assert 'nft gaming' in categories
    
    def test_complete_entity_recognition(self):
        """Test complete entity recognition pipeline"""
        engine = EntityRecognitionEngine()
        
        text = "Axie Infinity and Star Atlas are leading the play-to-earn gaming revolution on Ethereum and Solana"
        result = engine.recognize_entities(text)
        
        assert isinstance(result, dict)
        assert 'projects' in result
        assert 'tokens' in result
        assert 'blockchains' in result
        assert 'categories' in result
        assert 'confidence_scores' in result
        
        # Verify specific entities
        assert 'axie infinity' in result['projects']
        assert 'star atlas' in result['projects']
        assert 'ethereum' in result['blockchains']
        assert 'solana' in result['blockchains']
        assert 'play-to-earn' in result['categories']


class TestIntegrationScenarios:
    """Test integration scenarios between content intelligence components"""
    
    def test_full_content_analysis_pipeline(self, sample_content_classification):
        """Test full content analysis pipeline"""
        # Initialize all engines
        classifier = gaming_content_classifier
        sentiment_engine = sentiment_scoring_engine
        entity_engine = entity_recognition_engine
        
        title = sample_content_classification['title']
        content = sample_content_classification['content']
        summary = sample_content_classification['summary']
        
        # Step 1: Classify content
        classification = classifier.classify_content(title, content, summary)
        assert classification.primary_category.value == sample_content_classification['expected_category']
        
        # Step 2: Analyze sentiment
        full_text = f"{title} {content} {summary}"
        sentiment = sentiment_engine.analyze_gaming_sentiment(full_text)
        assert sentiment['sentiment_category'].value == sample_content_classification['expected_sentiment']
        
        # Step 3: Extract entities
        entities = entity_engine.recognize_entities(full_text)
        for expected_entity in sample_content_classification['expected_entities']:
            assert expected_entity in entities['projects']
    
    def test_market_intelligence_integration(self):
        """Test market intelligence integration with other components"""
        # Mock content with various sentiments and categories
        content_data = [
            {
                'title': 'Axie Infinity Revenue Surges',
                'content': 'Play-to-earn leader shows strong performance',
                'summary': 'Revenue growth continues',
                'source': 'gaming_news'
            },
            {
                'title': 'Star Atlas Partnership Announced',
                'content': 'Major gaming studio collaboration',
                'summary': 'Strategic partnership formed',
                'source': 'crypto_news'
            }
        ]
        
        # Analyze with market intelligence
        market_intel = market_intelligence_engine.analyze_gaming_sector(content_data, "7d")
        
        assert market_intel.market_sentiment > 0  # Should be positive
        assert market_intel.trend_direction in ['uptrend', 'neutral', 'downtrend']
        assert 0 <= market_intel.volatility_score <= 1
    
    def test_trend_detection_with_classification(self):
        """Test trend detection combined with content classification"""
        content_batch = [
            "New P2E game launches with innovative tokenomics",
            "Metaverse platform announces virtual land sale",
            "NFT gaming collection sees record trading volume",
            "DeFi gaming protocol introduces yield farming"
        ]
        
        # Classify each piece of content
        classifications = []
        for content in content_batch:
            classification = gaming_content_classifier.classify_content(content, "", "")
            classifications.append(classification)
        
        # Detect trends
        trends = trend_detection_engine.detect_trends(content_batch, "24h")
        
        # Verify trend detection incorporates classification results
        assert len(trends['emerging_themes']) > 0
        assert trends['trend_scores']['innovation_keywords'] > 0


class TestPerformanceAndScalability:
    """Test performance and scalability of content intelligence services"""

    def test_batch_processing_performance(self):
        """Test performance with large content batches"""
        import time

        # Generate large batch of content
        large_content_batch = [
            f"Gaming article {i} about blockchain and NFTs with play-to-earn mechanics"
            for i in range(100)
        ]

        # Test classification performance
        start_time = time.time()
        for content in large_content_batch[:10]:  # Test with smaller batch for unit test
            gaming_content_classifier.classify_content(content, "", "")
        classification_time = time.time() - start_time

        # Should process 10 articles in reasonable time (< 5 seconds)
        assert classification_time < 5.0

        # Test sentiment analysis performance
        start_time = time.time()
        for content in large_content_batch[:10]:
            sentiment_scoring_engine.analyze_gaming_sentiment(content)
        sentiment_time = time.time() - start_time

        # Should process 10 articles in reasonable time (< 3 seconds)
        assert sentiment_time < 3.0

    def test_memory_usage_optimization(self):
        """Test memory usage with repeated operations"""
        import gc
        import sys

        # Get initial memory usage
        gc.collect()
        initial_objects = len(gc.get_objects())

        # Perform multiple operations
        for i in range(50):
            content = f"Test gaming content {i} with various gaming keywords"
            gaming_content_classifier.classify_content(content, "", "")
            sentiment_scoring_engine.analyze_gaming_sentiment(content)
            entity_recognition_engine.recognize_entities(content)

        # Check memory usage after operations
        gc.collect()
        final_objects = len(gc.get_objects())

        # Memory growth should be reasonable (less than 50% increase)
        memory_growth = (final_objects - initial_objects) / initial_objects
        assert memory_growth < 0.5
