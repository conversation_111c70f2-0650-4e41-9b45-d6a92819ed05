"""
Gaming-specific database models
"""
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, Integer, String, Text, Boolean, Float, JSON, ForeignKey, Index, DateTime, BigInteger, Numeric, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from models.base import BaseModel
import uuid


class Article(BaseModel):
    """News article model"""
    __tablename__ = "articles"
    
    # Basic article info
    title = Column(String(500), nullable=False, index=True)
    content = Column(Text)
    summary = Column(Text)
    url = Column(String(1000), unique=True, nullable=False, index=True)
    author = Column(String(200))
    published_at = Column(DateTime, index=True)
    
    # Source information
    source_id = Column(Integer, ForeignKey("sources.id"), nullable=False)
    source_url = Column(String(1000))
    
    # Gaming categorization
    gaming_category = Column(String(50), index=True)  # P2E, NFT, DeFi, Metaverse, etc.
    gaming_subcategory = Column(String(100))
    gaming_projects = Column(JSON)  # Related gaming projects (list of strings)
    gaming_tokens = Column(JSON)   # Related tokens (list of strings)

    # Content analysis
    sentiment_score = Column(Float)  # -1 to 1
    relevance_score = Column(Float)  # 0 to 1
    keywords = Column(JSON)  # List of keywords
    tags = Column(JSON)  # List of tags
    
    # Engagement metrics
    views = Column(Integer, default=0)
    likes = Column(Integer, default=0)
    shares = Column(Integer, default=0)
    comments = Column(Integer, default=0)
    
    # Processing status
    is_processed = Column(Boolean, default=False)
    is_duplicate = Column(Boolean, default=False)
    duplicate_of_id = Column(Integer, ForeignKey("articles.id"))
    
    # Additional metadata
    extra_metadata = Column(JSON)
    
    # Relationships
    source = relationship("Source", back_populates="articles")
    blockchain_data = relationship("BlockchainData", back_populates="article")
    
    # Indexes
    __table_args__ = (
        Index('idx_articles_gaming_category', 'gaming_category'),
        Index('idx_articles_published_at', 'published_at'),
        Index('idx_articles_relevance', 'relevance_score'),
        Index('idx_articles_source_published', 'source_id', 'published_at'),
    )


class Source(BaseModel):
    """News source model"""
    __tablename__ = "sources"
    
    name = Column(String(200), nullable=False, unique=True)
    slug = Column(String(100), nullable=False, unique=True)  # URL-friendly identifier
    url = Column(String(1000), nullable=False)
    source_type = Column(String(50), nullable=False)  # RSS, API, Scraper, Social
    category = Column(String(100))  # Gaming, General, Social, Blockchain
    
    # Source configuration
    is_active = Column(Boolean, default=True)
    scrape_frequency = Column(Integer, default=3600)  # seconds
    last_scraped_at = Column(DateTime)
    
    # Quality metrics
    reliability_score = Column(Float, default=0.5)  # 0 to 1
    article_count = Column(Integer, default=0)
    
    # Configuration
    config = Column(JSON)  # Source-specific configuration
    
    # Relationships
    articles = relationship("Article", back_populates="source")


class GamingProject(BaseModel):
    """Comprehensive gaming project model based on CSV structure"""
    __tablename__ = "gaming_projects"

    # Basic project information
    project_name = Column(String(500), nullable=False, unique=True, index=True)  # Increased for long names
    project_website_link = Column(String(1000))
    whitepaper_link = Column(String(1000))
    blockchain = Column(String(100))
    validated_game_status = Column(String(100))  # Live, Coming Soon, etc.
    game_status_notes = Column(Text)
    token_schedule_link = Column(String(1000))

    # Token 1 information
    token1_symbol = Column(String(50))
    token1_contract_address = Column(String(500))  # Increased for long addresses
    token1_type = Column(String(200))  # Increased for long type descriptions
    token1_coingecko_link = Column(String(1000))
    token1_blockchain_scanner_link = Column(String(1000))
    token1_total_supply = Column(String(100))  # Use string for large numbers

    # Token 2 information
    token2_symbol = Column(String(50))
    token2_contract_address = Column(String(500))  # Increased for long addresses
    token2_type = Column(String(200))  # Increased for long type descriptions
    token2_coingecko_link = Column(String(1000))
    token2_blockchain_scanner_link = Column(String(1000))
    token2_total_supply = Column(String(100))

    # Genre classification (using Text for long descriptions)
    primary_genre = Column(Text)
    action_subgenre = Column(Text)
    action_adventure_subgenre = Column(Text)
    action_rpg_subgenre = Column(Text)
    adventure_subgenre = Column(Text)
    battle_royale_subgenre = Column(Text)
    casual_subgenre = Column(Text)
    fighting_subgenre = Column(Text)
    fps_subgenre = Column(Text)
    mmorpg_subgenre = Column(Text)
    party_subgenre = Column(Text)
    platformer_subgenre = Column(Text)
    puzzle_subgenre = Column(Text)
    racing_subgenre = Column(Text)
    rts_subgenre = Column(Text)
    rpg_subgenre = Column(Text)
    shooter_subgenre = Column(Text)
    simulation_subgenre = Column(Text)
    sports_subgenre = Column(Text)
    stealth_subgenre = Column(Text)
    strategy_subgenre = Column(Text)
    survival_subgenre = Column(Text)
    tactical_rpg_subgenre = Column(Text)

    # NFT information
    involves_nfts = Column(Boolean, default=False)
    nft_marketplace_link = Column(String(1000))
    nft_contract_address = Column(String(500))  # Increased for long addresses
    nft_blockchain_scanner_link = Column(String(1000))
    nft_function = Column(Text)  # What NFTs are used for
    nft_marketplace_links = Column(Text)  # Multiple marketplace links
    nft_name = Column(String(500))  # Increased for long NFT names
    nft_holding_wallets_count = Column(Integer)
    nft_holding_wallets_source = Column(String(1000))

    # Game style and mechanics
    game_style = Column(Text)  # Free to Play, Play to Earn, etc.

    # Social media links
    twitter_link = Column(String(1000))
    discord_link = Column(String(1000))
    telegram_link = Column(String(1000))
    medium_link = Column(String(1000))
    youtube_link = Column(String(1000))
    linkedin_link = Column(String(1000))
    facebook_link = Column(String(1000))
    reddit_link = Column(String(1000))

    # Metrics and analytics
    daily_active_users = Column(Integer)
    dau_source_link = Column(String(1000))
    daily_unique_active_wallets = Column(Integer)
    uaw_source_link = Column(String(1000))

    # Auto-clicker specific fields
    autoclicker_telegram_invite = Column(String(1000))
    autoclicker_telegram_channel = Column(String(1000))
    autoclicker_telegram_bot = Column(String(1000))
    autoclicker_membership_population = Column(Integer)
    autoclicker_twitter = Column(String(1000))
    autoclicker_discord = Column(String(1000))
    autoclicker_medium = Column(String(1000))
    autoclicker_youtube = Column(String(1000))
    autoclicker_has_token = Column(Boolean, default=False)

    # Development information
    developer = Column(String(500))  # Increased for long developer names
    platform = Column(String(500))  # Increased for long platform descriptions

    # Additional fields
    additional_tokens = Column(Text)
    notes_comments = Column(Text)
    additional_notes = Column(Text)
    notes_additional_tokens = Column(Text)

    # Metadata
    email_address = Column(String(500))  # Submitter email (increased)
    timestamp = Column(DateTime)  # Submission timestamp

    # Derived fields for compatibility with existing analytics
    slug = Column(String(500), index=True)  # Generated from project_name (increased)
    description = Column(Text)  # Can be populated from notes
    category = Column(String(50))  # Derived from primary_genre
    subcategory = Column(Text)  # Derived from subgenres (can be long)
    is_active = Column(Boolean, default=True)

    # Additional metadata
    extra_metadata = Column(JSON)

    # Relationships
    blockchain_data = relationship("BlockchainData", back_populates="gaming_project")


class BlockchainData(BaseModel):
    """Blockchain-related data model"""
    __tablename__ = "blockchain_data"
    
    # Reference to article or project
    article_id = Column(Integer, ForeignKey("articles.id"))
    gaming_project_id = Column(Integer, ForeignKey("gaming_projects.id"))
    
    # Blockchain information
    blockchain = Column(String(50), nullable=False, index=True)
    block_number = Column(Integer)
    transaction_hash = Column(String(100), index=True)
    contract_address = Column(String(100), index=True)
    
    # Event data
    event_type = Column(String(100))  # Transfer, Sale, Mint, etc.
    event_data = Column(JSON)
    
    # Token information
    token_symbol = Column(String(20))
    token_address = Column(String(100))
    token_amount = Column(Float)
    token_price_usd = Column(Float)
    
    # NFT information
    nft_collection = Column(String(200))
    nft_token_id = Column(String(100))
    nft_metadata = Column(JSON)
    
    # Transaction details
    from_address = Column(String(100))
    to_address = Column(String(100))
    gas_used = Column(Integer)
    gas_price = Column(Float)
    
    # Timestamps
    block_timestamp = Column(DateTime, index=True)
    
    # Relationships
    article = relationship("Article", back_populates="blockchain_data")
    gaming_project = relationship("GamingProject", back_populates="blockchain_data")
    
    # Indexes
    __table_args__ = (
        Index('idx_blockchain_data_blockchain', 'blockchain'),
        Index('idx_blockchain_data_contract', 'contract_address'),
        Index('idx_blockchain_data_timestamp', 'block_timestamp'),
        Index('idx_blockchain_data_event_type', 'event_type'),
    )


class NFTCollection(BaseModel):
    """NFT collection model for gaming NFTs"""
    __tablename__ = "nft_collections"
    
    name = Column(String(200), nullable=False)
    slug = Column(String(200), nullable=False, unique=True, index=True)
    contract_address = Column(String(100), nullable=False, index=True)
    blockchain = Column(String(50), nullable=False)
    
    # Collection info
    description = Column(Text)
    image_url = Column(String(500))
    website = Column(String(500))
    
    # Gaming categorization
    gaming_category = Column(String(50))  # In-game assets, Characters, Land, etc.
    gaming_project_id = Column(Integer, ForeignKey("gaming_projects.id"))
    
    # Collection metrics
    total_supply = Column(Integer)
    floor_price = Column(Float)
    floor_price_usd = Column(Float)
    volume_24h = Column(Float)
    volume_total = Column(Float)
    owners_count = Column(Integer)
    
    # Social metrics
    discord_members = Column(Integer)
    twitter_followers = Column(Integer)
    
    # Status
    is_verified = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    
    # Additional metadata
    extra_metadata = Column(JSON)
    
    # Indexes
    __table_args__ = (
        Index('idx_nft_collections_blockchain', 'blockchain'),
        Index('idx_nft_collections_gaming_category', 'gaming_category'),
        Index('idx_nft_collections_floor_price', 'floor_price'),
    )


class GamingProtocolMetricsHistory(BaseModel):
    """Historical gaming protocol metrics for time-series analysis"""
    __tablename__ = "gaming_protocol_metrics_history"

    # Protocol identification
    protocol_name = Column(String(100), nullable=False, index=True)
    protocol_display_name = Column(String(200))
    chain = Column(String(50), nullable=False)

    # Token metrics
    token_price = Column(Numeric(20, 8))  # High precision for crypto prices
    token_price_change_24h = Column(Float)
    market_cap = Column(BigInteger)  # Can be very large numbers
    trading_volume_24h = Column(BigInteger)
    circulating_supply = Column(BigInteger)

    # User activity metrics
    daily_active_users = Column(Integer)
    monthly_active_users = Column(Integer)
    new_users_24h = Column(Integer)
    user_retention_rate = Column(Float)

    # Transaction metrics
    transaction_count_24h = Column(Integer)
    transaction_volume_24h = Column(BigInteger)
    average_transaction_value = Column(Numeric(20, 8))
    gas_fees_24h = Column(Numeric(20, 8))

    # NFT metrics
    nft_trades_24h = Column(Integer)
    nft_volume_24h = Column(BigInteger)
    floor_price = Column(Numeric(20, 8))
    unique_holders = Column(Integer)

    # Protocol health metrics
    total_value_locked = Column(BigInteger)
    protocol_revenue_24h = Column(BigInteger)
    staking_rewards_distributed = Column(BigInteger)
    average_earnings_per_user = Column(Numeric(20, 8))
    reward_token_distribution = Column(BigInteger)
    gameplay_sessions_24h = Column(Integer)
    protocol_uptime = Column(Float)
    smart_contract_interactions = Column(Integer)
    developer_activity_score = Column(Float)

    # Metadata
    data_source = Column(String(100))  # e.g., 'cryptorank', 'flipside'
    collection_timestamp = Column(DateTime, nullable=False, index=True)


class TwitterPost(BaseModel):
    """Twitter post model for gaming content"""
    __tablename__ = "twitter_posts"

    # Post identification
    twitter_id = Column(String(50), unique=True, nullable=False, index=True)
    text = Column(Text, nullable=False)
    author_username = Column(String(100), nullable=False, index=True)
    author_name = Column(String(200))
    author_verified = Column(Boolean, default=False)

    # Post metadata
    created_at = Column(DateTime, nullable=False, index=True)
    language = Column(String(10), default='en')
    url = Column(String(500))

    # Engagement metrics
    retweet_count = Column(Integer, default=0)
    like_count = Column(Integer, default=0)
    reply_count = Column(Integer, default=0)
    quote_count = Column(Integer, default=0)

    # Content analysis
    hashtags = Column(JSON)  # List of hashtags
    mentions = Column(JSON)  # List of mentioned users
    gaming_keywords = Column(JSON)  # Gaming-related keywords found
    gaming_projects = Column(JSON)  # Gaming projects mentioned
    gaming_influencers = Column(JSON)  # Gaming influencers mentioned

    # Classification
    is_gaming_related = Column(Boolean, default=False, index=True)
    gaming_category = Column(String(50))  # P2E, NFT, DeFi, Metaverse, etc.
    sentiment_score = Column(Float)  # -1 to 1
    relevance_score = Column(Float)  # 0 to 1

    # Processing status
    is_processed = Column(Boolean, default=False)
    collection_source = Column(String(50))  # 'keyword_search', 'user_timeline', etc.

    # Additional metadata
    extra_metadata = Column(JSON)


class RedditPost(BaseModel):
    """Reddit post model for gaming content"""
    __tablename__ = "reddit_posts"

    # Post identification
    reddit_id = Column(String(50), unique=True, nullable=False, index=True)
    title = Column(String(500), nullable=False)
    selftext = Column(Text)
    author = Column(String(100), index=True)
    subreddit = Column(String(100), nullable=False, index=True)

    # Post metadata
    created_utc = Column(DateTime, nullable=False, index=True)
    url = Column(String(1000))
    permalink = Column(String(500))
    post_type = Column(String(50))  # 'text', 'link', 'image', 'video'

    # Engagement metrics
    score = Column(Integer, default=0, index=True)  # Upvotes - downvotes
    upvote_ratio = Column(Float)
    num_comments = Column(Integer, default=0)

    # Content analysis
    gaming_keywords = Column(JSON)  # Gaming-related keywords found
    gaming_projects = Column(JSON)  # Gaming projects mentioned
    gaming_tokens = Column(JSON)  # Gaming tokens mentioned

    # Classification
    is_gaming_related = Column(Boolean, default=False, index=True)
    gaming_category = Column(String(50))  # P2E, NFT, DeFi, Metaverse, etc.
    sentiment_score = Column(Float)  # -1 to 1
    relevance_score = Column(Float)  # 0 to 1
    quality_score = Column(Float)  # Based on upvotes, comments, etc.

    # Processing status
    is_processed = Column(Boolean, default=False)
    meets_quality_threshold = Column(Boolean, default=False, index=True)

    # Additional metadata
    extra_metadata = Column(JSON)


class SocialMediaFilter(BaseModel):
    """User-defined filters for social media content"""
    __tablename__ = "social_media_filters"

    # Filter identification
    filter_name = Column(String(100), nullable=False)
    filter_type = Column(String(50), nullable=False)  # 'twitter', 'reddit', 'both'
    is_active = Column(Boolean, default=True, index=True)
    is_locked = Column(Boolean, default=False)  # Locked filters can't be deleted

    # Filter criteria
    keywords = Column(JSON)  # List of keywords to include
    excluded_keywords = Column(JSON)  # List of keywords to exclude
    gaming_projects = Column(JSON)  # Specific gaming projects to track
    gaming_influencers = Column(JSON)  # Specific influencers to track
    gaming_tokens = Column(JSON)  # Specific tokens to track

    # Quality thresholds
    min_engagement_score = Column(Integer, default=0)  # Minimum likes/upvotes
    min_relevance_score = Column(Float, default=0.0)  # Minimum relevance score
    min_sentiment_score = Column(Float, default=-1.0)  # Minimum sentiment score

    # Time filters
    max_age_hours = Column(Integer, default=24)  # Maximum age of posts to include

    # Platform-specific settings
    twitter_settings = Column(JSON)  # Twitter-specific filter settings
    reddit_settings = Column(JSON)  # Reddit-specific filter settings

    # Usage tracking
    last_used_at = Column(DateTime)
    usage_count = Column(Integer, default=0)

    # Additional metadata
    description = Column(Text)
    extra_metadata = Column(JSON)

    # Indexes for efficient filtering
    __table_args__ = (
        Index('idx_social_media_filter_active', 'is_active'),
        Index('idx_social_media_filter_type', 'filter_type'),
        Index('idx_social_media_filter_name', 'filter_name'),
    )


class GamingProtocolDailySummary(BaseModel):
    """Daily aggregated gaming protocol metrics for efficient querying"""
    __tablename__ = "gaming_protocol_daily_summary"

    # Protocol identification
    protocol_name = Column(String(100), nullable=False, index=True)
    date = Column(DateTime, nullable=False, index=True)  # Date (start of day)

    # Aggregated metrics for the day
    avg_token_price = Column(Numeric(20, 8))
    min_token_price = Column(Numeric(20, 8))
    max_token_price = Column(Numeric(20, 8))
    price_change_percent = Column(Float)

    avg_market_cap = Column(BigInteger)
    total_trading_volume = Column(BigInteger)

    max_daily_active_users = Column(Integer)
    total_transactions = Column(Integer)
    total_transaction_volume = Column(BigInteger)

    total_nft_trades = Column(Integer)
    total_nft_volume = Column(BigInteger)
    avg_floor_price = Column(Numeric(20, 8))

    avg_protocol_uptime = Column(Float)
    total_protocol_revenue = Column(BigInteger)

    # Data quality metrics
    data_points_collected = Column(Integer)  # How many times we collected data this day
    last_updated = Column(DateTime, default=func.now())

    # Unique constraint to prevent duplicates
    __table_args__ = (
        Index('idx_daily_summary_protocol_date', 'protocol_name', 'date', unique=True),
        Index('idx_daily_summary_date', 'date'),
    )


# Blockchain Scraper Models

class GamingContract(BaseModel):
    """Gaming contract detection and analysis model"""
    __tablename__ = "gaming_contracts"

    contract_address = Column(String(100), nullable=False, index=True)
    blockchain = Column(String(50), nullable=False, index=True)

    # Contract metadata
    name = Column(String(200))
    symbol = Column(String(50))
    contract_type = Column(String(50))  # ERC20, ERC721, ERC1155, etc.

    # Analysis results
    is_gaming = Column(Boolean, default=False, index=True)
    confidence_level = Column(String(20), index=True)  # HIGH, MEDIUM, LOW, NONE
    confidence_score = Column(Float)

    # Heuristic analysis
    heuristic_patterns = Column(JSON)  # List of detected patterns
    heuristic_score = Column(Float)

    # ML analysis
    ml_probability = Column(Float)
    ml_confidence = Column(Float)
    ml_features = Column(JSON)  # Feature importance scores

    # Contract characteristics
    deployment_block = Column(Integer)
    deployment_timestamp = Column(DateTime)
    creator_address = Column(String(100), index=True)
    bytecode_size = Column(Integer)
    function_count = Column(Integer)
    event_count = Column(Integer)

    # Activity metrics
    transaction_count_24h = Column(Integer, default=0)
    unique_users_24h = Column(Integer, default=0)
    last_activity = Column(DateTime)

    # Monitoring status
    is_monitored = Column(Boolean, default=False, index=True)
    monitoring_started = Column(DateTime)

    # Indexes
    __table_args__ = (
        Index('idx_gaming_contracts_address_blockchain', 'contract_address', 'blockchain'),
        Index('idx_gaming_contracts_gaming', 'is_gaming'),
        Index('idx_gaming_contracts_monitored', 'is_monitored'),
        UniqueConstraint('contract_address', 'blockchain', name='uq_contract_blockchain'),
    )


class GameEvent(BaseModel):
    """Gaming events from blockchain monitoring"""
    __tablename__ = "game_events"

    event_id = Column(String(100), nullable=False, unique=True, index=True)  # Unique identifier

    # Blockchain info
    blockchain = Column(String(50), nullable=False, index=True)
    block_number = Column(Integer, nullable=False, index=True)
    transaction_hash = Column(String(100), nullable=False, index=True)
    log_index = Column(Integer)

    # Contract info
    contract_address = Column(String(100), nullable=False, index=True)
    gaming_contract_id = Column(Integer, ForeignKey("gaming_contracts.id"))

    # Event details
    event_type = Column(String(50), index=True)  # PLAYER_ACTION, ITEM_TRANSACTION, etc.
    event_name = Column(String(100), index=True)  # Transfer, Mint, Battle, etc.
    event_signature = Column(String(200))

    # Event data
    raw_data = Column(JSON)  # Raw event data
    decoded_data = Column(JSON)  # Decoded/processed event data

    # Gaming context
    player_address = Column(String(100), index=True)
    token_id = Column(String(100), index=True)
    amount = Column(String(100))  # Use string for large numbers
    game_action = Column(String(50), index=True)  # mint, burn, transfer, battle, etc.

    # Metadata
    gas_used = Column(Integer)
    gas_price = Column(String(50))
    block_timestamp = Column(DateTime, nullable=False, index=True)

    # Relationships
    gaming_contract = relationship("GamingContract", backref="events")

    # Indexes
    __table_args__ = (
        Index('idx_game_events_contract_timestamp', 'contract_address', 'block_timestamp'),
        Index('idx_game_events_player_timestamp', 'player_address', 'block_timestamp'),
        Index('idx_game_events_type_timestamp', 'event_type', 'block_timestamp'),
    )


class ContractAnalysis(BaseModel):
    """Contract analysis history and results"""
    __tablename__ = "contract_analyses"

    contract_address = Column(String(100), nullable=False, index=True)
    blockchain = Column(String(50), nullable=False, index=True)

    # Analysis metadata
    analysis_type = Column(String(20), index=True)  # heuristic, ml, combined
    analysis_version = Column(String(20))  # Version of analysis algorithm

    # Results
    is_gaming = Column(Boolean, index=True)
    confidence_level = Column(String(20), index=True)
    confidence_score = Column(Float)

    # Detailed results
    detected_patterns = Column(JSON)
    feature_scores = Column(JSON)
    analysis_details = Column(JSON)

    # Performance metrics
    analysis_duration = Column(Float)  # Time taken in seconds

    # Indexes
    __table_args__ = (
        Index('idx_contract_analyses_address_blockchain', 'contract_address', 'blockchain'),
        Index('idx_contract_analyses_type', 'analysis_type'),
        Index('idx_contract_analyses_gaming', 'is_gaming'),
    )
