#!/bin/bash

# Web3 Gaming News Tracker - Stop Script
# This script stops all running services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🛑 Stopping Web3 Gaming News Tracker Services${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Stop backend
if [ -f "$PROJECT_ROOT/deployment/backend.pid" ]; then
    BACKEND_PID=$(cat "$PROJECT_ROOT/deployment/backend.pid")
    if kill -0 "$BACKEND_PID" 2>/dev/null; then
        print_info "Stopping backend (PID: $BACKEND_PID)..."
        kill "$BACKEND_PID"
        print_status "Backend stopped"
    else
        print_warning "Backend process not running"
    fi
    rm -f "$PROJECT_ROOT/deployment/backend.pid"
else
    print_warning "Backend PID file not found"
fi

# Stop frontend
if [ -f "$PROJECT_ROOT/deployment/frontend.pid" ]; then
    FRONTEND_PID=$(cat "$PROJECT_ROOT/deployment/frontend.pid")
    if kill -0 "$FRONTEND_PID" 2>/dev/null; then
        print_info "Stopping frontend (PID: $FRONTEND_PID)..."
        kill "$FRONTEND_PID"
        print_status "Frontend stopped"
    else
        print_warning "Frontend process not running"
    fi
    rm -f "$PROJECT_ROOT/deployment/frontend.pid"
else
    print_warning "Frontend PID file not found"
fi

# Stop any remaining uvicorn processes
print_info "Stopping any remaining uvicorn processes..."
pkill -f "uvicorn api.main:app" 2>/dev/null || true

# Stop any remaining npm processes
print_info "Stopping any remaining npm processes..."
pkill -f "npm start" 2>/dev/null || true

# Stop Docker containers
print_info "Stopping Docker containers..."
cd "$PROJECT_ROOT"
if docker-compose down; then
    print_status "Docker containers stopped"
else
    print_warning "Failed to stop Docker containers or they were not running"
fi

# Clean up deployment info
rm -f "$PROJECT_ROOT/deployment/deployment_info.txt"

print_status "All services stopped successfully"

echo -e "\n${GREEN}🎉 Shutdown completed!${NC}"
