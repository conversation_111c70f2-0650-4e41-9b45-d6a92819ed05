#!/usr/bin/env python3
"""
Deployment Validation Script
Comprehensive validation of deployed Web3 Gaming News Tracker
"""

import requests
import time
import json
import sys
import subprocess
from typing import Dict, List, Tuple
from datetime import datetime

# Configuration
BACKEND_URL = "http://localhost:8000"
FRONTEND_URL = "http://localhost:3000"
TIMEOUT = 10

class Colors:
    RED = '\033[0;31m'
    GREEN = '\033[0;32m'
    YELLOW = '\033[1;33m'
    BLUE = '\033[0;34m'
    NC = '\033[0m'  # No Color

def print_status(message: str):
    print(f"{Colors.GREEN}✅ {message}{Colors.NC}")

def print_warning(message: str):
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.NC}")

def print_error(message: str):
    print(f"{Colors.RED}❌ {message}{Colors.NC}")

def print_info(message: str):
    print(f"{Colors.BLUE}ℹ️  {message}{Colors.NC}")

class DeploymentValidator:
    """Comprehensive deployment validation"""
    
    def __init__(self):
        self.results = {
            'passed': 0,
            'failed': 0,
            'warnings': 0,
            'tests': []
        }
    
    def add_result(self, test_name: str, passed: bool, message: str, warning: bool = False):
        """Add test result"""
        self.results['tests'].append({
            'name': test_name,
            'passed': passed,
            'message': message,
            'warning': warning,
            'timestamp': datetime.now().isoformat()
        })
        
        if warning:
            self.results['warnings'] += 1
            print_warning(f"{test_name}: {message}")
        elif passed:
            self.results['passed'] += 1
            print_status(f"{test_name}: {message}")
        else:
            self.results['failed'] += 1
            print_error(f"{test_name}: {message}")
    
    def test_service_health(self) -> bool:
        """Test basic service health"""
        print_info("Testing service health...")
        
        # Test backend health
        try:
            response = requests.get(f"{BACKEND_URL}/health", timeout=TIMEOUT)
            if response.status_code == 200:
                data = response.json()
                self.add_result("Backend Health", True, f"Status: {data.get('status', 'unknown')}")
                
                # Check individual components
                for component in ['database', 'redis', 'blockchain']:
                    if component in data:
                        comp_status = data[component].get('status', 'unknown')
                        if comp_status in ['healthy', 'connected']:
                            self.add_result(f"{component.title()} Health", True, f"Status: {comp_status}")
                        else:
                            self.add_result(f"{component.title()} Health", False, f"Status: {comp_status}", warning=True)
                
                return True
            else:
                self.add_result("Backend Health", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.add_result("Backend Health", False, f"Connection failed: {e}")
            return False
    
    def test_api_endpoints(self) -> bool:
        """Test critical API endpoints"""
        print_info("Testing API endpoints...")
        
        endpoints = [
            ("/docs", "API Documentation"),
            ("/metrics", "Prometheus Metrics"),
            ("/api/v1/gaming/projects", "Gaming Projects"),
            ("/api/v1/content-intelligence/health", "Content Intelligence"),
            ("/api/v1/blockchain/health", "Blockchain Data"),
            ("/api/v1/social/health", "Social Media")
        ]
        
        all_passed = True
        for endpoint, name in endpoints:
            try:
                response = requests.get(f"{BACKEND_URL}{endpoint}", timeout=TIMEOUT)
                if response.status_code in [200, 503]:  # 503 acceptable for some services
                    self.add_result(f"{name} Endpoint", True, f"HTTP {response.status_code}")
                else:
                    self.add_result(f"{name} Endpoint", False, f"HTTP {response.status_code}")
                    all_passed = False
                    
            except Exception as e:
                self.add_result(f"{name} Endpoint", False, f"Request failed: {e}")
                all_passed = False
        
        return all_passed
    
    def test_frontend_accessibility(self) -> bool:
        """Test frontend accessibility"""
        print_info("Testing frontend accessibility...")
        
        try:
            response = requests.get(FRONTEND_URL, timeout=TIMEOUT)
            if response.status_code == 200:
                content = response.text
                if "Web3 Gaming" in content or "root" in content:
                    self.add_result("Frontend Accessibility", True, "Frontend is serving content")
                    return True
                else:
                    self.add_result("Frontend Accessibility", False, "Content validation failed", warning=True)
                    return False
            else:
                self.add_result("Frontend Accessibility", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.add_result("Frontend Accessibility", False, f"Connection failed: {e}")
            return False
    
    def test_security_features(self) -> bool:
        """Test security features"""
        print_info("Testing security features...")
        
        try:
            response = requests.get(f"{BACKEND_URL}/health", timeout=TIMEOUT)
            headers = response.headers
            
            # Check security headers
            security_headers = [
                'X-Content-Type-Options',
                'X-Frame-Options',
                'X-XSS-Protection'
            ]
            
            found_headers = 0
            for header in security_headers:
                if header in headers:
                    found_headers += 1
            
            if found_headers > 0:
                self.add_result("Security Headers", True, f"{found_headers}/{len(security_headers)} headers present")
            else:
                self.add_result("Security Headers", False, "No security headers found")
            
            # Test CORS
            cors_response = requests.options(f"{BACKEND_URL}/health", timeout=TIMEOUT)
            if 'Access-Control-Allow-Origin' in cors_response.headers:
                self.add_result("CORS Configuration", True, "CORS headers present")
            else:
                self.add_result("CORS Configuration", False, "CORS headers missing", warning=True)
            
            return found_headers > 0
            
        except Exception as e:
            self.add_result("Security Features", False, f"Test failed: {e}")
            return False
    
    def test_performance(self) -> bool:
        """Test basic performance"""
        print_info("Testing performance...")
        
        endpoints = [
            "/health",
            "/api/v1/gaming/projects"
        ]
        
        all_passed = True
        for endpoint in endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{BACKEND_URL}{endpoint}", timeout=TIMEOUT)
                response_time = time.time() - start_time
                
                if response_time < 5.0:
                    self.add_result(f"Performance {endpoint}", True, f"Response time: {response_time:.2f}s")
                else:
                    self.add_result(f"Performance {endpoint}", False, f"Slow response: {response_time:.2f}s", warning=True)
                    all_passed = False
                    
            except Exception as e:
                self.add_result(f"Performance {endpoint}", False, f"Test failed: {e}")
                all_passed = False
        
        return all_passed
    
    def test_phase7_features(self) -> bool:
        """Test Phase 7 specific features"""
        print_info("Testing Phase 7 features...")
        
        # Test content intelligence
        test_data = {
            "title": "Test Gaming Content",
            "content": "This is a test for content classification",
            "summary": "Test content"
        }
        
        try:
            response = requests.post(
                f"{BACKEND_URL}/api/v1/content-intelligence/classify",
                json=test_data,
                timeout=15
            )
            
            if response.status_code in [200, 503]:
                self.add_result("Content Intelligence", True, f"Classification endpoint responsive (HTTP {response.status_code})")
            else:
                self.add_result("Content Intelligence", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.add_result("Content Intelligence", False, f"Test failed: {e}", warning=True)
        
        # Test market analytics
        try:
            response = requests.get(
                f"{BACKEND_URL}/api/v1/content-intelligence/market/sector-analysis",
                timeout=15
            )
            
            if response.status_code in [200, 503]:
                self.add_result("Market Analytics", True, f"Sector analysis endpoint responsive (HTTP {response.status_code})")
            else:
                self.add_result("Market Analytics", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.add_result("Market Analytics", False, f"Test failed: {e}", warning=True)
        
        # Test dashboard analytics
        try:
            response = requests.get(
                f"{BACKEND_URL}/api/v1/content-intelligence/analytics/dashboard",
                timeout=15
            )
            
            if response.status_code in [200, 503]:
                self.add_result("Dashboard Analytics", True, f"Dashboard endpoint responsive (HTTP {response.status_code})")
                return True
            else:
                self.add_result("Dashboard Analytics", False, f"HTTP {response.status_code}")
                return False
                
        except Exception as e:
            self.add_result("Dashboard Analytics", False, f"Test failed: {e}", warning=True)
            return False
    
    def test_data_integrity(self) -> bool:
        """Test data integrity"""
        print_info("Testing data integrity...")
        
        try:
            # Test that we can get some gaming projects data
            response = requests.get(f"{BACKEND_URL}/api/v1/gaming/projects", timeout=TIMEOUT)
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, (list, dict)):
                    self.add_result("Gaming Projects Data", True, f"Data structure valid ({type(data).__name__})")
                else:
                    self.add_result("Gaming Projects Data", False, "Invalid data structure", warning=True)
            else:
                self.add_result("Gaming Projects Data", False, f"HTTP {response.status_code}", warning=True)
            
            return True
            
        except Exception as e:
            self.add_result("Data Integrity", False, f"Test failed: {e}")
            return False
    
    def run_all_tests(self) -> Dict:
        """Run all validation tests"""
        print_info("Starting comprehensive deployment validation...")
        print_info(f"Backend URL: {BACKEND_URL}")
        print_info(f"Frontend URL: {FRONTEND_URL}")
        
        # Run all test categories
        self.test_service_health()
        self.test_api_endpoints()
        self.test_frontend_accessibility()
        self.test_security_features()
        self.test_performance()
        self.test_phase7_features()
        self.test_data_integrity()
        
        return self.results
    
    def generate_report(self) -> str:
        """Generate validation report"""
        total_tests = self.results['passed'] + self.results['failed'] + self.results['warnings']
        
        report = f"""
{Colors.BLUE}📊 Deployment Validation Report{Colors.NC}
{'='*50}

Summary:
- Total Tests: {total_tests}
- Passed: {Colors.GREEN}{self.results['passed']}{Colors.NC}
- Failed: {Colors.RED}{self.results['failed']}{Colors.NC}
- Warnings: {Colors.YELLOW}{self.results['warnings']}{Colors.NC}

Success Rate: {(self.results['passed'] / total_tests * 100):.1f}%

Detailed Results:
"""
        
        for test in self.results['tests']:
            status_icon = "✅" if test['passed'] else ("⚠️" if test['warning'] else "❌")
            report += f"  {status_icon} {test['name']}: {test['message']}\n"
        
        # Overall assessment
        if self.results['failed'] == 0:
            if self.results['warnings'] == 0:
                report += f"\n{Colors.GREEN}🎉 Deployment validation PASSED - All systems operational!{Colors.NC}\n"
            else:
                report += f"\n{Colors.YELLOW}⚠️  Deployment validation PASSED with warnings - Review warning items{Colors.NC}\n"
        else:
            report += f"\n{Colors.RED}❌ Deployment validation FAILED - {self.results['failed']} critical issues found{Colors.NC}\n"
        
        return report


def main():
    """Main validation function"""
    validator = DeploymentValidator()
    
    print(f"{Colors.BLUE}🚀 Web3 Gaming News Tracker - Deployment Validation{Colors.NC}")
    print(f"Timestamp: {datetime.now().isoformat()}")
    
    # Run validation
    results = validator.run_all_tests()
    
    # Generate and display report
    report = validator.generate_report()
    print(report)
    
    # Save results to file
    with open('deployment/validation_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print_info("Validation results saved to deployment/validation_results.json")
    
    # Exit with appropriate code
    if results['failed'] > 0:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()
