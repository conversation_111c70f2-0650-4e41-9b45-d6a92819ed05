{"passed": 10, "failed": 6, "warnings": 1, "tests": [{"name": "Backend Health", "passed": true, "message": "Status: healthy", "warning": false, "timestamp": "2025-07-08T00:10:10.674191"}, {"name": "Backend Health", "passed": false, "message": "Connection failed: 'str' object has no attribute 'get'", "warning": false, "timestamp": "2025-07-08T00:10:10.674215"}, {"name": "API Documentation Endpoint", "passed": true, "message": "HTTP 200", "warning": false, "timestamp": "2025-07-08T00:10:10.687360"}, {"name": "Prometheus Metrics Endpoint", "passed": true, "message": "HTTP 200", "warning": false, "timestamp": "2025-07-08T00:10:10.788493"}, {"name": "Gaming Projects Endpoint", "passed": true, "message": "HTTP 200", "warning": false, "timestamp": "2025-07-08T00:10:10.823056"}, {"name": "Content Intelligence Endpoint", "passed": false, "message": "HTTP 401", "warning": false, "timestamp": "2025-07-08T00:10:10.827586"}, {"name": "Blockchain Data Endpoint", "passed": true, "message": "HTTP 200", "warning": false, "timestamp": "2025-07-08T00:10:10.839150"}, {"name": "Social Media Endpoint", "passed": false, "message": "HTTP 404", "warning": false, "timestamp": "2025-07-08T00:10:10.849995"}, {"name": "Frontend Accessibility", "passed": true, "message": "Frontend is serving content", "warning": false, "timestamp": "2025-07-08T00:10:10.899694"}, {"name": "Security Headers", "passed": true, "message": "3/3 headers present", "warning": false, "timestamp": "2025-07-08T00:10:10.908015"}, {"name": "CORS Configuration", "passed": false, "message": "CORS headers missing", "warning": true, "timestamp": "2025-07-08T00:10:10.911265"}, {"name": "Performance /health", "passed": true, "message": "Response time: 0.01s", "warning": false, "timestamp": "2025-07-08T00:10:10.917748"}, {"name": "Performance /api/v1/gaming/projects", "passed": true, "message": "Response time: 0.00s", "warning": false, "timestamp": "2025-07-08T00:10:10.920678"}, {"name": "Content Intelligence", "passed": false, "message": "HTTP 401", "warning": false, "timestamp": "2025-07-08T00:10:10.922747"}, {"name": "Market Analytics", "passed": false, "message": "HTTP 401", "warning": false, "timestamp": "2025-07-08T00:10:10.925555"}, {"name": "Dashboard Analytics", "passed": false, "message": "HTTP 401", "warning": false, "timestamp": "2025-07-08T00:10:10.927645"}, {"name": "Gaming Projects Data", "passed": true, "message": "Data structure valid (dict)", "warning": false, "timestamp": "2025-07-08T00:10:10.930107"}]}