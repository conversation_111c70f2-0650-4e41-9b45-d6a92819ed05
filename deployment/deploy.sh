#!/bin/bash

# Web3 Gaming News Tracker - Deployment Script
# This script handles the complete deployment of the application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_PORT=8000
FRONTEND_PORT=3000
ENVIRONMENT=${1:-development}

echo -e "${BLUE}🚀 Starting Web3 Gaming News Tracker Deployment${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo -e "${BLUE}Project Root: ${PROJECT_ROOT}${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 1
    else
        return 0
    fi
}

# Function to wait for service
wait_for_service() {
    local url=$1
    local timeout=${2:-30}
    local count=0
    
    print_info "Waiting for service at $url..."
    while [ $count -lt $timeout ]; do
        if curl -s "$url" >/dev/null 2>&1; then
            print_status "Service is ready at $url"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done
    
    print_error "Service at $url did not become ready within $timeout seconds"
    return 1
}

# Pre-deployment checks
echo -e "\n${BLUE}📋 Pre-deployment Checks${NC}"

# Check required commands
print_info "Checking required commands..."
required_commands=("python3" "pip" "node" "npm" "docker" "docker-compose")
for cmd in "${required_commands[@]}"; do
    if command_exists "$cmd"; then
        print_status "$cmd is installed"
    else
        print_error "$cmd is not installed"
        exit 1
    fi
done

# Check Python version
python_version=$(python3 --version | cut -d' ' -f2)
print_info "Python version: $python_version"

# Check Node.js version
node_version=$(node --version)
print_info "Node.js version: $node_version"

# Check if ports are available
print_info "Checking port availability..."
if check_port $BACKEND_PORT; then
    print_status "Port $BACKEND_PORT is available"
else
    print_warning "Port $BACKEND_PORT is in use"
fi

if check_port $FRONTEND_PORT; then
    print_status "Port $FRONTEND_PORT is available"
else
    print_warning "Port $FRONTEND_PORT is in use"
fi

# Check environment file
if [ -f "$PROJECT_ROOT/.env" ]; then
    print_status ".env file exists"
else
    print_error ".env file not found"
    print_info "Please create .env file with required configuration"
    exit 1
fi

# Database and Redis setup
echo -e "\n${BLUE}🗄️  Database and Redis Setup${NC}"

print_info "Starting Docker containers..."
cd "$PROJECT_ROOT"

# Start PostgreSQL and Redis containers
if docker-compose up -d postgres_gaming redis_gaming; then
    print_status "Docker containers started"
else
    print_error "Failed to start Docker containers"
    exit 1
fi

# Wait for PostgreSQL to be ready
print_info "Waiting for PostgreSQL to be ready..."
sleep 10

# Backend deployment
echo -e "\n${BLUE}🐍 Backend Deployment${NC}"

print_info "Installing Python dependencies..."
if pip install -r requirements.txt; then
    print_status "Python dependencies installed"
else
    print_error "Failed to install Python dependencies"
    exit 1
fi

print_info "Running database migrations..."
if python -m alembic upgrade head; then
    print_status "Database migrations completed"
else
    print_error "Database migrations failed"
    exit 1
fi

print_info "Starting FastAPI application..."
# Start backend in background
nohup python -m uvicorn api.main:app --host 0.0.0.0 --port $BACKEND_PORT > logs/backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > deployment/backend.pid

# Wait for backend to be ready
if wait_for_service "http://localhost:$BACKEND_PORT/health" 30; then
    print_status "Backend is running (PID: $BACKEND_PID)"
else
    print_error "Backend failed to start"
    exit 1
fi

# Frontend deployment
echo -e "\n${BLUE}⚛️  Frontend Deployment${NC}"

cd "$PROJECT_ROOT/dashboard/frontend"

print_info "Installing Node.js dependencies..."
if npm install; then
    print_status "Node.js dependencies installed"
else
    print_error "Failed to install Node.js dependencies"
    exit 1
fi

print_info "Building React application..."
if npm run build; then
    print_status "React application built successfully"
else
    print_error "React build failed"
    exit 1
fi

print_info "Starting React development server..."
# Start frontend in background
nohup npm start > ../../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../../deployment/frontend.pid

# Wait for frontend to be ready
if wait_for_service "http://localhost:$FRONTEND_PORT" 60; then
    print_status "Frontend is running (PID: $FRONTEND_PID)"
else
    print_error "Frontend failed to start"
    exit 1
fi

# Post-deployment testing
echo -e "\n${BLUE}🧪 Post-deployment Testing${NC}"

cd "$PROJECT_ROOT"

print_info "Running health checks..."

# Test backend health
if curl -s "http://localhost:$BACKEND_PORT/health" | grep -q "healthy"; then
    print_status "Backend health check passed"
else
    print_error "Backend health check failed"
fi

# Test API endpoints
print_info "Testing API endpoints..."
endpoints=(
    "/health"
    "/metrics"
    "/api/v1/gaming/projects"
    "/api/v1/content-intelligence/health"
)

for endpoint in "${endpoints[@]}"; do
    if curl -s "http://localhost:$BACKEND_PORT$endpoint" >/dev/null; then
        print_status "Endpoint $endpoint is accessible"
    else
        print_warning "Endpoint $endpoint is not accessible"
    fi
done

# Test frontend
print_info "Testing frontend..."
if curl -s "http://localhost:$FRONTEND_PORT" | grep -q "Web3 Gaming"; then
    print_status "Frontend is serving content"
else
    print_warning "Frontend content check failed"
fi

# Test database connectivity
print_info "Testing database connectivity..."
if python -c "
from models.base import engine
from sqlalchemy import text
try:
    with engine.connect() as conn:
        conn.execute(text('SELECT 1'))
    print('Database connection successful')
except Exception as e:
    print(f'Database connection failed: {e}')
    exit(1)
"; then
    print_status "Database connectivity test passed"
else
    print_error "Database connectivity test failed"
fi

# Test Redis connectivity
print_info "Testing Redis connectivity..."
if python -c "
import redis
try:
    r = redis.Redis(host='localhost', port=6379, db=0)
    r.ping()
    print('Redis connection successful')
except Exception as e:
    print(f'Redis connection failed: {e}')
    exit(1)
"; then
    print_status "Redis connectivity test passed"
else
    print_error "Redis connectivity test failed"
fi

# Security validation
echo -e "\n${BLUE}🔒 Security Validation${NC}"

print_info "Testing security headers..."
if curl -s -I "http://localhost:$BACKEND_PORT/health" | grep -q "X-Content-Type-Options"; then
    print_status "Security headers are present"
else
    print_warning "Security headers check failed"
fi

print_info "Testing rate limiting..."
# Make multiple requests to test rate limiting
for i in {1..5}; do
    curl -s "http://localhost:$BACKEND_PORT/health" >/dev/null
done
print_status "Rate limiting test completed"

# Performance validation
echo -e "\n${BLUE}⚡ Performance Validation${NC}"

print_info "Testing API response times..."
response_time=$(curl -o /dev/null -s -w '%{time_total}' "http://localhost:$BACKEND_PORT/health")
if (( $(echo "$response_time < 2.0" | bc -l) )); then
    print_status "API response time: ${response_time}s (< 2s)"
else
    print_warning "API response time: ${response_time}s (>= 2s)"
fi

# Deployment summary
echo -e "\n${BLUE}📊 Deployment Summary${NC}"

print_status "Backend running on http://localhost:$BACKEND_PORT"
print_status "Frontend running on http://localhost:$FRONTEND_PORT"
print_status "API Documentation: http://localhost:$BACKEND_PORT/docs"
print_status "Metrics: http://localhost:$BACKEND_PORT/metrics"

echo -e "\n${GREEN}🎉 Deployment completed successfully!${NC}"

# Save deployment info
cat > deployment/deployment_info.txt << EOF
Deployment Date: $(date)
Environment: $ENVIRONMENT
Backend PID: $BACKEND_PID
Frontend PID: $FRONTEND_PID
Backend URL: http://localhost:$BACKEND_PORT
Frontend URL: http://localhost:$FRONTEND_PORT
EOF

print_info "Deployment information saved to deployment/deployment_info.txt"

# Instructions for stopping services
echo -e "\n${BLUE}🛑 To stop services:${NC}"
echo "Backend: kill $BACKEND_PID"
echo "Frontend: kill $FRONTEND_PID"
echo "Docker: docker-compose down"
echo "Or run: ./deployment/stop.sh"

exit 0
