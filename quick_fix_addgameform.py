#!/usr/bin/env python3
"""
Quick fix for AddGameForm.jsx broken onChange handlers
"""

import re

def fix_addgameform():
    file_path = "dashboard/frontend/src/components/AddGameForm.jsx"
    
    # Read the file
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix broken onChange patterns
    # Pattern: onChange={(e) =\n                  sx={textFieldSx}\n                > handleChange(
    pattern1 = r'onChange=\{\(e\) =\s*\n\s*sx=\{textFieldSx\}\s*\n\s*>\s*handleChange\('
    replacement1 = r'onChange={(e) => handleChange('
    
    content = re.sub(pattern1, replacement1, content, flags=re.MULTILINE)
    
    # Remove duplicate sx props
    content = re.sub(r'sx=\{textFieldSx\}\s*\n\s*sx=\{textFieldSx\}', 'sx={textFieldSx}', content)
    
    # Write back to file
    with open(file_path, 'w') as f:
        f.write(content)
    
    print("✅ Fixed AddGameForm.jsx onChange handlers")

if __name__ == "__main__":
    fix_addgameform()
