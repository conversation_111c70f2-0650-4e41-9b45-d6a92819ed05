#!/usr/bin/env python3
"""
Generate API documentation without importing the full FastAPI app
"""
import json
import yaml
from pathlib import Path
from datetime import datetime


def create_openapi_spec():
    """Create OpenAPI specification manually"""
    
    openapi_spec = {
        "openapi": "3.0.2",
        "info": {
            "title": "Web3 Gaming News Tracker API",
            "description": """
Comprehensive API for accessing web3 gaming news, blockchain data, content intelligence, and market analytics.

## Features

* **Gaming News**: Access to curated gaming articles from multiple sources
* **Blockchain Integration**: Real-time blockchain data from multiple networks
* **Content Intelligence**: Advanced NLP analysis and sentiment scoring
* **Market Analytics**: Sector analysis and investment tracking
* **Competitive Analysis**: Project comparison and market positioning

## Phase 7 Enhancements

* **Advanced NLP Classification**: 11-category gaming content classification
* **Multi-dimensional Sentiment Analysis**: Community, market, and technical sentiment
* **Trend Detection**: Emerging and declining trend identification
* **Market Intelligence**: Investment signals and risk assessment
* **Entity Recognition**: Gaming project and token extraction
* **Competitive Metrics**: 8-metric competitive analysis framework

## Authentication

Currently in development mode. Production will include API key authentication and rate limiting.

## Rate Limits

* General API: 1000 requests/hour
* Content Intelligence: 100 requests/hour  
* Market Analytics: 200 requests/hour
* Blockchain Data: 500 requests/hour
            """,
            "version": "1.0.0",
            "contact": {
                "name": "Web3 Gaming Tracker API Support",
                "email": "<EMAIL>"
            },
            "license": {
                "name": "MIT License",
                "url": "https://opensource.org/licenses/MIT"
            }
        },
        "servers": [
            {
                "url": "http://localhost:8000",
                "description": "Development server"
            },
            {
                "url": "https://api.web3gaming-tracker.com",
                "description": "Production server"
            }
        ],
        "paths": {
            "/health": {
                "get": {
                    "tags": ["System"],
                    "summary": "Health Check",
                    "description": "Check system health and status",
                    "responses": {
                        "200": {
                            "description": "System is healthy",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "status": {"type": "string"},
                                            "timestamp": {"type": "string"},
                                            "version": {"type": "string"}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/v1/content-intelligence/classify": {
                "post": {
                    "tags": ["Content Intelligence"],
                    "summary": "Classify Gaming Content",
                    "description": "Classify gaming content using advanced NLP analysis",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "title": {"type": "string"},
                                        "content": {"type": "string"},
                                        "summary": {"type": "string"},
                                        "source": {"type": "string"}
                                    },
                                    "required": ["title"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Content classification result",
                            "content": {
                                "application/json": {
                                    "schema": {
                                        "type": "object",
                                        "properties": {
                                            "primary_category": {"type": "string"},
                                            "category_confidence": {"type": "number"},
                                            "sentiment_score": {"type": "number"},
                                            "gaming_entities": {"type": "array", "items": {"type": "string"}},
                                            "blockchain_networks": {"type": "array", "items": {"type": "string"}}
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            "/api/v1/content-intelligence/sentiment": {
                "post": {
                    "tags": ["Content Intelligence"],
                    "summary": "Analyze Sentiment",
                    "description": "Analyze gaming community sentiment with enhanced scoring",
                    "requestBody": {
                        "required": True,
                        "content": {
                            "application/json": {
                                "schema": {
                                    "type": "object",
                                    "properties": {
                                        "title": {"type": "string"},
                                        "content": {"type": "string"},
                                        "summary": {"type": "string"}
                                    },
                                    "required": ["title"]
                                }
                            }
                        }
                    },
                    "responses": {
                        "200": {
                            "description": "Sentiment analysis result"
                        }
                    }
                }
            },
            "/api/v1/content-intelligence/market/sector-analysis": {
                "get": {
                    "tags": ["Market Analytics"],
                    "summary": "Sector Analysis",
                    "description": "Analyze gaming sector performance across protocols",
                    "parameters": [
                        {
                            "name": "timeframe",
                            "in": "query",
                            "schema": {"type": "string", "enum": ["24h", "7d", "30d"]},
                            "description": "Analysis timeframe"
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Sector analysis results"
                        }
                    }
                }
            },
            "/api/v1/content-intelligence/competitive/landscape": {
                "get": {
                    "tags": ["Competitive Analysis"],
                    "summary": "Competitive Landscape",
                    "description": "Analyze competitive landscape of gaming projects",
                    "parameters": [
                        {
                            "name": "category",
                            "in": "query",
                            "schema": {"type": "string"},
                            "description": "Filter by game category"
                        }
                    ],
                    "responses": {
                        "200": {
                            "description": "Competitive landscape analysis"
                        }
                    }
                }
            }
        },
        "components": {
            "schemas": {
                "Error": {
                    "type": "object",
                    "properties": {
                        "error": {"type": "boolean"},
                        "error_id": {"type": "string"},
                        "category": {"type": "string"},
                        "severity": {"type": "string"},
                        "message": {"type": "string"},
                        "timestamp": {"type": "string"}
                    }
                }
            }
        },
        "tags": [
            {
                "name": "System",
                "description": "System health and status endpoints"
            },
            {
                "name": "Content Intelligence",
                "description": "Phase 7: Advanced NLP and sentiment analysis"
            },
            {
                "name": "Market Analytics",
                "description": "Phase 7: Market intelligence and sector analysis"
            },
            {
                "name": "Competitive Analysis",
                "description": "Phase 7: Project comparison and competitive metrics"
            }
        ]
    }
    
    return openapi_spec


def export_documentation():
    """Export API documentation in multiple formats"""
    
    print("🚀 Generating Web3 Gaming News Tracker API Documentation")
    print("=" * 60)
    
    # Create docs directory
    docs_dir = Path("docs")
    docs_dir.mkdir(exist_ok=True)
    
    # Generate OpenAPI spec
    openapi_spec = create_openapi_spec()
    
    # Export as JSON
    json_path = docs_dir / "openapi.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(openapi_spec, f, indent=2, ensure_ascii=False)
    
    print(f"✅ OpenAPI JSON specification exported to: {json_path}")
    
    # Export as YAML
    yaml_path = docs_dir / "openapi.yaml"
    with open(yaml_path, 'w', encoding='utf-8') as f:
        yaml.dump(openapi_spec, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ OpenAPI YAML specification exported to: {yaml_path}")
    
    # Generate Postman collection
    postman_collection = {
        "info": {
            "name": "Web3 Gaming News Tracker API",
            "description": "API collection for testing Web3 Gaming News Tracker endpoints",
            "version": "1.0.0",
            "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
        },
        "variable": [
            {
                "key": "baseUrl",
                "value": "http://localhost:8000",
                "type": "string"
            }
        ],
        "item": [
            {
                "name": "Health Check",
                "request": {
                    "method": "GET",
                    "header": [],
                    "url": {
                        "raw": "{{baseUrl}}/health",
                        "host": ["{{baseUrl}}"],
                        "path": ["health"]
                    }
                }
            },
            {
                "name": "Content Classification",
                "request": {
                    "method": "POST",
                    "header": [
                        {
                            "key": "Content-Type",
                            "value": "application/json"
                        }
                    ],
                    "body": {
                        "mode": "raw",
                        "raw": json.dumps({
                            "title": "New Gaming Project Launches on Solana",
                            "content": "A revolutionary new gaming project has launched...",
                            "summary": "Gaming project launch announcement"
                        }, indent=2)
                    },
                    "url": {
                        "raw": "{{baseUrl}}/api/v1/content-intelligence/classify",
                        "host": ["{{baseUrl}}"],
                        "path": ["api", "v1", "content-intelligence", "classify"]
                    }
                }
            },
            {
                "name": "Sentiment Analysis",
                "request": {
                    "method": "POST",
                    "header": [
                        {
                            "key": "Content-Type",
                            "value": "application/json"
                        }
                    ],
                    "body": {
                        "mode": "raw",
                        "raw": json.dumps({
                            "title": "Gaming Community Reaction",
                            "content": "The gaming community is excited about the new features..."
                        }, indent=2)
                    },
                    "url": {
                        "raw": "{{baseUrl}}/api/v1/content-intelligence/sentiment",
                        "host": ["{{baseUrl}}"],
                        "path": ["api", "v1", "content-intelligence", "sentiment"]
                    }
                }
            }
        ]
    }
    
    # Export Postman collection
    postman_path = docs_dir / "postman_collection.json"
    with open(postman_path, 'w', encoding='utf-8') as f:
        json.dump(postman_collection, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Postman collection exported to: {postman_path}")
    
    # Print summary
    print("\n📊 API Documentation Summary:")
    print(f"Title: {openapi_spec['info']['title']}")
    print(f"Version: {openapi_spec['info']['version']}")
    print(f"Total Paths: {len(openapi_spec['paths'])}")
    print(f"Total Tags: {len(openapi_spec['tags'])}")
    
    print("\n🏷️  API Categories:")
    for tag in openapi_spec['tags']:
        print(f"  {tag['name']}: {tag['description']}")
    
    print("\n📁 Generated Files:")
    print("  📄 docs/openapi.json - OpenAPI JSON specification")
    print("  📄 docs/openapi.yaml - OpenAPI YAML specification") 
    print("  📄 docs/postman_collection.json - Postman collection")
    print("  📄 docs/api_documentation.md - Human-readable documentation")
    
    print("\n🌐 Access Documentation:")
    print("  📖 Swagger UI: http://localhost:8000/docs")
    print("  📖 ReDoc: http://localhost:8000/redoc")
    
    print("\n✅ API documentation generation completed successfully!")


if __name__ == "__main__":
    export_documentation()
