#!/usr/bin/env python3
"""
Comprehensive Log Management System

This script provides advanced log management capabilities including:
- Log rotation and archival
- Log analysis and monitoring
- Log cleanup and retention policies
- Log aggregation and compression
- Performance monitoring and alerting
"""

import os
import sys
import gzip
import shutil
import argparse
import logging
import json
import re
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from collections import defaultdict, Counter
import subprocess

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config.settings import get_settings

# Configure logging for this script
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LogManager:
    """Comprehensive log management system"""
    
    def __init__(self, log_directory: str = "logs"):
        self.log_dir = Path(log_directory)
        self.log_dir.mkdir(exist_ok=True)
        self.archive_dir = self.log_dir / "archive"
        self.archive_dir.mkdir(exist_ok=True)
        
        # Default retention policies (days)
        self.retention_policies = {
            'debug': 7,      # Debug logs kept for 1 week
            'info': 30,      # Info logs kept for 1 month
            'warning': 90,   # Warning logs kept for 3 months
            'error': 365,    # Error logs kept for 1 year
            'critical': 730  # Critical logs kept for 2 years
        }
        
        # Log file patterns
        self.log_patterns = {
            'main': r'web3_gaming_tracker\.log(\.\d+)?',
            'error': r'web3_gaming_tracker_errors\.log(\.\d+)?',
            'migration': r'migration_manager\.log(\.\d+)?',
            'api': r'api_.*\.log(\.\d+)?',
            'blockchain': r'blockchain_.*\.log(\.\d+)?',
            'scraper': r'scraper_.*\.log(\.\d+)?'
        }
    
    def get_log_files(self, pattern: Optional[str] = None) -> List[Path]:
        """Get all log files matching pattern"""
        if pattern:
            regex = re.compile(pattern)
            return [f for f in self.log_dir.glob("*.log*") if regex.match(f.name)]
        else:
            return list(self.log_dir.glob("*.log*"))
    
    def analyze_log_file(self, log_file: Path) -> Dict[str, any]:
        """Analyze log file for metrics and issues"""
        analysis = {
            'file': str(log_file),
            'size_mb': round(log_file.stat().st_size / (1024 * 1024), 2),
            'modified': datetime.fromtimestamp(log_file.stat().st_mtime).isoformat(),
            'line_count': 0,
            'log_levels': Counter(),
            'error_patterns': Counter(),
            'performance_issues': [],
            'recent_errors': [],
            'services': Counter()
        }
        
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line_num, line in enumerate(f, 1):
                    analysis['line_count'] = line_num
                    
                    # Parse JSON logs
                    try:
                        if line.strip().startswith('{'):
                            log_entry = json.loads(line.strip())
                            
                            # Count log levels
                            level = log_entry.get('level', 'UNKNOWN')
                            analysis['log_levels'][level] += 1
                            
                            # Track services
                            service = log_entry.get('service', 'unknown')
                            analysis['services'][service] += 1
                            
                            # Check for errors
                            if level in ['ERROR', 'CRITICAL']:
                                timestamp = log_entry.get('timestamp', '')
                                message = log_entry.get('message', '')
                                
                                # Keep recent errors (last 24 hours)
                                try:
                                    log_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                                    if datetime.now() - log_time.replace(tzinfo=None) < timedelta(hours=24):
                                        analysis['recent_errors'].append({
                                            'timestamp': timestamp,
                                            'level': level,
                                            'message': message[:200],
                                            'service': service
                                        })
                                except:
                                    pass
                                
                                # Count error patterns
                                error_type = self._classify_error(message)
                                analysis['error_patterns'][error_type] += 1
                            
                            # Check for performance issues
                            response_time = log_entry.get('response_time_ms', 0)
                            if response_time > 5000:  # > 5 seconds
                                analysis['performance_issues'].append({
                                    'timestamp': log_entry.get('timestamp'),
                                    'response_time_ms': response_time,
                                    'endpoint': log_entry.get('endpoint', 'unknown')
                                })
                    
                    except json.JSONDecodeError:
                        # Handle non-JSON log lines
                        if 'ERROR' in line:
                            analysis['log_levels']['ERROR'] += 1
                        elif 'WARNING' in line:
                            analysis['log_levels']['WARNING'] += 1
                        elif 'INFO' in line:
                            analysis['log_levels']['INFO'] += 1
                        elif 'DEBUG' in line:
                            analysis['log_levels']['DEBUG'] += 1
        
        except Exception as e:
            analysis['error'] = f"Failed to analyze file: {e}"
        
        return analysis
    
    def _classify_error(self, message: str) -> str:
        """Classify error message into categories"""
        message_lower = message.lower()
        
        if 'database' in message_lower or 'sql' in message_lower:
            return 'database_error'
        elif 'blockchain' in message_lower or 'rpc' in message_lower:
            return 'blockchain_error'
        elif 'api' in message_lower or 'http' in message_lower:
            return 'api_error'
        elif 'scraper' in message_lower or 'scraping' in message_lower:
            return 'scraper_error'
        elif 'timeout' in message_lower:
            return 'timeout_error'
        elif 'connection' in message_lower:
            return 'connection_error'
        else:
            return 'general_error'
    
    def rotate_logs(self, max_size_mb: int = 100) -> Dict[str, List[str]]:
        """Rotate logs that exceed size limit"""
        rotated_files = []
        errors = []
        
        for log_file in self.get_log_files():
            if log_file.suffix == '.log':  # Only rotate active log files
                size_mb = log_file.stat().st_size / (1024 * 1024)
                
                if size_mb > max_size_mb:
                    try:
                        # Find next rotation number
                        rotation_num = 1
                        while (log_file.parent / f"{log_file.name}.{rotation_num}").exists():
                            rotation_num += 1
                        
                        # Rotate the file
                        rotated_name = f"{log_file.name}.{rotation_num}"
                        rotated_path = log_file.parent / rotated_name
                        
                        shutil.move(str(log_file), str(rotated_path))
                        
                        # Create new empty log file
                        log_file.touch()
                        
                        rotated_files.append(str(rotated_path))
                        logger.info(f"Rotated log file: {log_file} -> {rotated_path}")
                        
                    except Exception as e:
                        error_msg = f"Failed to rotate {log_file}: {e}"
                        errors.append(error_msg)
                        logger.error(error_msg)
        
        return {'rotated': rotated_files, 'errors': errors}
    
    def archive_old_logs(self, days_old: int = 7) -> Dict[str, List[str]]:
        """Archive logs older than specified days"""
        archived_files = []
        errors = []
        cutoff_date = datetime.now() - timedelta(days=days_old)
        
        for log_file in self.get_log_files():
            if log_file.suffix != '.log':  # Only archive rotated logs
                file_modified = datetime.fromtimestamp(log_file.stat().st_mtime)
                
                if file_modified < cutoff_date:
                    try:
                        # Compress and move to archive
                        archive_name = f"{log_file.name}.gz"
                        archive_path = self.archive_dir / archive_name
                        
                        with open(log_file, 'rb') as f_in:
                            with gzip.open(archive_path, 'wb') as f_out:
                                shutil.copyfileobj(f_in, f_out)
                        
                        # Remove original file
                        log_file.unlink()
                        
                        archived_files.append(str(archive_path))
                        logger.info(f"Archived log file: {log_file} -> {archive_path}")
                        
                    except Exception as e:
                        error_msg = f"Failed to archive {log_file}: {e}"
                        errors.append(error_msg)
                        logger.error(error_msg)
        
        return {'archived': archived_files, 'errors': errors}
    
    def cleanup_old_archives(self, retention_days: int = 365) -> Dict[str, List[str]]:
        """Clean up archived logs older than retention period"""
        deleted_files = []
        errors = []
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        
        for archive_file in self.archive_dir.glob("*.gz"):
            file_modified = datetime.fromtimestamp(archive_file.stat().st_mtime)
            
            if file_modified < cutoff_date:
                try:
                    archive_file.unlink()
                    deleted_files.append(str(archive_file))
                    logger.info(f"Deleted old archive: {archive_file}")
                    
                except Exception as e:
                    error_msg = f"Failed to delete {archive_file}: {e}"
                    errors.append(error_msg)
                    logger.error(error_msg)
        
        return {'deleted': deleted_files, 'errors': errors}
    
    def generate_log_report(self) -> Dict[str, any]:
        """Generate comprehensive log analysis report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'log_directory': str(self.log_dir),
            'total_files': 0,
            'total_size_mb': 0,
            'file_analysis': [],
            'summary': {
                'total_log_levels': Counter(),
                'total_services': Counter(),
                'total_error_patterns': Counter(),
                'recent_errors_count': 0,
                'performance_issues_count': 0
            },
            'recommendations': []
        }
        
        # Analyze all log files
        for log_file in self.get_log_files():
            analysis = self.analyze_log_file(log_file)
            report['file_analysis'].append(analysis)
            
            # Update totals
            report['total_files'] += 1
            report['total_size_mb'] += analysis['size_mb']
            
            # Aggregate summary data
            for level, count in analysis['log_levels'].items():
                report['summary']['total_log_levels'][level] += count
            
            for service, count in analysis['services'].items():
                report['summary']['total_services'][service] += count
            
            for error_type, count in analysis['error_patterns'].items():
                report['summary']['total_error_patterns'][error_type] += count
            
            report['summary']['recent_errors_count'] += len(analysis['recent_errors'])
            report['summary']['performance_issues_count'] += len(analysis['performance_issues'])
        
        # Generate recommendations
        if report['total_size_mb'] > 1000:  # > 1GB
            report['recommendations'].append("Consider implementing more aggressive log rotation")
        
        if report['summary']['total_error_patterns']['database_error'] > 100:
            report['recommendations'].append("High number of database errors detected - investigate database health")
        
        if report['summary']['performance_issues_count'] > 50:
            report['recommendations'].append("Multiple performance issues detected - review API response times")
        
        if report['summary']['recent_errors_count'] > 20:
            report['recommendations'].append("High error rate in last 24 hours - immediate attention required")
        
        return report
    
    def monitor_log_health(self) -> Dict[str, any]:
        """Monitor log health and detect issues"""
        health_status = {
            'status': 'healthy',
            'issues': [],
            'warnings': [],
            'metrics': {}
        }
        
        # Check disk space
        disk_usage = shutil.disk_usage(self.log_dir)
        free_space_gb = disk_usage.free / (1024**3)
        
        if free_space_gb < 1:  # Less than 1GB free
            health_status['issues'].append(f"Low disk space: {free_space_gb:.2f}GB free")
            health_status['status'] = 'critical'
        elif free_space_gb < 5:  # Less than 5GB free
            health_status['warnings'].append(f"Disk space warning: {free_space_gb:.2f}GB free")
            health_status['status'] = 'warning'
        
        health_status['metrics']['free_space_gb'] = round(free_space_gb, 2)
        
        # Check log file sizes
        large_files = []
        for log_file in self.get_log_files():
            size_mb = log_file.stat().st_size / (1024 * 1024)
            if size_mb > 100:  # Files larger than 100MB
                large_files.append(f"{log_file.name}: {size_mb:.1f}MB")
        
        if large_files:
            health_status['warnings'].extend([f"Large log file: {f}" for f in large_files])
            if health_status['status'] == 'healthy':
                health_status['status'] = 'warning'
        
        # Check for recent errors
        report = self.generate_log_report()
        recent_errors = report['summary']['recent_errors_count']
        
        if recent_errors > 50:
            health_status['issues'].append(f"High error rate: {recent_errors} errors in last 24 hours")
            health_status['status'] = 'critical'
        elif recent_errors > 20:
            health_status['warnings'].append(f"Elevated error rate: {recent_errors} errors in last 24 hours")
            if health_status['status'] == 'healthy':
                health_status['status'] = 'warning'
        
        health_status['metrics']['recent_errors'] = recent_errors
        
        return health_status


def main():
    """CLI interface for log management"""
    parser = argparse.ArgumentParser(description="Log Management System")
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze log files')
    analyze_parser.add_argument('--file', help='Specific log file to analyze')
    analyze_parser.add_argument('--output', help='Output file for analysis report')
    
    # Rotate command
    rotate_parser = subparsers.add_parser('rotate', help='Rotate large log files')
    rotate_parser.add_argument('--max-size', type=int, default=100, help='Max size in MB before rotation')
    
    # Archive command
    archive_parser = subparsers.add_parser('archive', help='Archive old log files')
    archive_parser.add_argument('--days', type=int, default=7, help='Archive files older than N days')
    
    # Cleanup command
    cleanup_parser = subparsers.add_parser('cleanup', help='Clean up old archives')
    cleanup_parser.add_argument('--retention', type=int, default=365, help='Retention period in days')
    
    # Report command
    subparsers.add_parser('report', help='Generate comprehensive log report')
    
    # Health command
    subparsers.add_parser('health', help='Check log system health')
    
    # Maintenance command
    maintenance_parser = subparsers.add_parser('maintenance', help='Run full maintenance cycle')
    maintenance_parser.add_argument('--rotate-size', type=int, default=100, help='Rotation size in MB')
    maintenance_parser.add_argument('--archive-days', type=int, default=7, help='Archive age in days')
    maintenance_parser.add_argument('--retention-days', type=int, default=365, help='Retention period in days')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    log_manager = LogManager()
    
    if args.command == 'analyze':
        if args.file:
            analysis = log_manager.analyze_log_file(Path(args.file))
            result = {'file_analysis': analysis}
        else:
            result = log_manager.generate_log_report()
        
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(result, f, indent=2, default=str)
            print(f"Analysis saved to: {args.output}")
        else:
            print(json.dumps(result, indent=2, default=str))
    
    elif args.command == 'rotate':
        result = log_manager.rotate_logs(args.max_size)
        print(f"Rotated {len(result['rotated'])} files")
        if result['errors']:
            print(f"Errors: {result['errors']}")
    
    elif args.command == 'archive':
        result = log_manager.archive_old_logs(args.days)
        print(f"Archived {len(result['archived'])} files")
        if result['errors']:
            print(f"Errors: {result['errors']}")
    
    elif args.command == 'cleanup':
        result = log_manager.cleanup_old_archives(args.retention)
        print(f"Deleted {len(result['deleted'])} old archives")
        if result['errors']:
            print(f"Errors: {result['errors']}")
    
    elif args.command == 'report':
        report = log_manager.generate_log_report()
        print(json.dumps(report, indent=2, default=str))
    
    elif args.command == 'health':
        health = log_manager.monitor_log_health()
        print(f"Status: {health['status']}")
        if health['issues']:
            print("Issues:")
            for issue in health['issues']:
                print(f"  - {issue}")
        if health['warnings']:
            print("Warnings:")
            for warning in health['warnings']:
                print(f"  - {warning}")
        print(f"Metrics: {health['metrics']}")
    
    elif args.command == 'maintenance':
        print("Running full log maintenance cycle...")
        
        # Rotate large files
        rotate_result = log_manager.rotate_logs(args.rotate_size)
        print(f"Rotated {len(rotate_result['rotated'])} files")
        
        # Archive old files
        archive_result = log_manager.archive_old_logs(args.archive_days)
        print(f"Archived {len(archive_result['archived'])} files")
        
        # Cleanup old archives
        cleanup_result = log_manager.cleanup_old_archives(args.retention_days)
        print(f"Deleted {len(cleanup_result['deleted'])} old archives")
        
        # Health check
        health = log_manager.monitor_log_health()
        print(f"Final status: {health['status']}")


if __name__ == "__main__":
    main()
