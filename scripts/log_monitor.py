#!/usr/bin/env python3
"""
Real-time Log Monitoring and Alerting System

This script provides real-time log monitoring with:
- Real-time log tailing and analysis
- Error pattern detection and alerting
- Performance monitoring and thresholds
- Automated incident response
- Integration with external monitoring systems
"""

import os
import sys
import time
import json
import asyncio
import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Callable
from collections import deque, Counter
import re
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config.settings import get_settings

# Configure logging for this script
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LogAlert:
    """Log alert configuration and handling"""
    
    def __init__(self, name: str, pattern: str, threshold: int = 1, 
                 time_window: int = 300, severity: str = "warning"):
        self.name = name
        self.pattern = re.compile(pattern, re.IGNORECASE)
        self.threshold = threshold
        self.time_window = time_window  # seconds
        self.severity = severity
        self.matches = deque()
        self.last_alert = None
        self.cooldown = 300  # 5 minutes between alerts
    
    def check_match(self, log_line: str, timestamp: datetime) -> bool:
        """Check if log line matches alert pattern"""
        if self.pattern.search(log_line):
            self.matches.append(timestamp)
            
            # Remove old matches outside time window
            cutoff = timestamp - timedelta(seconds=self.time_window)
            while self.matches and self.matches[0] < cutoff:
                self.matches.popleft()
            
            # Check if threshold exceeded
            if len(self.matches) >= self.threshold:
                # Check cooldown
                if (self.last_alert is None or 
                    timestamp - self.last_alert > timedelta(seconds=self.cooldown)):
                    self.last_alert = timestamp
                    return True
        
        return False
    
    def get_alert_data(self) -> Dict:
        """Get alert data for notification"""
        return {
            'name': self.name,
            'severity': self.severity,
            'threshold': self.threshold,
            'time_window': self.time_window,
            'match_count': len(self.matches),
            'last_alert': self.last_alert.isoformat() if self.last_alert else None
        }


class LogMonitor:
    """Real-time log monitoring system"""
    
    def __init__(self, log_directory: str = "logs"):
        self.log_dir = Path(log_directory)
        self.alerts = []
        self.metrics = {
            'lines_processed': 0,
            'alerts_triggered': 0,
            'errors_detected': 0,
            'start_time': datetime.now()
        }
        
        # Performance tracking
        self.response_times = deque(maxlen=1000)
        self.error_rates = deque(maxlen=100)
        
        # Setup default alerts
        self._setup_default_alerts()
        
        # Notification handlers
        self.notification_handlers = []
    
    def _setup_default_alerts(self):
        """Setup default alert configurations"""
        
        # Critical error alerts
        self.alerts.append(LogAlert(
            name="Critical Errors",
            pattern=r'"level":\s*"CRITICAL"',
            threshold=1,
            time_window=60,
            severity="critical"
        ))
        
        # High error rate
        self.alerts.append(LogAlert(
            name="High Error Rate",
            pattern=r'"level":\s*"ERROR"',
            threshold=10,
            time_window=300,
            severity="warning"
        ))
        
        # Database connection issues
        self.alerts.append(LogAlert(
            name="Database Connection Issues",
            pattern=r'database.*connection.*failed|connection.*database.*error',
            threshold=3,
            time_window=180,
            severity="critical"
        ))
        
        # Blockchain RPC failures
        self.alerts.append(LogAlert(
            name="Blockchain RPC Failures",
            pattern=r'rpc.*failed|blockchain.*connection.*error',
            threshold=5,
            time_window=300,
            severity="warning"
        ))
        
        # API performance issues
        self.alerts.append(LogAlert(
            name="Slow API Responses",
            pattern=r'"response_time_ms":\s*[5-9]\d{3,}',  # > 5000ms
            threshold=5,
            time_window=300,
            severity="warning"
        ))
        
        # Memory issues
        self.alerts.append(LogAlert(
            name="Memory Issues",
            pattern=r'memory.*error|out of memory|memory.*exceeded',
            threshold=1,
            time_window=60,
            severity="critical"
        ))
        
        # Authentication failures
        self.alerts.append(LogAlert(
            name="Authentication Failures",
            pattern=r'authentication.*failed|unauthorized|invalid.*token',
            threshold=10,
            time_window=300,
            severity="warning"
        ))
    
    def add_alert(self, alert: LogAlert):
        """Add custom alert configuration"""
        self.alerts.append(alert)
    
    def add_notification_handler(self, handler: Callable):
        """Add notification handler function"""
        self.notification_handlers.append(handler)
    
    async def process_log_line(self, line: str, timestamp: datetime):
        """Process a single log line for monitoring"""
        self.metrics['lines_processed'] += 1
        
        # Check all alerts
        for alert in self.alerts:
            if alert.check_match(line, timestamp):
                await self._trigger_alert(alert, line, timestamp)
        
        # Extract performance metrics
        try:
            if line.strip().startswith('{'):
                log_entry = json.loads(line.strip())
                
                # Track response times
                response_time = log_entry.get('response_time_ms')
                if response_time:
                    self.response_times.append(response_time)
                
                # Track error rates
                if log_entry.get('level') in ['ERROR', 'CRITICAL']:
                    self.metrics['errors_detected'] += 1
                    self.error_rates.append(timestamp)
        
        except json.JSONDecodeError:
            pass
    
    async def _trigger_alert(self, alert: LogAlert, log_line: str, timestamp: datetime):
        """Trigger alert and send notifications"""
        self.metrics['alerts_triggered'] += 1
        
        alert_data = {
            'alert': alert.get_alert_data(),
            'timestamp': timestamp.isoformat(),
            'log_line': log_line.strip(),
            'metrics': self.get_current_metrics()
        }
        
        logger.warning(f"ALERT TRIGGERED: {alert.name}")
        
        # Send notifications
        for handler in self.notification_handlers:
            try:
                await handler(alert_data)
            except Exception as e:
                logger.error(f"Notification handler failed: {e}")
    
    def get_current_metrics(self) -> Dict:
        """Get current monitoring metrics"""
        now = datetime.now()
        uptime = now - self.metrics['start_time']
        
        # Calculate error rate (errors per minute)
        recent_errors = [
            t for t in self.error_rates 
            if now - t < timedelta(minutes=5)
        ]
        error_rate = len(recent_errors) / 5  # errors per minute
        
        # Calculate average response time
        avg_response_time = (
            sum(self.response_times) / len(self.response_times)
            if self.response_times else 0
        )
        
        return {
            'uptime_seconds': uptime.total_seconds(),
            'lines_processed': self.metrics['lines_processed'],
            'alerts_triggered': self.metrics['alerts_triggered'],
            'errors_detected': self.metrics['errors_detected'],
            'error_rate_per_minute': round(error_rate, 2),
            'avg_response_time_ms': round(avg_response_time, 2),
            'active_alerts': len([a for a in self.alerts if a.matches])
        }
    
    async def tail_log_file(self, log_file: Path):
        """Tail a log file for real-time monitoring"""
        logger.info(f"Starting to monitor: {log_file}")
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                # Go to end of file
                f.seek(0, 2)
                
                while True:
                    line = f.readline()
                    if line:
                        await self.process_log_line(line, datetime.now())
                    else:
                        await asyncio.sleep(0.1)  # Wait for new content
        
        except FileNotFoundError:
            logger.error(f"Log file not found: {log_file}")
        except Exception as e:
            logger.error(f"Error monitoring {log_file}: {e}")
    
    async def monitor_all_logs(self):
        """Monitor all log files concurrently"""
        log_files = [
            self.log_dir / "web3_gaming_tracker.log",
            self.log_dir / "web3_gaming_tracker_errors.log",
            self.log_dir / "migration_manager.log"
        ]
        
        # Filter to existing files
        existing_files = [f for f in log_files if f.exists()]
        
        if not existing_files:
            logger.warning("No log files found to monitor")
            return
        
        # Start monitoring tasks
        tasks = [self.tail_log_file(log_file) for log_file in existing_files]
        
        # Add metrics reporting task
        tasks.append(self._metrics_reporter())
        
        await asyncio.gather(*tasks)
    
    async def _metrics_reporter(self):
        """Periodically report monitoring metrics"""
        while True:
            await asyncio.sleep(60)  # Report every minute
            metrics = self.get_current_metrics()
            logger.info(f"Monitoring metrics: {json.dumps(metrics)}")


class EmailNotificationHandler:
    """Email notification handler for alerts"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, 
                 password: str, from_email: str, to_emails: List[str]):
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.from_email = from_email
        self.to_emails = to_emails
    
    async def __call__(self, alert_data: Dict):
        """Send email notification for alert"""
        try:
            alert = alert_data['alert']
            
            subject = f"[{alert['severity'].upper()}] {alert['name']} - Web3 Gaming Tracker"
            
            body = f"""
Alert Triggered: {alert['name']}
Severity: {alert['severity']}
Time: {alert_data['timestamp']}

Alert Details:
- Threshold: {alert['threshold']} occurrences in {alert['time_window']} seconds
- Current matches: {alert['match_count']}

Log Entry:
{alert_data['log_line']}

Current System Metrics:
{json.dumps(alert_data['metrics'], indent=2)}

This is an automated alert from the Web3 Gaming Tracker monitoring system.
            """
            
            msg = MimeMultipart()
            msg['From'] = self.from_email
            msg['To'] = ', '.join(self.to_emails)
            msg['Subject'] = subject
            
            msg.attach(MimeText(body, 'plain'))
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
            
            logger.info(f"Email alert sent for: {alert['name']}")
        
        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")


class WebhookNotificationHandler:
    """Webhook notification handler for alerts"""
    
    def __init__(self, webhook_url: str, headers: Optional[Dict] = None):
        self.webhook_url = webhook_url
        self.headers = headers or {}
    
    async def __call__(self, alert_data: Dict):
        """Send webhook notification for alert"""
        try:
            import aiohttp
            
            payload = {
                'alert_name': alert_data['alert']['name'],
                'severity': alert_data['alert']['severity'],
                'timestamp': alert_data['timestamp'],
                'log_entry': alert_data['log_line'],
                'metrics': alert_data['metrics']
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers=self.headers
                ) as response:
                    if response.status == 200:
                        logger.info(f"Webhook alert sent for: {alert_data['alert']['name']}")
                    else:
                        logger.error(f"Webhook failed with status: {response.status}")
        
        except Exception as e:
            logger.error(f"Failed to send webhook alert: {e}")


async def main():
    """CLI interface for log monitoring"""
    parser = argparse.ArgumentParser(description="Real-time Log Monitoring System")
    
    parser.add_argument('--log-dir', default='logs', help='Log directory to monitor')
    parser.add_argument('--config', help='Configuration file for custom alerts')
    parser.add_argument('--email-config', help='Email notification configuration file')
    parser.add_argument('--webhook-url', help='Webhook URL for notifications')
    parser.add_argument('--metrics-interval', type=int, default=60, 
                       help='Metrics reporting interval in seconds')
    
    args = parser.parse_args()
    
    # Initialize monitor
    monitor = LogMonitor(args.log_dir)
    
    # Setup notification handlers
    if args.email_config and Path(args.email_config).exists():
        try:
            with open(args.email_config) as f:
                email_config = json.load(f)
            
            email_handler = EmailNotificationHandler(**email_config)
            monitor.add_notification_handler(email_handler)
            logger.info("Email notifications enabled")
        
        except Exception as e:
            logger.error(f"Failed to setup email notifications: {e}")
    
    if args.webhook_url:
        webhook_handler = WebhookNotificationHandler(args.webhook_url)
        monitor.add_notification_handler(webhook_handler)
        logger.info("Webhook notifications enabled")
    
    # Load custom alerts
    if args.config and Path(args.config).exists():
        try:
            with open(args.config) as f:
                config = json.load(f)
            
            for alert_config in config.get('alerts', []):
                alert = LogAlert(**alert_config)
                monitor.add_alert(alert)
            
            logger.info(f"Loaded {len(config.get('alerts', []))} custom alerts")
        
        except Exception as e:
            logger.error(f"Failed to load custom alerts: {e}")
    
    logger.info("Starting log monitoring...")
    logger.info(f"Monitoring directory: {args.log_dir}")
    logger.info(f"Active alerts: {len(monitor.alerts)}")
    
    try:
        await monitor.monitor_all_logs()
    except KeyboardInterrupt:
        logger.info("Monitoring stopped by user")
    except Exception as e:
        logger.error(f"Monitoring failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
