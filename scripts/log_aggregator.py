#!/usr/bin/env python3
"""
Log Aggregation and Analysis System

This script provides advanced log aggregation capabilities including:
- Multi-source log collection and parsing
- Time-series analysis and trending
- Error pattern analysis and classification
- Performance metrics extraction
- Custom query and filtering capabilities
- Export to various formats (JSON, CSV, Elasticsearch)
"""

import os
import sys
import json
import gzip
import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Iterator, Any
from collections import defaultdict, Counter
import re
import csv
import sqlite3
from dataclasses import dataclass, asdict

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Configure logging for this script
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class LogEntry:
    """Structured log entry representation"""
    timestamp: datetime
    level: str
    service: str
    module: str
    function: str
    line: int
    message: str
    raw_data: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data


class LogParser:
    """Parse various log formats into structured entries"""
    
    def __init__(self):
        self.parsers = {
            'json': self._parse_json_log,
            'text': self._parse_text_log
        }
    
    def parse_log_line(self, line: str, source_file: str = None) -> Optional[LogEntry]:
        """Parse a single log line into structured entry"""
        line = line.strip()
        if not line:
            return None
        
        # Try JSON format first
        if line.startswith('{'):
            return self._parse_json_log(line, source_file)
        else:
            return self._parse_text_log(line, source_file)
    
    def _parse_json_log(self, line: str, source_file: str = None) -> Optional[LogEntry]:
        """Parse JSON formatted log line"""
        try:
            data = json.loads(line)
            
            # Extract timestamp
            timestamp_str = data.get('timestamp', '')
            try:
                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                if timestamp.tzinfo:
                    timestamp = timestamp.replace(tzinfo=None)
            except:
                timestamp = datetime.now()
            
            return LogEntry(
                timestamp=timestamp,
                level=data.get('level', 'UNKNOWN'),
                service=data.get('service', 'unknown'),
                module=data.get('module', 'unknown'),
                function=data.get('function', 'unknown'),
                line=data.get('line', 0),
                message=data.get('message', ''),
                raw_data=data
            )
        
        except json.JSONDecodeError:
            return None
    
    def _parse_text_log(self, line: str, source_file: str = None) -> Optional[LogEntry]:
        """Parse text formatted log line"""
        # Pattern for standard log format: timestamp - name - level - module:function:line - message
        pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}(?:,\d{3})?)\s*-\s*([^-]+)\s*-\s*(\w+)\s*-\s*([^:]+):([^:]+):(\d+)\s*-\s*(.*)'
        
        match = re.match(pattern, line)
        if match:
            timestamp_str, service, level, module, function, line_num, message = match.groups()
            
            try:
                timestamp = datetime.strptime(timestamp_str.split(',')[0], '%Y-%m-%d %H:%M:%S')
            except:
                timestamp = datetime.now()
            
            return LogEntry(
                timestamp=timestamp,
                level=level.strip(),
                service=service.strip(),
                module=module.strip(),
                function=function.strip(),
                line=int(line_num),
                message=message.strip(),
                raw_data={'raw_line': line, 'source_file': source_file}
            )
        
        # Fallback for unstructured logs
        return LogEntry(
            timestamp=datetime.now(),
            level='UNKNOWN',
            service='unknown',
            module='unknown',
            function='unknown',
            line=0,
            message=line,
            raw_data={'raw_line': line, 'source_file': source_file}
        )


class LogAggregator:
    """Aggregate and analyze logs from multiple sources"""
    
    def __init__(self, log_directory: str = "logs"):
        self.log_dir = Path(log_directory)
        self.parser = LogParser()
        self.entries = []
        self.db_path = self.log_dir / "aggregated_logs.db"
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database for log storage"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS log_entries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    level TEXT NOT NULL,
                    service TEXT NOT NULL,
                    module TEXT NOT NULL,
                    function TEXT NOT NULL,
                    line INTEGER,
                    message TEXT NOT NULL,
                    raw_data TEXT,
                    source_file TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create indexes for better query performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON log_entries(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_level ON log_entries(level)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_service ON log_entries(service)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_message ON log_entries(message)")
    
    def collect_logs(self, start_time: Optional[datetime] = None, 
                    end_time: Optional[datetime] = None) -> int:
        """Collect logs from all sources within time range"""
        collected_count = 0
        
        # Get all log files
        log_files = list(self.log_dir.glob("*.log*"))
        
        # Include archived logs
        archive_dir = self.log_dir / "archive"
        if archive_dir.exists():
            log_files.extend(archive_dir.glob("*.gz"))
        
        for log_file in log_files:
            try:
                count = self._process_log_file(log_file, start_time, end_time)
                collected_count += count
                logger.info(f"Processed {count} entries from {log_file}")
            
            except Exception as e:
                logger.error(f"Failed to process {log_file}: {e}")
        
        logger.info(f"Total collected entries: {collected_count}")
        return collected_count
    
    def _process_log_file(self, log_file: Path, start_time: Optional[datetime], 
                         end_time: Optional[datetime]) -> int:
        """Process a single log file"""
        count = 0
        
        # Determine if file is compressed
        if log_file.suffix == '.gz':
            file_opener = gzip.open
            mode = 'rt'
        else:
            file_opener = open
            mode = 'r'
        
        with file_opener(log_file, mode, encoding='utf-8', errors='ignore') as f:
            for line in f:
                entry = self.parser.parse_log_line(line, str(log_file))
                
                if entry:
                    # Filter by time range
                    if start_time and entry.timestamp < start_time:
                        continue
                    if end_time and entry.timestamp > end_time:
                        continue
                    
                    self._store_entry(entry)
                    count += 1
        
        return count
    
    def _store_entry(self, entry: LogEntry):
        """Store log entry in database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                INSERT INTO log_entries 
                (timestamp, level, service, module, function, line, message, raw_data, source_file)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                entry.timestamp.isoformat(),
                entry.level,
                entry.service,
                entry.module,
                entry.function,
                entry.line,
                entry.message,
                json.dumps(entry.raw_data),
                entry.raw_data.get('source_file', '')
            ))
    
    def query_logs(self, filters: Dict[str, Any] = None, 
                  limit: int = 1000) -> List[LogEntry]:
        """Query logs with filters"""
        filters = filters or {}
        
        query = "SELECT * FROM log_entries WHERE 1=1"
        params = []
        
        # Add filters
        if 'level' in filters:
            query += " AND level = ?"
            params.append(filters['level'])
        
        if 'service' in filters:
            query += " AND service = ?"
            params.append(filters['service'])
        
        if 'start_time' in filters:
            query += " AND timestamp >= ?"
            params.append(filters['start_time'].isoformat())
        
        if 'end_time' in filters:
            query += " AND timestamp <= ?"
            params.append(filters['end_time'].isoformat())
        
        if 'message_contains' in filters:
            query += " AND message LIKE ?"
            params.append(f"%{filters['message_contains']}%")
        
        query += " ORDER BY timestamp DESC LIMIT ?"
        params.append(limit)
        
        entries = []
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(query, params)
            
            for row in cursor:
                entry = LogEntry(
                    timestamp=datetime.fromisoformat(row['timestamp']),
                    level=row['level'],
                    service=row['service'],
                    module=row['module'],
                    function=row['function'],
                    line=row['line'],
                    message=row['message'],
                    raw_data=json.loads(row['raw_data'])
                )
                entries.append(entry)
        
        return entries
    
    def analyze_error_patterns(self, hours: int = 24) -> Dict[str, Any]:
        """Analyze error patterns in recent logs"""
        start_time = datetime.now() - timedelta(hours=hours)
        
        error_entries = self.query_logs({
            'level': 'ERROR',
            'start_time': start_time
        }, limit=10000)
        
        # Classify errors
        error_patterns = Counter()
        service_errors = Counter()
        hourly_errors = defaultdict(int)
        
        for entry in error_entries:
            # Classify error type
            error_type = self._classify_error(entry.message)
            error_patterns[error_type] += 1
            
            # Count by service
            service_errors[entry.service] += 1
            
            # Count by hour
            hour_key = entry.timestamp.strftime('%Y-%m-%d %H:00')
            hourly_errors[hour_key] += 1
        
        return {
            'total_errors': len(error_entries),
            'error_patterns': dict(error_patterns.most_common(10)),
            'service_errors': dict(service_errors.most_common(10)),
            'hourly_distribution': dict(hourly_errors),
            'analysis_period_hours': hours
        }
    
    def _classify_error(self, message: str) -> str:
        """Classify error message into categories"""
        message_lower = message.lower()
        
        if 'database' in message_lower or 'sql' in message_lower:
            return 'database_error'
        elif 'blockchain' in message_lower or 'rpc' in message_lower:
            return 'blockchain_error'
        elif 'api' in message_lower or 'http' in message_lower:
            return 'api_error'
        elif 'scraper' in message_lower or 'scraping' in message_lower:
            return 'scraper_error'
        elif 'timeout' in message_lower:
            return 'timeout_error'
        elif 'connection' in message_lower:
            return 'connection_error'
        elif 'memory' in message_lower:
            return 'memory_error'
        elif 'permission' in message_lower or 'auth' in message_lower:
            return 'auth_error'
        else:
            return 'general_error'
    
    def generate_performance_report(self, hours: int = 24) -> Dict[str, Any]:
        """Generate performance analysis report"""
        start_time = datetime.now() - timedelta(hours=hours)
        
        # Get all entries with performance data
        all_entries = self.query_logs({
            'start_time': start_time
        }, limit=50000)
        
        response_times = []
        service_performance = defaultdict(list)
        
        for entry in all_entries:
            raw_data = entry.raw_data
            
            # Extract response time
            response_time = raw_data.get('response_time_ms')
            if response_time:
                response_times.append(response_time)
                service_performance[entry.service].append(response_time)
        
        # Calculate statistics
        def calculate_stats(times):
            if not times:
                return {}
            
            times.sort()
            n = len(times)
            
            return {
                'count': n,
                'min': min(times),
                'max': max(times),
                'avg': sum(times) / n,
                'p50': times[n // 2],
                'p95': times[int(n * 0.95)],
                'p99': times[int(n * 0.99)]
            }
        
        overall_stats = calculate_stats(response_times)
        service_stats = {
            service: calculate_stats(times)
            for service, times in service_performance.items()
        }
        
        return {
            'analysis_period_hours': hours,
            'overall_performance': overall_stats,
            'service_performance': service_stats,
            'slow_requests': len([t for t in response_times if t > 5000])
        }
    
    def export_to_csv(self, output_file: str, filters: Dict[str, Any] = None):
        """Export logs to CSV format"""
        entries = self.query_logs(filters, limit=100000)
        
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['timestamp', 'level', 'service', 'module', 'function', 'line', 'message']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for entry in entries:
                writer.writerow({
                    'timestamp': entry.timestamp.isoformat(),
                    'level': entry.level,
                    'service': entry.service,
                    'module': entry.module,
                    'function': entry.function,
                    'line': entry.line,
                    'message': entry.message
                })
        
        logger.info(f"Exported {len(entries)} entries to {output_file}")
    
    def export_to_json(self, output_file: str, filters: Dict[str, Any] = None):
        """Export logs to JSON format"""
        entries = self.query_logs(filters, limit=100000)
        
        data = {
            'export_timestamp': datetime.now().isoformat(),
            'total_entries': len(entries),
            'filters': filters or {},
            'entries': [entry.to_dict() for entry in entries]
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, default=str)
        
        logger.info(f"Exported {len(entries)} entries to {output_file}")


def main():
    """CLI interface for log aggregation"""
    parser = argparse.ArgumentParser(description="Log Aggregation and Analysis System")
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Collect command
    collect_parser = subparsers.add_parser('collect', help='Collect logs from sources')
    collect_parser.add_argument('--start-time', help='Start time (ISO format)')
    collect_parser.add_argument('--end-time', help='End time (ISO format)')
    
    # Query command
    query_parser = subparsers.add_parser('query', help='Query collected logs')
    query_parser.add_argument('--level', help='Filter by log level')
    query_parser.add_argument('--service', help='Filter by service')
    query_parser.add_argument('--message', help='Filter by message content')
    query_parser.add_argument('--limit', type=int, default=100, help='Limit results')
    query_parser.add_argument('--output', help='Output file for results')
    
    # Analyze command
    analyze_parser = subparsers.add_parser('analyze', help='Analyze error patterns')
    analyze_parser.add_argument('--hours', type=int, default=24, help='Analysis period in hours')
    analyze_parser.add_argument('--output', help='Output file for analysis')
    
    # Performance command
    perf_parser = subparsers.add_parser('performance', help='Generate performance report')
    perf_parser.add_argument('--hours', type=int, default=24, help='Analysis period in hours')
    perf_parser.add_argument('--output', help='Output file for report')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export logs to file')
    export_parser.add_argument('--format', choices=['csv', 'json'], required=True, help='Export format')
    export_parser.add_argument('--output', required=True, help='Output file')
    export_parser.add_argument('--level', help='Filter by log level')
    export_parser.add_argument('--service', help='Filter by service')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    aggregator = LogAggregator()
    
    if args.command == 'collect':
        start_time = None
        end_time = None
        
        if args.start_time:
            start_time = datetime.fromisoformat(args.start_time)
        if args.end_time:
            end_time = datetime.fromisoformat(args.end_time)
        
        count = aggregator.collect_logs(start_time, end_time)
        print(f"Collected {count} log entries")
    
    elif args.command == 'query':
        filters = {}
        if args.level:
            filters['level'] = args.level
        if args.service:
            filters['service'] = args.service
        if args.message:
            filters['message_contains'] = args.message
        
        entries = aggregator.query_logs(filters, args.limit)
        
        if args.output:
            with open(args.output, 'w') as f:
                for entry in entries:
                    f.write(f"{entry.timestamp} [{entry.level}] {entry.service}: {entry.message}\n")
            print(f"Results saved to {args.output}")
        else:
            for entry in entries:
                print(f"{entry.timestamp} [{entry.level}] {entry.service}: {entry.message}")
    
    elif args.command == 'analyze':
        analysis = aggregator.analyze_error_patterns(args.hours)
        
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(analysis, f, indent=2, default=str)
            print(f"Analysis saved to {args.output}")
        else:
            print(json.dumps(analysis, indent=2, default=str))
    
    elif args.command == 'performance':
        report = aggregator.generate_performance_report(args.hours)
        
        if args.output:
            with open(args.output, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            print(f"Performance report saved to {args.output}")
        else:
            print(json.dumps(report, indent=2, default=str))
    
    elif args.command == 'export':
        filters = {}
        if args.level:
            filters['level'] = args.level
        if args.service:
            filters['service'] = args.service
        
        if args.format == 'csv':
            aggregator.export_to_csv(args.output, filters)
        elif args.format == 'json':
            aggregator.export_to_json(args.output, filters)


if __name__ == "__main__":
    main()
