#!/usr/bin/env python3
"""
Enhanced Gaming Analytics Runner
Runs all four enhanced analytics services to populate the database with gaming project metrics
"""
import asyncio
import logging
import sys
import os
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.tvl_tracker import tvl_tracker
from services.user_activity_tracker import user_activity_tracker
from services.p2e_economics_tracker import p2e_economics_tracker
from services.nft_floor_tracker import nft_floor_tracker
from services.database_analytics_config import database_analytics_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def run_tvl_analytics():
    """Run TVL analytics for all gaming projects"""
    logger.info("🏦 Starting TVL Analytics...")

    projects = database_analytics_config.get_projects_with_tokens()
    logger.info(f"📊 Running TVL analysis for {len(projects)} projects")

    for project_name, project_config in projects.items():
        try:
            logger.info(f"💰 Analyzing TVL for {project_config.project_name}...")
            tvl_data = await tvl_tracker.get_protocol_tvl(project_name)

            if tvl_data:
                logger.info(f"✅ TVL collected for {project_config.project_name}: ${tvl_data.total_tvl_usd:,.2f}")
                # Store the data
                await tvl_tracker.store_tvl_data(tvl_data.primary_tvl_data)
            else:
                logger.warning(f"⚠️ No TVL data collected for {project_config.project_name}")

        except Exception as e:
            logger.error(f"❌ Error collecting TVL for {project_config.project_name}: {e}")

    logger.info("🏦 TVL Analytics completed!")

async def run_user_activity_analytics():
    """Run user activity analytics for all gaming projects"""
    logger.info("👥 Starting User Activity Analytics...")

    projects = database_analytics_config.get_projects_with_contracts()
    logger.info(f"📊 Running user activity analysis for {len(projects)} projects")

    for project_name, project_config in projects.items():
        try:
            logger.info(f"🎮 Analyzing user activity for {project_config.project_name}...")
            activity_data = await user_activity_tracker.get_protocol_user_activity(project_name)

            if activity_data:
                logger.info(f"✅ Activity collected for {project_config.project_name}: {activity_data.daily_active_users} DAU")
                # Store the data
                await user_activity_tracker.store_user_activity_data(activity_data)
            else:
                logger.warning(f"⚠️ No activity data collected for {project_config.project_name}")

        except Exception as e:
            logger.error(f"❌ Error collecting activity for {project_config.project_name}: {e}")

    logger.info("👥 User Activity Analytics completed!")

async def run_p2e_economics_analytics():
    """Run P2E economics analytics for all gaming projects"""
    logger.info("💎 Starting P2E Economics Analytics...")

    projects = database_analytics_config.get_projects_with_tokens()
    logger.info(f"📊 Running P2E economics analysis for {len(projects)} projects")

    for project_name, project_config in projects.items():
        try:
            logger.info(f"💰 Analyzing P2E economics for {project_config.project_name}...")
            economics_data = await p2e_economics_tracker.get_protocol_economics(project_name)

            if economics_data:
                logger.info(f"✅ Economics collected for {project_config.project_name}")
                # Get earning metrics from the economics data
                avg_daily_earnings = economics_data.earning_potential.get('avg_daily_usd', 0)
                sustainability_score = economics_data.sustainability_metrics.get('overall_score', 0)
                logger.info(f"   💰 Avg daily earnings: ${avg_daily_earnings:.2f}")
                logger.info(f"   📊 Sustainability score: {sustainability_score:.1f}%")
            else:
                logger.warning(f"⚠️ No economics data collected for {project_config.project_name}")

        except Exception as e:
            logger.error(f"❌ Error collecting economics for {project_config.project_name}: {e}")

    logger.info("💎 P2E Economics Analytics completed!")

async def run_nft_floor_analytics():
    """Run NFT floor price analytics for all gaming projects"""
    logger.info("🖼️ Starting NFT Floor Price Analytics...")

    projects = database_analytics_config.get_projects_with_nfts()
    logger.info(f"📊 Running NFT floor analysis for {len(projects)} projects")

    for project_name, project_config in projects.items():
        try:
            logger.info(f"🎨 Analyzing NFT floors for {project_config.project_name}...")

            # Get all collections for this protocol
            all_collections = await nft_floor_tracker.get_all_collections_floor_data()
            protocol_collections = all_collections.get(project_name, {})

            if protocol_collections:
                logger.info(f"✅ NFT data collected for {project_config.project_name}: {len(protocol_collections)} collections")
                for collection_name, floor_data in protocol_collections.items():
                    logger.info(f"   🎨 {collection_name}: {floor_data.floor_price_eth:.4f} ETH (${floor_data.floor_price_usd:.2f})")
            else:
                logger.warning(f"⚠️ No NFT data collected for {project_config.project_name}")

        except Exception as e:
            logger.error(f"❌ Error collecting NFT data for {project_config.project_name}: {e}")

    logger.info("🖼️ NFT Floor Price Analytics completed!")

async def run_all_analytics():
    """Run all enhanced analytics services"""
    start_time = datetime.now()
    logger.info("🚀 Starting Enhanced Gaming Analytics Collection...")
    logger.info(f"⏰ Started at: {start_time}")
    
    # Check if gaming projects are loaded
    projects = database_analytics_config.get_all_projects()
    if not projects:
        logger.error("❌ No gaming projects loaded! Please check database configuration.")
        return

    logger.info(f"📋 Found {len(projects)} gaming projects to analyze:")
    for project_name, project_config in projects.items():
        logger.info(f"  • {project_config.project_name} ({project_config.blockchain})")
    
    # Run all analytics in parallel for better performance
    try:
        await asyncio.gather(
            run_tvl_analytics(),
            run_user_activity_analytics(),
            run_p2e_economics_analytics(),
            run_nft_floor_analytics(),
            return_exceptions=True
        )
    except Exception as e:
        logger.error(f"❌ Error running analytics: {e}")
    
    end_time = datetime.now()
    duration = end_time - start_time
    logger.info(f"🎉 Enhanced Gaming Analytics Collection completed!")
    logger.info(f"⏰ Finished at: {end_time}")
    logger.info(f"⏱️ Total duration: {duration}")

def main():
    """Main entry point"""
    logger.info("🎮 Enhanced Gaming Analytics Runner")
    logger.info("=" * 50)
    
    try:
        asyncio.run(run_all_analytics())
    except KeyboardInterrupt:
        logger.info("⏹️ Analytics collection stopped by user")
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
