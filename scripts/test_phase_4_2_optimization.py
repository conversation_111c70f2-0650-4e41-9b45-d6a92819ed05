#!/usr/bin/env python3
"""
Phase 4.2 Extended SQL Optimization Testing Script

Tests the extended SQL optimization features including:
- News analytics optimization
- Cross-chain comparison optimization  
- Time-series metrics optimization
- Performance monitoring
- Optimized gaming analytics endpoints
"""

import asyncio
import time
import sys
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from models.base import get_db
    from services.sql_optimization import get_sql_optimizer
    from models.gaming import Article, GamingProject
    from sqlalchemy.orm import Session
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure you're running from the project root directory")
    sys.exit(1)


class Phase42OptimizationTester:
    """Test Phase 4.2 extended SQL optimizations"""
    
    def __init__(self):
        self.db = next(get_db())
        self.sql_optimizer = get_sql_optimizer(self.db)
        self.test_results = {}
        
    def log_test(self, test_name: str, message: str):
        """Log test progress"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {test_name}: {message}")
        
    def measure_performance(self, func, *args, **kwargs) -> tuple:
        """Measure function execution time"""
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            return result, execution_time, None
        except Exception as e:
            execution_time = time.time() - start_time
            # Rollback the transaction to prevent abort state
            try:
                self.db.rollback()
            except:
                pass  # Ignore rollback errors
            return None, execution_time, str(e)
    
    def test_news_analytics_optimization(self) -> Dict[str, Any]:
        """Test optimized news analytics query"""
        self.log_test("NEWS_ANALYTICS", "Testing optimized news analytics...")
        
        # Test different time ranges and network filters
        test_cases = [
            {"hours": 24, "network": None},
            {"hours": 48, "network": "ethereum"},
            {"hours": 72, "network": "solana"},
            {"hours": 168, "network": None}  # 1 week
        ]
        
        results = []
        for case in test_cases:
            result, exec_time, error = self.measure_performance(
                self.sql_optimizer.get_news_analytics_optimized,
                hours=case["hours"],
                network=case["network"]
            )
            
            test_result = {
                "test_case": case,
                "execution_time": round(exec_time, 3),
                "success": error is None,
                "error": error,
                "data_points": len(result.get("hourly_data", [])) if result and isinstance(result, dict) else 0
            }
            results.append(test_result)
            
            status = "✅ PASS" if error is None else "❌ FAIL"
            self.log_test("NEWS_ANALYTICS", 
                f"{status} - {case} - {exec_time:.3f}s")
        
        return {
            "test_name": "news_analytics_optimization",
            "results": results,
            "average_time": sum(r["execution_time"] for r in results) / len(results),
            "success_rate": sum(1 for r in results if r["success"]) / len(results)
        }
    
    def test_cross_chain_comparison_optimization(self) -> Dict[str, Any]:
        """Test optimized cross-chain comparison query"""
        self.log_test("CROSS_CHAIN", "Testing optimized cross-chain comparison...")
        
        test_cases = [1, 3, 7, 14, 30]  # Different day ranges
        
        results = []
        for days in test_cases:
            result, exec_time, error = self.measure_performance(
                self.sql_optimizer.get_cross_chain_comparison_optimized,
                days=days
            )
            
            test_result = {
                "days": days,
                "execution_time": round(exec_time, 3),
                "success": error is None,
                "error": error,
                "networks_analyzed": len(result.get("networks", [])) if result and isinstance(result, dict) else 0
            }
            results.append(test_result)
            
            status = "✅ PASS" if error is None else "❌ FAIL"
            self.log_test("CROSS_CHAIN", 
                f"{status} - {days} days - {exec_time:.3f}s - {test_result['networks_analyzed']} networks")
        
        return {
            "test_name": "cross_chain_comparison_optimization",
            "results": results,
            "average_time": sum(r["execution_time"] for r in results) / len(results),
            "success_rate": sum(1 for r in results if r["success"]) / len(results)
        }
    
    def test_time_series_optimization(self) -> Dict[str, Any]:
        """Test optimized time-series metrics query"""
        self.log_test("TIME_SERIES", "Testing optimized time-series metrics...")
        
        test_cases = [
            {"days": 7, "interval": "hour", "networks": None},
            {"days": 14, "interval": "day", "networks": ["ethereum", "solana"]},
            {"days": 30, "interval": "day", "networks": None},
            {"days": 3, "interval": "hour", "networks": ["polygon"]}
        ]
        
        results = []
        for case in test_cases:
            result, exec_time, error = self.measure_performance(
                self.sql_optimizer.get_time_series_metrics_optimized,
                days=case["days"],
                interval=case["interval"],
                networks=case["networks"]
            )
            
            test_result = {
                "test_case": case,
                "execution_time": round(exec_time, 3),
                "success": error is None,
                "error": error,
                "time_points": len(result.get("time_series", {})) if result and isinstance(result, dict) else 0
            }
            results.append(test_result)
            
            status = "✅ PASS" if error is None else "❌ FAIL"
            self.log_test("TIME_SERIES", 
                f"{status} - {case} - {exec_time:.3f}s - {test_result['time_points']} points")
        
        return {
            "test_name": "time_series_optimization",
            "results": results,
            "average_time": sum(r["execution_time"] for r in results) / len(results),
            "success_rate": sum(1 for r in results if r["success"]) / len(results)
        }
    
    def test_performance_monitoring(self) -> Dict[str, Any]:
        """Test performance monitoring query"""
        self.log_test("PERFORMANCE", "Testing performance monitoring...")
        
        result, exec_time, error = self.measure_performance(
            self.sql_optimizer.get_performance_monitoring_data
        )
        
        test_result = {
            "execution_time": round(exec_time, 3),
            "success": error is None,
            "error": error,
            "metrics_collected": len(result.keys()) if result else 0
        }
        
        status = "✅ PASS" if error is None else "❌ FAIL"
        self.log_test("PERFORMANCE", 
            f"{status} - {exec_time:.3f}s - {test_result['metrics_collected']} metric categories")
        
        return {
            "test_name": "performance_monitoring",
            "result": test_result
        }
    
    def test_database_stats(self) -> Dict[str, Any]:
        """Get current database statistics"""
        self.log_test("DB_STATS", "Collecting database statistics...")

        try:
            # Count records in main tables
            article_count = self.db.query(Article).count()
            gaming_project_count = self.db.query(GamingProject).count()

            # Try to count other tables if they exist
            blockchain_data_count = 0
            nft_collection_count = 0

            # Check if tables exist before querying
            from sqlalchemy import inspect
            inspector = inspect(self.db.bind)
            table_names = inspector.get_table_names()

            if 'blockchain_data' in table_names:
                try:
                    from models.gaming import BlockchainData
                    blockchain_data_count = self.db.query(BlockchainData).count()
                except:
                    blockchain_data_count = 0

            if 'nft_collections' in table_names:
                try:
                    from models.gaming import NFTCollection
                    nft_collection_count = self.db.query(NFTCollection).count()
                except:
                    nft_collection_count = 0
            
            stats = {
                "articles": article_count,
                "gaming_projects": gaming_project_count,
                "blockchain_data": blockchain_data_count,
                "nft_collections": nft_collection_count,
                "total_records": article_count + gaming_project_count + blockchain_data_count + nft_collection_count
            }
            
            self.log_test("DB_STATS", f"Articles: {article_count}, Gaming Projects: {gaming_project_count}")
            self.log_test("DB_STATS", f"Blockchain Data: {blockchain_data_count}, NFT Collections: {nft_collection_count}")
            
            return {
                "test_name": "database_statistics",
                "stats": stats,
                "success": True
            }
            
        except Exception as e:
            self.log_test("DB_STATS", f"❌ Error collecting stats: {e}")
            return {
                "test_name": "database_statistics",
                "error": str(e),
                "success": False
            }
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run all Phase 4.2 optimization tests"""
        print("🚀 Starting Phase 4.2 Extended SQL Optimization Tests")
        print("=" * 60)
        
        start_time = time.time()
        
        # Run all tests
        test_results = {
            "test_suite": "Phase 4.2 Extended SQL Optimization",
            "start_time": datetime.now().isoformat(),
            "database_stats": self.test_database_stats(),
            "news_analytics": self.test_news_analytics_optimization(),
            "cross_chain_comparison": self.test_cross_chain_comparison_optimization(),
            "time_series_metrics": self.test_time_series_optimization(),
            "performance_monitoring": self.test_performance_monitoring()
        }
        
        total_time = time.time() - start_time
        test_results["total_execution_time"] = round(total_time, 3)
        test_results["end_time"] = datetime.now().isoformat()
        
        # Calculate overall success rate
        successful_tests = 0
        total_tests = 0
        
        for test_name, test_data in test_results.items():
            if isinstance(test_data, dict) and "success" in test_data:
                total_tests += 1
                if test_data["success"]:
                    successful_tests += 1
            elif isinstance(test_data, dict) and "success_rate" in test_data:
                total_tests += 1
                if test_data["success_rate"] == 1.0:
                    successful_tests += 1
        
        test_results["overall_success_rate"] = successful_tests / total_tests if total_tests > 0 else 0
        
        print("\n" + "=" * 60)
        print(f"✅ Phase 4.2 Testing Complete - {total_time:.3f}s total")
        print(f"📊 Overall Success Rate: {test_results['overall_success_rate']:.1%}")
        
        return test_results


def main():
    """Main test execution"""
    try:
        tester = Phase42OptimizationTester()
        results = tester.run_comprehensive_test()
        
        # Print summary
        print("\n📋 TEST SUMMARY:")
        print("-" * 40)
        
        for test_name, test_data in results.items():
            if isinstance(test_data, dict):
                if "success_rate" in test_data:
                    print(f"{test_name}: {test_data['success_rate']:.1%} success, avg {test_data.get('average_time', 0):.3f}s")
                elif "success" in test_data:
                    status = "✅ PASS" if test_data["success"] else "❌ FAIL"
                    print(f"{test_name}: {status}")
        
        return results
        
    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        return None


if __name__ == "__main__":
    main()
