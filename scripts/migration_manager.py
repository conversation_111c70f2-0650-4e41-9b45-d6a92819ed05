#!/usr/bin/env python3
"""
Database Migration Management System

This script provides comprehensive database migration management with:
- Migration validation and verification
- Rollback capabilities with safety checks
- Migration status reporting
- Backup and restore functionality
- Environment-specific migration handling
"""

import os
import sys
import subprocess
import argparse
import logging
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional, Tuple

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config.settings import get_settings
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/migration_manager.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MigrationManager:
    """Comprehensive database migration management system"""
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        self.settings = get_settings()
        self.engine = create_engine(self.settings.database.url)
        self.alembic_dir = project_root / "alembic"
        
    def get_current_revision(self) -> Optional[str]:
        """Get the current database revision"""
        try:
            result = subprocess.run(
                ["alembic", "current"],
                capture_output=True,
                text=True,
                cwd=project_root
            )
            if result.returncode == 0:
                # Extract revision ID from output
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if line and not line.startswith('INFO'):
                        return line.split()[0]
            return None
        except Exception as e:
            logger.error(f"Failed to get current revision: {e}")
            return None
    
    def get_migration_history(self) -> List[Dict[str, str]]:
        """Get complete migration history"""
        try:
            result = subprocess.run(
                ["alembic", "history", "--verbose"],
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            migrations = []
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                current_migration = {}
                
                for line in lines:
                    line = line.strip()
                    if line.startswith('Rev:'):
                        if current_migration:
                            migrations.append(current_migration)
                        current_migration = {'revision': line.split()[1]}
                    elif line.startswith('Parent:'):
                        current_migration['parent'] = line.split()[1] if len(line.split()) > 1 else None
                    elif line.startswith('Path:'):
                        current_migration['path'] = line.split('Path:')[1].strip()
                    elif line and not line.startswith(('Revision ID:', 'Revises:', 'Create Date:')):
                        if 'description' not in current_migration:
                            current_migration['description'] = line
                
                if current_migration:
                    migrations.append(current_migration)
                    
            return migrations
        except Exception as e:
            logger.error(f"Failed to get migration history: {e}")
            return []
    
    def validate_migration(self, revision: str) -> Tuple[bool, str]:
        """Validate a specific migration"""
        try:
            # Check if revision exists
            result = subprocess.run(
                ["alembic", "show", revision],
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            if result.returncode != 0:
                return False, f"Revision {revision} not found"
            
            # Additional validation checks
            migration_file = None
            for version_file in (self.alembic_dir / "versions").glob("*.py"):
                if revision in version_file.name:
                    migration_file = version_file
                    break
            
            if not migration_file:
                return False, f"Migration file for {revision} not found"
            
            # Check file syntax
            try:
                with open(migration_file, 'r') as f:
                    compile(f.read(), migration_file, 'exec')
            except SyntaxError as e:
                return False, f"Syntax error in migration file: {e}"
            
            return True, "Migration validation passed"
            
        except Exception as e:
            return False, f"Validation error: {e}"
    
    def create_backup(self, backup_name: Optional[str] = None) -> str:
        """Create database backup before migration"""
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{self.environment}_{timestamp}"
        
        backup_dir = project_root / "backups"
        backup_dir.mkdir(exist_ok=True)
        backup_file = backup_dir / f"{backup_name}.sql"
        
        try:
            # Extract database connection details
            db_url = self.settings.database.url
            # Parse PostgreSQL URL: postgresql://user:pass@host:port/dbname
            if db_url.startswith('postgresql://'):
                parts = db_url.replace('postgresql://', '').split('/')
                db_name = parts[-1]
                host_part = parts[0].split('@')[-1]
                host = host_part.split(':')[0]
                port = host_part.split(':')[1] if ':' in host_part else '5432'
                
                user_pass = parts[0].split('@')[0]
                user = user_pass.split(':')[0]
                password = user_pass.split(':')[1] if ':' in user_pass else ''
                
                # Create pg_dump command
                env = os.environ.copy()
                if password:
                    env['PGPASSWORD'] = password
                
                cmd = [
                    'pg_dump',
                    '-h', host,
                    '-p', port,
                    '-U', user,
                    '-f', str(backup_file),
                    '--verbose',
                    '--no-password',
                    db_name
                ]
                
                result = subprocess.run(cmd, env=env, capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"Database backup created: {backup_file}")
                    return str(backup_file)
                else:
                    logger.error(f"Backup failed: {result.stderr}")
                    return ""
            else:
                logger.error("Only PostgreSQL backups are currently supported")
                return ""
                
        except Exception as e:
            logger.error(f"Failed to create backup: {e}")
            return ""
    
    def migrate_to_revision(self, revision: str, create_backup: bool = True) -> bool:
        """Migrate to specific revision with safety checks"""
        try:
            current_rev = self.get_current_revision()
            logger.info(f"Current revision: {current_rev}")
            logger.info(f"Target revision: {revision}")
            
            # Validate target revision
            is_valid, message = self.validate_migration(revision)
            if not is_valid:
                logger.error(f"Migration validation failed: {message}")
                return False
            
            # Create backup if requested
            backup_file = ""
            if create_backup:
                backup_file = self.create_backup()
                if not backup_file:
                    logger.error("Failed to create backup, aborting migration")
                    return False
            
            # Perform migration
            result = subprocess.run(
                ["alembic", "upgrade", revision],
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            if result.returncode == 0:
                logger.info(f"Successfully migrated to revision {revision}")
                if backup_file:
                    logger.info(f"Backup available at: {backup_file}")
                return True
            else:
                logger.error(f"Migration failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Migration error: {e}")
            return False
    
    def rollback_to_revision(self, revision: str, confirm: bool = False) -> bool:
        """Rollback to specific revision with confirmation"""
        try:
            current_rev = self.get_current_revision()
            
            if not confirm:
                logger.warning(f"ROLLBACK OPERATION: {current_rev} -> {revision}")
                logger.warning("This operation may result in data loss!")
                logger.warning("Use --confirm flag to proceed")
                return False
            
            # Create backup before rollback
            backup_file = self.create_backup(f"pre_rollback_{current_rev}")
            if not backup_file:
                logger.error("Failed to create pre-rollback backup")
                return False
            
            # Perform rollback
            result = subprocess.run(
                ["alembic", "downgrade", revision],
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            if result.returncode == 0:
                logger.info(f"Successfully rolled back to revision {revision}")
                logger.info(f"Pre-rollback backup: {backup_file}")
                return True
            else:
                logger.error(f"Rollback failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Rollback error: {e}")
            return False
    
    def generate_migration(self, message: str, autogenerate: bool = True) -> bool:
        """Generate new migration with proper naming"""
        try:
            cmd = ["alembic", "revision"]
            if autogenerate:
                cmd.append("--autogenerate")
            cmd.extend(["-m", message])
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            if result.returncode == 0:
                logger.info(f"Migration generated: {message}")
                logger.info(result.stdout)
                return True
            else:
                logger.error(f"Failed to generate migration: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Migration generation error: {e}")
            return False
    
    def status_report(self) -> Dict:
        """Generate comprehensive migration status report"""
        try:
            current_rev = self.get_current_revision()
            history = self.get_migration_history()
            
            # Check for pending migrations
            result = subprocess.run(
                ["alembic", "heads"],
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            heads = []
            if result.returncode == 0:
                heads = [line.strip() for line in result.stdout.split('\n') if line.strip()]
            
            return {
                'current_revision': current_rev,
                'total_migrations': len(history),
                'migration_history': history[:5],  # Last 5 migrations
                'heads': heads,
                'environment': self.environment,
                'database_url': self.settings.database.url.split('@')[-1],  # Hide credentials
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to generate status report: {e}")
            return {'error': str(e)}


def main():
    """Main CLI interface"""
    parser = argparse.ArgumentParser(description="Database Migration Manager")
    parser.add_argument('--env', default='development', help='Environment (development/production)')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Status command
    subparsers.add_parser('status', help='Show migration status')
    
    # Migrate command
    migrate_parser = subparsers.add_parser('migrate', help='Migrate to revision')
    migrate_parser.add_argument('revision', help='Target revision (head/revision_id)')
    migrate_parser.add_argument('--no-backup', action='store_true', help='Skip backup creation')
    
    # Rollback command
    rollback_parser = subparsers.add_parser('rollback', help='Rollback to revision')
    rollback_parser.add_argument('revision', help='Target revision')
    rollback_parser.add_argument('--confirm', action='store_true', help='Confirm rollback operation')
    
    # Generate command
    generate_parser = subparsers.add_parser('generate', help='Generate new migration')
    generate_parser.add_argument('message', help='Migration message')
    generate_parser.add_argument('--no-autogenerate', action='store_true', help='Skip autogenerate')
    
    # Validate command
    validate_parser = subparsers.add_parser('validate', help='Validate migration')
    validate_parser.add_argument('revision', help='Revision to validate')
    
    # Backup command
    backup_parser = subparsers.add_parser('backup', help='Create database backup')
    backup_parser.add_argument('--name', help='Backup name')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = MigrationManager(args.env)
    
    if args.command == 'status':
        status = manager.status_report()
        print("\n=== Migration Status Report ===")
        for key, value in status.items():
            if key == 'migration_history':
                print(f"{key}:")
                for migration in value:
                    print(f"  - {migration.get('revision', 'N/A')}: {migration.get('description', 'N/A')}")
            else:
                print(f"{key}: {value}")
    
    elif args.command == 'migrate':
        success = manager.migrate_to_revision(args.revision, not args.no_backup)
        sys.exit(0 if success else 1)
    
    elif args.command == 'rollback':
        success = manager.rollback_to_revision(args.revision, args.confirm)
        sys.exit(0 if success else 1)
    
    elif args.command == 'generate':
        success = manager.generate_migration(args.message, not args.no_autogenerate)
        sys.exit(0 if success else 1)
    
    elif args.command == 'validate':
        is_valid, message = manager.validate_migration(args.revision)
        print(f"Validation result: {message}")
        sys.exit(0 if is_valid else 1)
    
    elif args.command == 'backup':
        backup_file = manager.create_backup(args.name)
        if backup_file:
            print(f"Backup created: {backup_file}")
            sys.exit(0)
        else:
            print("Backup failed")
            sys.exit(1)


if __name__ == "__main__":
    main()
