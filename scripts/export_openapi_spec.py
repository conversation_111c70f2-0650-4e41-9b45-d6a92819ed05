#!/usr/bin/env python3
"""
Export OpenAPI specification for Web3 Gaming News Tracker API
"""
import sys
import os
import json
import yaml
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from api.main import app


def export_openapi_spec():
    """Export OpenAPI specification in JSON and YAML formats"""
    
    # Get OpenAPI schema
    openapi_schema = app.openapi()
    
    # Create docs directory if it doesn't exist
    docs_dir = project_root / "docs"
    docs_dir.mkdir(exist_ok=True)
    
    # Export as JSON
    json_path = docs_dir / "openapi.json"
    with open(json_path, 'w', encoding='utf-8') as f:
        json.dump(openapi_schema, f, indent=2, ensure_ascii=False)
    
    print(f"✅ OpenAPI JSON specification exported to: {json_path}")
    
    # Export as YAML
    yaml_path = docs_dir / "openapi.yaml"
    with open(yaml_path, 'w', encoding='utf-8') as f:
        yaml.dump(openapi_schema, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ OpenAPI YAML specification exported to: {yaml_path}")
    
    # Print summary
    print("\n📊 API Summary:")
    print(f"Title: {openapi_schema['info']['title']}")
    print(f"Version: {openapi_schema['info']['version']}")
    print(f"Total Paths: {len(openapi_schema['paths'])}")
    
    # Count endpoints by tag
    tag_counts = {}
    for path_data in openapi_schema['paths'].values():
        for method_data in path_data.values():
            if isinstance(method_data, dict) and 'tags' in method_data:
                for tag in method_data['tags']:
                    tag_counts[tag] = tag_counts.get(tag, 0) + 1
    
    print("\n🏷️  Endpoints by Tag:")
    for tag, count in sorted(tag_counts.items()):
        print(f"  {tag}: {count} endpoints")
    
    # List Phase 7 endpoints
    phase7_endpoints = []
    for path, path_data in openapi_schema['paths'].items():
        if 'content-intelligence' in path:
            for method in path_data.keys():
                phase7_endpoints.append(f"{method.upper()} {path}")
    
    if phase7_endpoints:
        print("\n🧠 Phase 7 Content Intelligence Endpoints:")
        for endpoint in sorted(phase7_endpoints):
            print(f"  {endpoint}")
    
    return openapi_schema


def validate_openapi_spec():
    """Validate the exported OpenAPI specification"""
    try:
        from openapi_spec_validator import validate_spec
        
        # Load and validate JSON spec
        json_path = project_root / "docs" / "openapi.json"
        if json_path.exists():
            with open(json_path, 'r') as f:
                spec = json.load(f)
            
            validate_spec(spec)
            print("✅ OpenAPI specification is valid!")
            return True
            
    except ImportError:
        print("⚠️  openapi-spec-validator not installed. Skipping validation.")
        print("   Install with: pip install openapi-spec-validator")
        return True
    except Exception as e:
        print(f"❌ OpenAPI specification validation failed: {e}")
        return False


def generate_postman_collection():
    """Generate Postman collection from OpenAPI spec"""
    try:
        import requests
        
        # Load OpenAPI spec
        json_path = project_root / "docs" / "openapi.json"
        if not json_path.exists():
            print("❌ OpenAPI JSON spec not found. Run export first.")
            return False
        
        with open(json_path, 'r') as f:
            openapi_spec = json.load(f)
        
        # Create basic Postman collection structure
        postman_collection = {
            "info": {
                "name": openapi_spec['info']['title'],
                "description": openapi_spec['info']['description'],
                "version": openapi_spec['info']['version'],
                "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
            },
            "item": [],
            "variable": [
                {
                    "key": "baseUrl",
                    "value": "http://localhost:8000",
                    "type": "string"
                }
            ]
        }
        
        # Convert paths to Postman requests
        for path, path_data in openapi_spec['paths'].items():
            for method, method_data in path_data.items():
                if isinstance(method_data, dict):
                    request_item = {
                        "name": method_data.get('summary', f"{method.upper()} {path}"),
                        "request": {
                            "method": method.upper(),
                            "header": [
                                {
                                    "key": "Content-Type",
                                    "value": "application/json",
                                    "type": "text"
                                }
                            ],
                            "url": {
                                "raw": "{{baseUrl}}" + path,
                                "host": ["{{baseUrl}}"],
                                "path": path.strip('/').split('/')
                            },
                            "description": method_data.get('description', '')
                        }
                    }
                    
                    # Add request body for POST/PUT methods
                    if method.upper() in ['POST', 'PUT', 'PATCH']:
                        if 'requestBody' in method_data:
                            request_item['request']['body'] = {
                                "mode": "raw",
                                "raw": "{\n  // Add request body here\n}",
                                "options": {
                                    "raw": {
                                        "language": "json"
                                    }
                                }
                            }
                    
                    postman_collection['item'].append(request_item)
        
        # Export Postman collection
        postman_path = project_root / "docs" / "postman_collection.json"
        with open(postman_path, 'w', encoding='utf-8') as f:
            json.dump(postman_collection, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Postman collection exported to: {postman_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to generate Postman collection: {e}")
        return False


def main():
    """Main function to export API documentation"""
    print("🚀 Exporting Web3 Gaming News Tracker API Documentation")
    print("=" * 60)
    
    try:
        # Export OpenAPI specification
        openapi_schema = export_openapi_spec()
        
        # Validate specification
        validate_openapi_spec()
        
        # Generate Postman collection
        generate_postman_collection()
        
        print("\n✅ API documentation export completed successfully!")
        print("\n📁 Generated Files:")
        print("  📄 docs/openapi.json - OpenAPI JSON specification")
        print("  📄 docs/openapi.yaml - OpenAPI YAML specification") 
        print("  📄 docs/postman_collection.json - Postman collection")
        print("  📄 docs/api_documentation.md - Human-readable documentation")
        
        print("\n🌐 Access Documentation:")
        print("  📖 Swagger UI: http://localhost:8000/docs")
        print("  📖 ReDoc: http://localhost:8000/redoc")
        
        print("\n🔧 Usage:")
        print("  • Import postman_collection.json into Postman for API testing")
        print("  • Use openapi.json/yaml for code generation and tooling")
        print("  • Reference api_documentation.md for detailed endpoint information")
        
    except Exception as e:
        print(f"❌ Export failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
