#!/usr/bin/env python3
"""
Automated Log Management Service

This service provides automated log management based on configuration:
- Scheduled log rotation and archival
- Continuous monitoring and alerting
- Health checks and emergency procedures
- Integration with external systems
- Comprehensive reporting and analytics
"""

import os
import sys
import yaml
import asyncio
import logging
import schedule
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional
import signal
import json

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from scripts.log_manager import LogManager
from scripts.log_monitor import LogMonitor
from scripts.log_aggregator import LogAggregator

# Configure logging for this service
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LogManagementService:
    """Automated log management service"""
    
    def __init__(self, config_file: str = "config/log_management.yaml"):
        self.config_file = Path(config_file)
        self.config = self._load_config()
        self.running = False
        
        # Initialize components
        self.log_manager = LogManager(self.config['log_directory'])
        self.log_monitor = LogMonitor(self.config['log_directory'])
        self.log_aggregator = LogAggregator(self.config['log_directory'])
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        # Setup scheduled tasks
        self._setup_schedules()
        
        # Setup monitoring alerts
        self._setup_alerts()
        
        logger.info("Log Management Service initialized")
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_file, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"Configuration loaded from {self.config_file}")
            return config
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Return default configuration
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration if file loading fails"""
        return {
            'log_directory': 'logs',
            'rotation': {'max_file_size_mb': 100},
            'archival': {'archive_after_days': 7},
            'cleanup': {'min_free_space_gb': 5},
            'monitoring': {'enabled': True, 'check_interval_seconds': 60},
            'alerts': [],
            'automation': {'schedules': {}}
        }
    
    def _setup_schedules(self):
        """Setup scheduled tasks based on configuration"""
        schedules_config = self.config.get('automation', {}).get('schedules', {})
        
        # Log rotation schedule
        if schedules_config.get('log_rotation', {}).get('enabled', True):
            cron = schedules_config.get('log_rotation', {}).get('cron', '0 2 * * *')
            self._schedule_from_cron(cron, self._scheduled_rotation)
            logger.info("Scheduled log rotation")
        
        # Log archival schedule
        if schedules_config.get('log_archival', {}).get('enabled', True):
            cron = schedules_config.get('log_archival', {}).get('cron', '0 3 * * 0')
            self._schedule_from_cron(cron, self._scheduled_archival)
            logger.info("Scheduled log archival")
        
        # Log cleanup schedule
        if schedules_config.get('log_cleanup', {}).get('enabled', True):
            cron = schedules_config.get('log_cleanup', {}).get('cron', '0 4 1 * *')
            self._schedule_from_cron(cron, self._scheduled_cleanup)
            logger.info("Scheduled log cleanup")
        
        # Health check schedule
        if schedules_config.get('health_check', {}).get('enabled', True):
            schedule.every(15).minutes.do(self._scheduled_health_check)
            logger.info("Scheduled health checks")
        
        # Metrics collection schedule
        if schedules_config.get('metrics_collection', {}).get('enabled', True):
            schedule.every(5).minutes.do(self._scheduled_metrics_collection)
            logger.info("Scheduled metrics collection")
        
        # Analysis report schedule
        if schedules_config.get('analysis_report', {}).get('enabled', True):
            schedule.every().day.at("06:00").do(self._scheduled_analysis_report)
            logger.info("Scheduled analysis reports")
    
    def _schedule_from_cron(self, cron_expr: str, job_func):
        """Convert cron expression to schedule job (simplified)"""
        # This is a simplified cron parser - in production, use a proper cron library
        parts = cron_expr.split()
        if len(parts) >= 5:
            minute, hour, day, month, weekday = parts[:5]
            
            if minute == "0" and hour != "*":
                # Daily at specific hour
                schedule.every().day.at(f"{hour.zfill(2)}:00").do(job_func)
            elif weekday != "*":
                # Weekly on specific day
                days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
                if weekday.isdigit() and 0 <= int(weekday) <= 6:
                    day_name = days[int(weekday)]
                    schedule.every().week.on(day_name).at(f"{hour.zfill(2)}:00").do(job_func)
    
    def _setup_alerts(self):
        """Setup monitoring alerts from configuration"""
        alerts_config = self.config.get('alerts', [])
        
        for alert_config in alerts_config:
            from scripts.log_monitor import LogAlert
            alert = LogAlert(
                name=alert_config['name'],
                pattern=alert_config['pattern'],
                threshold=alert_config.get('threshold', 1),
                time_window=alert_config.get('time_window_seconds', 300),
                severity=alert_config.get('severity', 'warning')
            )
            alert.cooldown = alert_config.get('cooldown_seconds', 300)
            self.log_monitor.add_alert(alert)
        
        logger.info(f"Setup {len(alerts_config)} monitoring alerts")
    
    def _scheduled_rotation(self):
        """Scheduled log rotation task"""
        try:
            max_size = self.config.get('rotation', {}).get('max_file_size_mb', 100)
            result = self.log_manager.rotate_logs(max_size)
            
            logger.info(f"Scheduled rotation completed: {len(result['rotated'])} files rotated")
            
            if result['errors']:
                logger.error(f"Rotation errors: {result['errors']}")
        
        except Exception as e:
            logger.error(f"Scheduled rotation failed: {e}")
    
    def _scheduled_archival(self):
        """Scheduled log archival task"""
        try:
            archive_days = self.config.get('archival', {}).get('archive_after_days', 7)
            result = self.log_manager.archive_old_logs(archive_days)
            
            logger.info(f"Scheduled archival completed: {len(result['archived'])} files archived")
            
            if result['errors']:
                logger.error(f"Archival errors: {result['errors']}")
        
        except Exception as e:
            logger.error(f"Scheduled archival failed: {e}")
    
    def _scheduled_cleanup(self):
        """Scheduled log cleanup task"""
        try:
            retention_days = max(self.config.get('retention_policies', {}).values())
            result = self.log_manager.cleanup_old_archives(retention_days)
            
            logger.info(f"Scheduled cleanup completed: {len(result['deleted'])} files deleted")
            
            if result['errors']:
                logger.error(f"Cleanup errors: {result['errors']}")
        
        except Exception as e:
            logger.error(f"Scheduled cleanup failed: {e}")
    
    def _scheduled_health_check(self):
        """Scheduled health check task"""
        try:
            health = self.log_manager.monitor_log_health()
            
            if health['status'] == 'critical':
                logger.critical(f"Log system health critical: {health['issues']}")
                self._handle_emergency()
            elif health['status'] == 'warning':
                logger.warning(f"Log system health warning: {health['warnings']}")
            else:
                logger.debug("Log system health check passed")
        
        except Exception as e:
            logger.error(f"Health check failed: {e}")
    
    def _scheduled_metrics_collection(self):
        """Scheduled metrics collection task"""
        try:
            metrics = self.log_monitor.get_current_metrics()
            
            # Log metrics for monitoring systems
            logger.info(f"Log metrics: {json.dumps(metrics)}")
            
            # Check thresholds
            thresholds = self.config.get('monitoring', {}).get('thresholds', {})
            
            error_rate = metrics.get('error_rate_per_minute', 0)
            if error_rate > thresholds.get('error_rate_critical_per_minute', 20):
                logger.critical(f"Critical error rate: {error_rate} errors/minute")
            elif error_rate > thresholds.get('error_rate_warning_per_minute', 5):
                logger.warning(f"High error rate: {error_rate} errors/minute")
        
        except Exception as e:
            logger.error(f"Metrics collection failed: {e}")
    
    def _scheduled_analysis_report(self):
        """Scheduled analysis report generation"""
        try:
            # Generate error analysis
            error_analysis = self.log_aggregator.analyze_error_patterns(24)
            
            # Generate performance report
            perf_report = self.log_aggregator.generate_performance_report(24)
            
            # Save reports
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            error_file = f"logs/reports/error_analysis_{timestamp}.json"
            perf_file = f"logs/reports/performance_report_{timestamp}.json"
            
            Path("logs/reports").mkdir(exist_ok=True)
            
            with open(error_file, 'w') as f:
                json.dump(error_analysis, f, indent=2, default=str)
            
            with open(perf_file, 'w') as f:
                json.dump(perf_report, f, indent=2, default=str)
            
            logger.info(f"Analysis reports generated: {error_file}, {perf_file}")
        
        except Exception as e:
            logger.error(f"Analysis report generation failed: {e}")
    
    def _handle_emergency(self):
        """Handle emergency situations"""
        emergency_config = self.config.get('automation', {}).get('emergency', {})
        
        if not emergency_config.get('auto_cleanup_enabled', True):
            logger.warning("Emergency cleanup disabled in configuration")
            return
        
        actions = emergency_config.get('emergency_actions', [])
        
        for action in actions:
            try:
                if action == "compress_old_logs":
                    self._emergency_compress_logs()
                elif action == "archive_rotated_logs":
                    self._emergency_archive_logs()
                elif action == "cleanup_temp_files":
                    self._emergency_cleanup_temp()
                elif action == "remove_debug_logs":
                    self._emergency_remove_debug_logs()
                
                logger.info(f"Emergency action completed: {action}")
            
            except Exception as e:
                logger.error(f"Emergency action failed {action}: {e}")
    
    def _emergency_compress_logs(self):
        """Emergency log compression"""
        import gzip
        import shutil
        
        log_dir = Path(self.config['log_directory'])
        for log_file in log_dir.glob("*.log.*"):
            if not log_file.name.endswith('.gz'):
                compressed_file = log_file.with_suffix(log_file.suffix + '.gz')
                
                with open(log_file, 'rb') as f_in:
                    with gzip.open(compressed_file, 'wb') as f_out:
                        shutil.copyfileobj(f_in, f_out)
                
                log_file.unlink()
                logger.info(f"Emergency compressed: {log_file}")
    
    def _emergency_archive_logs(self):
        """Emergency log archival"""
        result = self.log_manager.archive_old_logs(1)  # Archive files older than 1 day
        logger.info(f"Emergency archival: {len(result['archived'])} files")
    
    def _emergency_cleanup_temp(self):
        """Emergency temp file cleanup"""
        temp_dir = Path(self.config.get('temp_directory', 'logs/temp'))
        if temp_dir.exists():
            for temp_file in temp_dir.glob("*"):
                temp_file.unlink()
                logger.info(f"Emergency removed temp file: {temp_file}")
    
    def _emergency_remove_debug_logs(self):
        """Emergency debug log removal"""
        # This would remove debug-level log entries - implement based on needs
        logger.info("Emergency debug log removal completed")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
    
    async def start_monitoring(self):
        """Start real-time log monitoring"""
        if self.config.get('monitoring', {}).get('enabled', True):
            logger.info("Starting real-time log monitoring...")
            await self.log_monitor.monitor_all_logs()
    
    def start_scheduler(self):
        """Start the scheduled task runner"""
        logger.info("Starting scheduled task runner...")
        self.running = True
        
        while self.running:
            schedule.run_pending()
            time.sleep(1)
        
        logger.info("Scheduled task runner stopped")
    
    async def run(self):
        """Run the complete log management service"""
        logger.info("Starting Log Management Service...")
        
        # Start both monitoring and scheduling
        tasks = []
        
        # Add monitoring task if enabled
        if self.config.get('monitoring', {}).get('enabled', True):
            tasks.append(self.start_monitoring())
        
        # Add scheduler task
        tasks.append(asyncio.to_thread(self.start_scheduler))
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Service error: {e}")
        finally:
            logger.info("Log Management Service stopped")


async def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Log Management Service")
    parser.add_argument('--config', default='config/log_management.yaml',
                       help='Configuration file path')
    parser.add_argument('--mode', choices=['service', 'monitor', 'scheduler'],
                       default='service', help='Service mode')
    
    args = parser.parse_args()
    
    service = LogManagementService(args.config)
    
    if args.mode == 'service':
        await service.run()
    elif args.mode == 'monitor':
        await service.start_monitoring()
    elif args.mode == 'scheduler':
        service.start_scheduler()


if __name__ == "__main__":
    asyncio.run(main())
