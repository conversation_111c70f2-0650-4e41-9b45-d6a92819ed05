#!/usr/bin/env python3
"""
Configuration script for blockchain data API keys
"""
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def update_env_file():
    """Update .env file with the provided API keys"""
    env_file = project_root / '.env'
    env_example_file = project_root / '.env.example'
    
    # Read existing .env or create from .env.example
    if env_file.exists():
        with open(env_file, 'r') as f:
            env_content = f.read()
    elif env_example_file.exists():
        with open(env_example_file, 'r') as f:
            env_content = f.read()
        print("📋 Created .env from .env.example")
    else:
        env_content = ""
        print("⚠️ No .env.example found, creating new .env file")
    
    # API keys provided by user
    api_keys = {
        'BITQUERY_API_KEY': 'rookietrader',
        'BITQUERY_ACCESS_TOKEN': 'ory_at_uSfMPlc_q-ArqkBqW4VGGP02ZNe6rseqVbaEKkwqtdo.5Mo1fAcIixfbJzhMZ-M-eeld9GdF8iMRjFdj04shtiU',
        # Note: User mentioned they have Flipside API key but didn't provide it
        # They need to add it manually or provide it
        'FLIPSIDE_API_KEY': 'your_flipside_api_key_here',
        # CryptoRank and DexTools keys not provided - user needs to get these
        'CRYPTORANK_API_KEY': 'your_cryptorank_api_key_here',
        'DEXTOOLS_API_KEY': 'your_dextools_api_key_here'
    }
    
    # Update or add API keys
    lines = env_content.split('\n')
    updated_lines = []
    keys_found = set()
    
    for line in lines:
        line_updated = False
        for key, value in api_keys.items():
            if line.startswith(f'{key}='):
                # Only update if it's a placeholder value
                if 'your_' in line or line.endswith('='):
                    updated_lines.append(f'{key}={value}')
                    keys_found.add(key)
                    line_updated = True
                    print(f"✅ Updated {key}")
                else:
                    updated_lines.append(line)
                    keys_found.add(key)
                    line_updated = True
                    print(f"ℹ️ Kept existing {key}")
                break
        
        if not line_updated:
            updated_lines.append(line)
    
    # Add missing keys
    for key, value in api_keys.items():
        if key not in keys_found:
            updated_lines.append(f'{key}={value}')
            print(f"➕ Added {key}")
    
    # Write updated .env file
    with open(env_file, 'w') as f:
        f.write('\n'.join(updated_lines))
    
    print(f"📝 Updated {env_file}")


def print_configuration_status():
    """Print current configuration status"""
    print("\n📊 Configuration Status:")
    print("=" * 50)
    
    # Check which APIs are configured
    env_file = project_root / '.env'
    if env_file.exists():
        with open(env_file, 'r') as f:
            env_content = f.read()
        
        api_status = {
            'Flipside Crypto': 'FLIPSIDE_API_KEY',
            'BitQuery': 'BITQUERY_API_KEY',
            'CryptoRank': 'CRYPTORANK_API_KEY',
            'DexTools': 'DEXTOOLS_API_KEY'
        }
        
        for service, key in api_status.items():
            if f'{key}=' in env_content:
                line = [l for l in env_content.split('\n') if l.startswith(f'{key}=')][0]
                value = line.split('=', 1)[1] if '=' in line else ''
                
                if value and not value.startswith('your_') and value != '':
                    print(f"✅ {service}: Configured")
                else:
                    print(f"❌ {service}: Not configured (placeholder value)")
            else:
                print(f"❌ {service}: Missing from .env")
    else:
        print("❌ No .env file found")


def print_next_steps():
    """Print next steps for the user"""
    print("\n🚀 Next Steps:")
    print("=" * 50)
    print("1. ✅ BitQuery API is configured with your provided credentials")
    print("2. 📝 Add your Flipside Crypto API key to the .env file:")
    print("   FLIPSIDE_API_KEY=your_actual_flipside_api_key")
    print("3. 🔑 Get API keys for the following services:")
    print("   - CryptoRank.io: https://cryptorank.io/api")
    print("   - DexTools.io: https://www.dextools.io/api")
    print("4. 🧪 Run the test script to verify integration:")
    print("   python scripts/test_blockchain_data_integration.py")
    print("5. 🚀 Start the API server and test the new endpoints:")
    print("   python main.py")
    print("   curl http://localhost:8000/api/v1/blockchain/data/test-connections")


def main():
    """Main configuration function"""
    print("🔧 Configuring Blockchain Data API Integration")
    print("=" * 50)
    
    # Update .env file
    update_env_file()
    
    # Show configuration status
    print_configuration_status()
    
    # Show next steps
    print_next_steps()
    
    print("\n✅ Configuration script completed!")
    print("📋 Please review the .env file and add any missing API keys.")


if __name__ == "__main__":
    main()
