#!/usr/bin/env python3
"""
Seed script to populate the database with sample gaming data
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from datetime import datetime, timedelta
from models.base import SessionLocal
from models.gaming import Source, GamingProject, Article, NFTCollection, BlockchainData

def create_sample_sources():
    """Create sample news sources"""
    sources = [
        {
            "name": "CoinDesk Gaming",
            "url": "https://www.coindesk.com/tag/gaming/",
            "source_type": "news",
            "category": "crypto_news",
            "is_active": True,
            "scrape_frequency": 3600,  # 1 hour
            "reliability_score": 0.9,
            "article_count": 0,
            "config": {"rss_url": "https://www.coindesk.com/arc/outboundfeeds/rss/"}
        },
        {
            "name": "Decrypt Gaming",
            "url": "https://decrypt.co/learn/gaming",
            "source_type": "news",
            "category": "gaming_news",
            "is_active": True,
            "scrape_frequency": 1800,  # 30 minutes
            "reliability_score": 0.85,
            "article_count": 0,
            "config": {"rss_url": "https://decrypt.co/feed"}
        },
        {
            "name": "The Block Gaming",
            "url": "https://www.theblock.co/category/gaming",
            "source_type": "news",
            "category": "blockchain_news",
            "is_active": True,
            "scrape_frequency": 2400,  # 40 minutes
            "reliability_score": 0.88,
            "article_count": 0,
            "config": {"api_key": "", "rate_limit": 100}
        }
    ]
    
    db_sources = []
    for source_data in sources:
        source = Source(**source_data)
        db_sources.append(source)
    
    return db_sources

def create_sample_gaming_projects():
    """Create sample gaming projects"""
    projects = [
        {
            "name": "Axie Infinity",
            "slug": "axie-infinity",
            "description": "A blockchain-based trading and battling game that is partially owned and operated by its players.",
            "website": "https://axieinfinity.com/",
            "category": "P2E",
            "subcategory": "Pet Battling",
            "blockchain": "Ethereum",
            "contract_addresses": ["******************************************"],
            "token_symbol": "AXS",
            "token_address": "******************************************",
            "market_cap": 1500000000.0,
            "token_price": 8.50,
            "daily_active_users": 150000,
            "total_value_locked": 45000000.0,
            "twitter_followers": 850000,
            "discord_members": 750000,
            "is_active": True,
            "launch_date": datetime(2018, 3, 1),
            "extra_metadata": {"founder": "Sky Mavis", "country": "Vietnam"}
        },
        {
            "name": "The Sandbox",
            "slug": "the-sandbox",
            "description": "A virtual world where players can build, own, and monetize their gaming experiences.",
            "website": "https://www.sandbox.game/",
            "category": "Metaverse",
            "subcategory": "Virtual World",
            "blockchain": "Ethereum",
            "contract_addresses": ["******************************************"],
            "token_symbol": "SAND",
            "token_address": "******************************************",
            "market_cap": 800000000.0,
            "token_price": 0.45,
            "daily_active_users": 75000,
            "total_value_locked": 25000000.0,
            "twitter_followers": 1200000,
            "discord_members": 400000,
            "is_active": True,
            "launch_date": datetime(2020, 8, 14),
            "extra_metadata": {"founder": "Pixowl", "partnerships": ["Atari", "Snoop Dogg"]}
        },
        {
            "name": "Splinterlands",
            "slug": "splinterlands",
            "description": "A digital collectible card game built on blockchain technology.",
            "website": "https://splinterlands.com/",
            "category": "P2E",
            "subcategory": "Card Game",
            "blockchain": "Hive",
            "contract_addresses": [],
            "token_symbol": "SPS",
            "token_address": "",
            "market_cap": 120000000.0,
            "token_price": 0.025,
            "daily_active_users": 300000,
            "total_value_locked": 8000000.0,
            "twitter_followers": 180000,
            "discord_members": 85000,
            "is_active": True,
            "launch_date": datetime(2018, 5, 10),
            "extra_metadata": {"founder": "Splinterlands Team", "blockchain_migration": "Hive"}
        }
    ]
    
    db_projects = []
    for project_data in projects:
        project = GamingProject(**project_data)
        db_projects.append(project)
    
    return db_projects

def create_sample_articles(sources, projects):
    """Create sample articles"""
    articles = [
        {
            "title": "Axie Infinity Launches New Season with Enhanced Rewards",
            "content": "Axie Infinity has announced the launch of its new season featuring enhanced rewards for players...",
            "summary": "New season brings better rewards and gameplay improvements to Axie Infinity.",
            "url": "https://coindesk.com/axie-infinity-new-season-2024",
            "author": "Gaming Reporter",
            "published_at": datetime.now() - timedelta(hours=2),
            "source_id": sources[0].id,
            "source_url": "https://coindesk.com/",
            "gaming_category": "P2E",
            "gaming_subcategory": "Pet Battling",
            "gaming_projects": ["axie-infinity"],
            "gaming_tokens": ["AXS", "SLP"],
            "sentiment_score": 0.8,
            "relevance_score": 0.95,
            "keywords": ["axie", "infinity", "season", "rewards", "P2E"],
            "tags": ["gaming", "blockchain", "P2E", "update"],
            "views": 1250,
            "likes": 89,
            "shares": 23,
            "comments": 15,
            "is_processed": True,
            "is_duplicate": False,
            "extra_metadata": {"featured": True, "trending": True}
        },
        {
            "title": "The Sandbox Partners with Major Gaming Studio for Virtual Experiences",
            "content": "The Sandbox has announced a new partnership that will bring AAA gaming experiences to the metaverse...",
            "summary": "Major gaming studio partnership expands The Sandbox's virtual world offerings.",
            "url": "https://decrypt.co/sandbox-partnership-2024",
            "author": "Metaverse Analyst",
            "published_at": datetime.now() - timedelta(hours=5),
            "source_id": sources[1].id,
            "source_url": "https://decrypt.co/",
            "gaming_category": "Metaverse",
            "gaming_subcategory": "Virtual World",
            "gaming_projects": ["the-sandbox"],
            "gaming_tokens": ["SAND"],
            "sentiment_score": 0.7,
            "relevance_score": 0.88,
            "keywords": ["sandbox", "partnership", "metaverse", "virtual", "AAA"],
            "tags": ["metaverse", "partnership", "gaming", "virtual-world"],
            "views": 890,
            "likes": 67,
            "shares": 18,
            "comments": 12,
            "is_processed": True,
            "is_duplicate": False,
            "extra_metadata": {"partnership_type": "content", "duration": "2 years"}
        }
    ]
    
    db_articles = []
    for article_data in articles:
        article = Article(**article_data)
        db_articles.append(article)
    
    return db_articles

def seed_database():
    """Main function to seed the database"""
    db = SessionLocal()
    
    try:
        print("🌱 Starting database seeding...")
        
        # Create sources
        print("📰 Creating sample news sources...")
        sources = create_sample_sources()
        db.add_all(sources)
        db.commit()
        
        # Refresh to get IDs
        for source in sources:
            db.refresh(source)
        
        # Create gaming projects
        print("🎮 Creating sample gaming projects...")
        projects = create_sample_gaming_projects()
        db.add_all(projects)
        db.commit()
        
        # Refresh to get IDs
        for project in projects:
            db.refresh(project)
        
        # Create articles
        print("📝 Creating sample articles...")
        articles = create_sample_articles(sources, projects)
        db.add_all(articles)
        db.commit()
        
        print("✅ Database seeding completed successfully!")
        print(f"   - Created {len(sources)} news sources")
        print(f"   - Created {len(projects)} gaming projects")
        print(f"   - Created {len(articles)} articles")
        
    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    seed_database()
