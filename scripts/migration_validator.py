#!/usr/bin/env python3
"""
Migration Validation and Testing System

This script provides comprehensive validation for database migrations:
- Schema validation and consistency checks
- Data integrity verification
- Performance impact analysis
- Rollback testing
- Cross-environment compatibility checks
"""

import os
import sys
import tempfile
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Tuple, Optional
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config.settings import get_settings
from sqlalchemy import create_engine, text, inspect, MetaData
from sqlalchemy.exc import SQLAlchemyError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MigrationValidator:
    """Comprehensive migration validation system"""
    
    def __init__(self, environment: str = "development"):
        self.environment = environment
        self.settings = get_settings()
        self.engine = create_engine(self.settings.database.url)
        self.inspector = inspect(self.engine)
        
    def validate_schema_consistency(self) -> Tuple[bool, List[str]]:
        """Validate database schema consistency"""
        issues = []
        
        try:
            # Check for orphaned foreign keys
            tables = self.inspector.get_table_names()
            
            for table_name in tables:
                foreign_keys = self.inspector.get_foreign_keys(table_name)
                
                for fk in foreign_keys:
                    referenced_table = fk['referred_table']
                    if referenced_table not in tables:
                        issues.append(f"Table {table_name} references non-existent table {referenced_table}")
                    
                    # Check if referenced columns exist
                    try:
                        ref_columns = [col['name'] for col in self.inspector.get_columns(referenced_table)]
                        for ref_col in fk['referred_columns']:
                            if ref_col not in ref_columns:
                                issues.append(f"Foreign key in {table_name} references non-existent column {ref_col} in {referenced_table}")
                    except Exception as e:
                        issues.append(f"Could not validate foreign key reference: {e}")
            
            # Check for missing indexes on foreign keys
            for table_name in tables:
                foreign_keys = self.inspector.get_foreign_keys(table_name)
                indexes = self.inspector.get_indexes(table_name)
                index_columns = set()
                
                for idx in indexes:
                    for col in idx['column_names']:
                        index_columns.add(col)
                
                for fk in foreign_keys:
                    for col in fk['constrained_columns']:
                        if col not in index_columns:
                            issues.append(f"Foreign key column {col} in {table_name} lacks index")
            
            return len(issues) == 0, issues
            
        except Exception as e:
            logger.error(f"Schema validation error: {e}")
            return False, [f"Schema validation failed: {e}"]
    
    def validate_data_integrity(self) -> Tuple[bool, List[str]]:
        """Validate data integrity constraints"""
        issues = []
        
        try:
            with self.engine.connect() as conn:
                # Check for NULL values in NOT NULL columns
                tables = self.inspector.get_table_names()
                
                for table_name in tables:
                    columns = self.inspector.get_columns(table_name)
                    
                    for column in columns:
                        if not column['nullable'] and column['name'] != 'id':
                            result = conn.execute(text(f"""
                                SELECT COUNT(*) as null_count 
                                FROM {table_name} 
                                WHERE {column['name']} IS NULL
                            """))
                            
                            null_count = result.fetchone()[0]
                            if null_count > 0:
                                issues.append(f"Table {table_name} has {null_count} NULL values in NOT NULL column {column['name']}")
                
                # Check foreign key constraints
                for table_name in tables:
                    foreign_keys = self.inspector.get_foreign_keys(table_name)
                    
                    for fk in foreign_keys:
                        local_col = fk['constrained_columns'][0]
                        ref_table = fk['referred_table']
                        ref_col = fk['referred_columns'][0]
                        
                        result = conn.execute(text(f"""
                            SELECT COUNT(*) as orphan_count
                            FROM {table_name} t1
                            LEFT JOIN {ref_table} t2 ON t1.{local_col} = t2.{ref_col}
                            WHERE t1.{local_col} IS NOT NULL AND t2.{ref_col} IS NULL
                        """))
                        
                        orphan_count = result.fetchone()[0]
                        if orphan_count > 0:
                            issues.append(f"Table {table_name} has {orphan_count} orphaned foreign key references")
            
            return len(issues) == 0, issues
            
        except Exception as e:
            logger.error(f"Data integrity validation error: {e}")
            return False, [f"Data integrity validation failed: {e}"]
    
    def analyze_performance_impact(self, migration_file: Path) -> Dict[str, any]:
        """Analyze potential performance impact of migration"""
        analysis = {
            'estimated_duration': 'unknown',
            'blocking_operations': [],
            'recommendations': [],
            'risk_level': 'low'
        }
        
        try:
            with open(migration_file, 'r') as f:
                content = f.read()
            
            # Check for potentially slow operations
            slow_operations = [
                ('ALTER TABLE', 'Table alterations can be slow on large tables'),
                ('CREATE INDEX', 'Index creation can be slow and blocking'),
                ('DROP INDEX', 'Index drops can cause query performance issues'),
                ('ADD COLUMN', 'Adding columns with defaults can be slow'),
                ('DROP COLUMN', 'Dropping columns requires table rewrite'),
            ]
            
            for operation, warning in slow_operations:
                if operation in content.upper():
                    analysis['blocking_operations'].append(operation)
                    analysis['recommendations'].append(warning)
            
            # Estimate risk level
            if len(analysis['blocking_operations']) == 0:
                analysis['risk_level'] = 'low'
            elif len(analysis['blocking_operations']) <= 2:
                analysis['risk_level'] = 'medium'
            else:
                analysis['risk_level'] = 'high'
            
            # Check for concurrent index creation
            if 'CREATE INDEX CONCURRENTLY' in content.upper():
                analysis['recommendations'].append('Using CONCURRENTLY for index creation - good practice')
            elif 'CREATE INDEX' in content.upper():
                analysis['recommendations'].append('Consider using CREATE INDEX CONCURRENTLY for large tables')
            
            return analysis
            
        except Exception as e:
            logger.error(f"Performance analysis error: {e}")
            analysis['error'] = str(e)
            return analysis
    
    def test_migration_rollback(self, revision: str) -> Tuple[bool, str]:
        """Test migration rollback capability"""
        try:
            # Get current revision
            result = subprocess.run(
                ["alembic", "current"],
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            if result.returncode != 0:
                return False, "Could not determine current revision"
            
            current_rev = None
            for line in result.stdout.split('\n'):
                if line.strip() and not line.startswith('INFO'):
                    current_rev = line.strip().split()[0]
                    break
            
            if not current_rev:
                return False, "Could not parse current revision"
            
            # Test downgrade
            result = subprocess.run(
                ["alembic", "downgrade", revision],
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            if result.returncode != 0:
                return False, f"Downgrade failed: {result.stderr}"
            
            # Test upgrade back
            result = subprocess.run(
                ["alembic", "upgrade", current_rev],
                capture_output=True,
                text=True,
                cwd=project_root
            )
            
            if result.returncode != 0:
                return False, f"Upgrade back failed: {result.stderr}"
            
            return True, "Rollback test successful"
            
        except Exception as e:
            return False, f"Rollback test error: {e}"
    
    def validate_migration_file(self, migration_file: Path) -> Tuple[bool, List[str]]:
        """Validate migration file structure and content"""
        issues = []
        
        try:
            with open(migration_file, 'r') as f:
                content = f.read()
            
            # Check for required functions
            if 'def upgrade():' not in content:
                issues.append("Missing upgrade() function")
            
            if 'def downgrade():' not in content:
                issues.append("Missing downgrade() function")
            
            # Check for proper imports
            required_imports = ['from alembic import op', 'import sqlalchemy as sa']
            for imp in required_imports:
                if imp not in content:
                    issues.append(f"Missing import: {imp}")
            
            # Check for dangerous operations without safeguards
            dangerous_ops = [
                ('op.drop_table', 'Table drops should have safeguards'),
                ('op.drop_column', 'Column drops should have safeguards'),
                ('op.execute', 'Raw SQL should be carefully reviewed'),
            ]
            
            for op, warning in dangerous_ops:
                if op in content:
                    issues.append(f"Potentially dangerous operation: {warning}")
            
            # Check syntax
            try:
                compile(content, migration_file, 'exec')
            except SyntaxError as e:
                issues.append(f"Syntax error: {e}")
            
            return len(issues) == 0, issues
            
        except Exception as e:
            return False, [f"File validation error: {e}"]
    
    def comprehensive_validation(self, migration_file: Optional[Path] = None) -> Dict[str, any]:
        """Run comprehensive validation suite"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'environment': self.environment,
            'validations': {}
        }
        
        # Schema consistency validation
        logger.info("Validating schema consistency...")
        schema_valid, schema_issues = self.validate_schema_consistency()
        report['validations']['schema_consistency'] = {
            'passed': schema_valid,
            'issues': schema_issues
        }
        
        # Data integrity validation
        logger.info("Validating data integrity...")
        data_valid, data_issues = self.validate_data_integrity()
        report['validations']['data_integrity'] = {
            'passed': data_valid,
            'issues': data_issues
        }
        
        # Migration file validation
        if migration_file and migration_file.exists():
            logger.info(f"Validating migration file: {migration_file}")
            file_valid, file_issues = self.validate_migration_file(migration_file)
            report['validations']['migration_file'] = {
                'passed': file_valid,
                'issues': file_issues
            }
            
            # Performance impact analysis
            logger.info("Analyzing performance impact...")
            performance_analysis = self.analyze_performance_impact(migration_file)
            report['validations']['performance_impact'] = performance_analysis
        
        # Overall status
        all_passed = all(
            validation.get('passed', True) 
            for validation in report['validations'].values()
            if 'passed' in validation
        )
        
        report['overall_status'] = 'PASSED' if all_passed else 'FAILED'
        
        return report


def main():
    """CLI interface for migration validation"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Migration Validation System")
    parser.add_argument('--env', default='development', help='Environment')
    parser.add_argument('--migration-file', help='Specific migration file to validate')
    parser.add_argument('--test-rollback', help='Test rollback to specific revision')
    parser.add_argument('--output', help='Output file for validation report')
    
    args = parser.parse_args()
    
    validator = MigrationValidator(args.env)
    
    if args.test_rollback:
        success, message = validator.test_migration_rollback(args.test_rollback)
        print(f"Rollback test: {'PASSED' if success else 'FAILED'}")
        print(f"Message: {message}")
        sys.exit(0 if success else 1)
    
    migration_file = None
    if args.migration_file:
        migration_file = Path(args.migration_file)
        if not migration_file.exists():
            print(f"Migration file not found: {migration_file}")
            sys.exit(1)
    
    # Run comprehensive validation
    report = validator.comprehensive_validation(migration_file)
    
    # Output report
    if args.output:
        import json
        with open(args.output, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"Validation report saved to: {args.output}")
    else:
        import json
        print(json.dumps(report, indent=2))
    
    # Exit with appropriate code
    sys.exit(0 if report['overall_status'] == 'PASSED' else 1)


if __name__ == "__main__":
    main()
