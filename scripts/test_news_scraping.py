#!/usr/bin/env python3
"""
Test script for news scraping functionality
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_content_processing():
    """Test content processing functionality"""
    print("🧠 Testing content processing...")

    try:
        # Test content processing without instantiating scrapers
        from textblob import TextBlob

        # Test content
        test_content = """
        This is a test article about blockchain gaming and NFTs.
        The game features play-to-earn mechanics and DeFi integration.
        Players can earn tokens by completing quests and battles.
        """

        # Test sentiment analysis directly
        blob = TextBlob(test_content)
        sentiment = blob.sentiment.polarity
        print(f"   📊 Sentiment score: {sentiment}")

        # Test keyword extraction directly
        keywords = [word for word, pos in blob.tags if pos.startswith('NN')][:5]
        print(f"   🔑 Keywords: {keywords}")

        # Test gaming content detection
        gaming_keywords = [
            'gaming', 'game', 'nft', 'metaverse', 'play-to-earn', 'p2e',
            'blockchain game', 'crypto game', 'defi', 'token', 'quest'
        ]
        text_lower = test_content.lower()
        is_gaming = any(keyword in text_lower for keyword in gaming_keywords)
        print(f"   🎮 Is gaming content: {is_gaming}")

        print("   ✅ Content processing tests passed!")

    except Exception as e:
        print(f"   ❌ Error testing content processing: {e}")
        return False

    return True


async def test_single_scraper():
    """Test a single news scraper"""
    print("🔍 Testing single news scraper...")
    
    try:
        from scrapers.news.gaming_sources import CoinDeskGamingScraper
        from models.base import SessionLocal
        from models.gaming import Source
        
        # Get or create a test source
        db = SessionLocal()
        source = db.query(Source).filter(Source.slug == "coindesk-gaming").first()
        if not source:
            print("❌ CoinDesk Gaming source not found in database")
            return
        
        db.close()
        
        # Test scraper
        scraper = CoinDeskGamingScraper(source.id)
        
        async with scraper:
            articles = await scraper.scrape()
        
        print(f"✅ Scraped {len(articles)} articles from CoinDesk Gaming")
        
        # Show sample articles
        for i, article in enumerate(articles[:3]):
            print(f"   📰 Article {i+1}:")
            print(f"      Title: {article.title[:80]}...")
            print(f"      URL: {article.url}")
            print(f"      Keywords: {article.keywords[:5]}")
            print(f"      Sentiment: {article.raw_data.get('sentiment_score', 'N/A')}")
            print()
        
    except Exception as e:
        print(f"❌ Error testing single scraper: {e}")


async def test_all_scrapers():
    """Test all available scrapers"""
    print("🌐 Testing all news scrapers...")
    
    try:
        from scrapers.news import scrape_all_sources
        
        # Scrape all sources
        scraped_data = await scrape_all_sources()
        
        total_articles = sum(len(articles) for articles in scraped_data.values())
        print(f"✅ Scraped {total_articles} total articles from {len(scraped_data)} sources")
        
        # Show results by source
        for source_slug, articles in scraped_data.items():
            print(f"   📡 {source_slug}: {len(articles)} articles")
        
        return scraped_data
        
    except Exception as e:
        print(f"❌ Error testing all scrapers: {e}")
        return {}


async def test_save_articles(scraped_data):
    """Test saving articles to database"""
    print("💾 Testing article saving...")
    
    try:
        from scrapers.news import save_scraped_articles
        
        saved_count = await save_scraped_articles(scraped_data)
        print(f"✅ Saved {saved_count} new articles to database")
        
        # Check database
        from models.base import SessionLocal
        from models.gaming import Article
        
        db = SessionLocal()
        total_articles = db.query(Article).count()
        recent_articles = db.query(Article).filter(
            Article.created_at >= datetime.now().replace(hour=0, minute=0, second=0)
        ).count()
        db.close()
        
        print(f"   📊 Total articles in database: {total_articles}")
        print(f"   📊 Articles added today: {recent_articles}")
        
    except Exception as e:
        print(f"❌ Error testing article saving: {e}")


async def test_news_manager():
    """Test news scraping manager"""
    print("🎛️ Testing news scraping manager...")
    
    try:
        from scrapers.news import news_manager
        
        # Get initial stats
        initial_stats = news_manager.get_stats()
        print(f"   📈 Initial stats: {initial_stats}")
        
        # Run scraping cycle
        result = await news_manager.run_scraping_cycle()
        print(f"   🔄 Scraping cycle result: {result}")
        
        # Get updated stats
        final_stats = news_manager.get_stats()
        print(f"   📈 Final stats: {final_stats}")
        
        # Test recent articles
        recent = await news_manager.get_recent_articles(hours=1)
        print(f"   📰 Recent articles (last hour): {len(recent)}")
        
    except Exception as e:
        print(f"❌ Error testing news manager: {e}")





async def test_api_endpoints():
    """Test news API endpoints"""
    print("🌐 Testing news API endpoints...")
    
    try:
        import httpx
        
        base_url = "http://localhost:8000"
        
        async with httpx.AsyncClient() as client:
            # Test scraping status
            response = await client.get(f"{base_url}/api/v1/news/scrape/status")
            if response.status_code == 200:
                print(f"   ✅ Scraping status: {response.json()}")
            else:
                print(f"   ❌ Scraping status failed: {response.status_code}")
            
            # Test available scrapers
            response = await client.get(f"{base_url}/api/v1/news/sources/available")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Available scrapers: {data['count']} scrapers")
            else:
                print(f"   ❌ Available scrapers failed: {response.status_code}")
            
            # Test recent articles
            response = await client.get(f"{base_url}/api/v1/news/recent?hours=24")
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Recent articles: {data['count']} articles")
            else:
                print(f"   ❌ Recent articles failed: {response.status_code}")
        
    except Exception as e:
        print(f"❌ Error testing API endpoints: {e}")


async def main():
    """Main test function"""
    print("🚀 Starting News Scraping Tests")
    print("=" * 50)
    
    # Test content processing first (no external dependencies)
    await test_content_processing()
    print()
    
    # Test single scraper
    await test_single_scraper()
    print()
    
    # Test all scrapers
    scraped_data = await test_all_scrapers()
    print()
    
    # Test saving articles
    if scraped_data:
        await test_save_articles(scraped_data)
        print()
    
    # Test news manager
    await test_news_manager()
    print()
    
    # Test API endpoints (requires server to be running)
    await test_api_endpoints()
    print()
    
    print("=" * 50)
    print("✅ News scraping tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
