#!/usr/bin/env python3
"""
<PERSON>ript to add new gaming news sources to the database
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.base import SessionLocal
from models.gaming import Source
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def add_new_sources():
    """Add new gaming news sources to the database"""
    
    new_sources = [
        {
            'name': 'Gam3s.gg',
            'slug': 'gam3s',
            'url': 'https://gam3s.gg/news',
            'source_type': 'Scraper',
            'category': 'Gaming',
            'is_active': True,
            'scrape_frequency': 3600,  # 1 hour
            'reliability_score': 0.8,
            'config': {
                'scraper_type': 'web',
                'rate_limit': 1.0,  # 1 second between requests
                'max_articles_per_run': 20
            }
        },
        {
            'name': 'ChainPlay.gg',
            'slug': 'chainplay',
            'url': 'https://chainplay.gg',
            'source_type': 'Scraper',
            'category': 'Gaming',
            'is_active': True,
            'scrape_frequency': 3600,
            'reliability_score': 0.7,
            'config': {
                'scraper_type': 'web',
                'rate_limit': 1.0,
                'max_articles_per_run': 15
            }
        },
        {
            'name': 'PlayToEarn.com Blockchain Games',
            'slug': 'playtoearn-com',
            'url': 'https://playtoearn.com/blockchaingames',
            'source_type': 'Scraper',
            'category': 'Gaming',
            'is_active': True,
            'scrape_frequency': 7200,  # 2 hours
            'reliability_score': 0.8,
            'config': {
                'scraper_type': 'web',
                'rate_limit': 2.0,  # 2 seconds between requests (be respectful)
                'max_articles_per_run': 25
            }
        },
        {
            'name': 'GameFi.to',
            'slug': 'gamefi-to',
            'url': 'https://gamefi.to',
            'source_type': 'Scraper',
            'category': 'Gaming',
            'is_active': True,
            'scrape_frequency': 3600,
            'reliability_score': 0.7,
            'config': {
                'scraper_type': 'web',
                'rate_limit': 1.5,
                'max_articles_per_run': 20
            }
        },
        {
            'name': 'GameFi.org',
            'slug': 'gamefi-org',
            'url': 'https://gamefi.org',
            'source_type': 'Scraper',
            'category': 'Gaming',
            'is_active': True,
            'scrape_frequency': 3600,
            'reliability_score': 0.8,
            'config': {
                'scraper_type': 'web',
                'rate_limit': 1.0,
                'max_articles_per_run': 20
            }
        }
    ]
    
    db = SessionLocal()
    
    try:
        added_count = 0
        
        for source_data in new_sources:
            # Check if source already exists
            existing = db.query(Source).filter(Source.slug == source_data['slug']).first()
            
            if existing:
                logger.info(f"Source {source_data['name']} already exists, skipping")
                continue
            
            # Create new source
            source = Source(**source_data)
            db.add(source)
            added_count += 1
            logger.info(f"Added source: {source_data['name']}")
        
        db.commit()
        logger.info(f"Successfully added {added_count} new gaming news sources")
        
        # List all sources
        all_sources = db.query(Source).all()
        logger.info(f"Total sources in database: {len(all_sources)}")
        
        for source in all_sources:
            status = "✅ Active" if source.is_active else "❌ Inactive"
            logger.info(f"  - {source.name} ({source.slug}) - {status}")
    
    except Exception as e:
        logger.error(f"Error adding sources: {e}")
        db.rollback()
        raise
    
    finally:
        db.close()


if __name__ == "__main__":
    add_new_sources()
