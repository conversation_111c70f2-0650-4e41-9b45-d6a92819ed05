#!/usr/bin/env python3
"""
System Validation Script
Simple validation of core system components without complex dependencies
"""
import os
import sys
import json
from pathlib import Path

def validate_file_structure():
    """Validate that all required files exist"""
    print("🔍 Validating file structure...")
    
    required_files = [
        "config/settings.py",
        "models/gaming.py",
        "services/tvl_tracker.py",
        "services/user_activity_tracker.py", 
        "services/p2e_economics_tracker.py",
        "services/nft_floor_tracker.py",
        "services/database_analytics_config.py",
        "api/gaming_analytics_endpoints.py",
        "dashboard/frontend/src/components/EnhancedAnalyticsDashboard.jsx",
        "blockchain/enhanced_rpc.py",
        "scripts/run_enhanced_analytics.py"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ Missing files: {missing_files}")
        return False
    
    print("✅ All required files present")
    return True

def validate_env_config():
    """Validate environment configuration"""
    print("\n🔍 Validating environment configuration...")
    
    if not Path(".env").exists():
        print("❌ .env file missing")
        return False
    
    required_env_vars = [
        "DATABASE_URL",
        "REDIS_URL", 
        "API_HOST",
        "API_PORT"
    ]
    
    # Read .env file manually
    env_vars = {}
    try:
        with open(".env", "r") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    env_vars[key] = value
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False
    
    missing_vars = []
    for var in required_env_vars:
        if var not in env_vars:
            missing_vars.append(var)
        else:
            print(f"✅ {var}={env_vars[var]}")
    
    if missing_vars:
        print(f"❌ Missing environment variables: {missing_vars}")
        return False
    
    print("✅ Environment configuration valid")
    return True

def validate_csv_data():
    """Validate CSV data file exists and has content"""
    print("\n🔍 Validating CSV data...")
    
    csv_file = "GamingDBDraftResponses.csv"
    if not Path(csv_file).exists():
        print(f"❌ {csv_file} missing")
        return False
    
    try:
        with open(csv_file, "r") as f:
            lines = f.readlines()
            if len(lines) < 2:  # Header + at least one data row
                print(f"❌ {csv_file} appears empty or has no data rows")
                return False
            
            print(f"✅ {csv_file} exists with {len(lines)-1} data rows")
            return True
    except Exception as e:
        print(f"❌ Error reading {csv_file}: {e}")
        return False

def validate_dashboard_files():
    """Validate dashboard frontend files"""
    print("\n🔍 Validating dashboard files...")
    
    dashboard_files = [
        "dashboard/frontend/src/components/EnhancedAnalyticsDashboard.jsx",
        "dashboard/frontend/src/services/api.js",
        "dashboard/frontend/src/styles/components.css",
        "dashboard/frontend/package.json"
    ]
    
    missing_files = []
    for file_path in dashboard_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ Missing dashboard files: {missing_files}")
        return False
    
    print("✅ Dashboard files present")
    return True

def validate_api_structure():
    """Validate API endpoint structure"""
    print("\n🔍 Validating API structure...")
    
    api_files = [
        "api/main.py",
        "api/gaming_analytics_endpoints.py", 
        "api/dashboard_endpoints.py",
        "api/websocket_manager.py"
    ]
    
    missing_files = []
    for file_path in api_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"❌ Missing API files: {missing_files}")
        return False
    
    print("✅ API structure valid")
    return True

def generate_validation_report():
    """Generate comprehensive validation report"""
    print("\n" + "="*60)
    print("🚀 WEB3 GAMING ANALYTICS SYSTEM VALIDATION")
    print("="*60)
    
    results = {
        "file_structure": validate_file_structure(),
        "env_config": validate_env_config(), 
        "csv_data": validate_csv_data(),
        "dashboard_files": validate_dashboard_files(),
        "api_structure": validate_api_structure()
    }
    
    print("\n" + "="*60)
    print("📊 VALIDATION SUMMARY")
    print("="*60)
    
    all_passed = True
    for check, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{check.replace('_', ' ').title()}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*60)
    if all_passed:
        print("🎉 ALL VALIDATIONS PASSED - SYSTEM READY FOR TESTING")
        print("Next steps:")
        print("1. Start PostgreSQL and Redis services")
        print("2. Run database migrations")
        print("3. Start API server: python -m api.main")
        print("4. Start frontend: cd dashboard/frontend && npm start")
        print("5. Run analytics: python scripts/run_enhanced_analytics.py")
    else:
        print("⚠️  SOME VALIDATIONS FAILED - PLEASE FIX ISSUES ABOVE")
    print("="*60)
    
    return all_passed

if __name__ == "__main__":
    # Change to project root directory
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    os.chdir(project_root)
    
    success = generate_validation_report()
    sys.exit(0 if success else 1)
