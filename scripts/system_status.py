#!/usr/bin/env python3
"""
System status and summary script for Web3 Gaming News Tracker
"""
import sys
import os
import asyncio
import aiohttp
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.base import SessionLocal
from models.gaming import Source, Article
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_database_status():
    """Check database status and content"""
    logger.info("🗄️  DATABASE STATUS")
    logger.info("=" * 50)
    
    db = SessionLocal()
    
    try:
        # Count sources
        sources = db.query(Source).all()
        active_sources = db.query(Source).filter(Source.is_active == True).all()
        
        logger.info(f"📊 Total sources: {len(sources)}")
        logger.info(f"✅ Active sources: {len(active_sources)}")
        
        # List sources by category
        gaming_sources = [s for s in sources if s.category == 'Gaming']
        logger.info(f"🎮 Gaming sources: {len(gaming_sources)}")
        
        # Show new sources
        new_sources = ['gam3s', 'chainplay', 'playtoearn-com', 'gamefi-to', 'gamefi-org']
        logger.info(f"\n🆕 NEW SOURCES ADDED:")
        for source in sources:
            if source.slug in new_sources:
                status = "✅ Active" if source.is_active else "❌ Inactive"
                logger.info(f"  - {source.name} ({source.slug}) - {status}")
        
        # Count articles
        articles = db.query(Article).all()
        logger.info(f"\n📰 Total articles: {len(articles)}")
        
        if articles:
            recent_articles = db.query(Article).order_by(Article.created_at.desc()).limit(5).all()
            logger.info(f"📅 Recent articles:")
            for article in recent_articles:
                logger.info(f"  - {article.title[:60]}...")
    
    except Exception as e:
        logger.error(f"❌ Database error: {e}")
    
    finally:
        db.close()


async def check_api_status():
    """Check API endpoints status"""
    logger.info(f"\n🌐 API STATUS")
    logger.info("=" * 50)
    
    base_url = "http://localhost:8000"
    
    endpoints = [
        ("Health Check", "GET", "/health"),
        ("News Scraping", "POST", "/api/v1/news/scrape"),
        ("Blockchain Test", "GET", "/api/v1/blockchain/data/test-connections"),
        ("Gaming Tokens", "GET", "/api/v1/blockchain/data/gaming-tokens"),
        ("API Docs", "GET", "/docs")
    ]
    
    async with aiohttp.ClientSession() as session:
        for name, method, endpoint in endpoints:
            try:
                url = f"{base_url}{endpoint}"
                
                if method == "GET":
                    async with session.get(url) as response:
                        status = response.status
                elif method == "POST":
                    async with session.post(url) as response:
                        status = response.status
                
                if status == 200:
                    logger.info(f"✅ {name}: {status}")
                else:
                    logger.info(f"⚠️  {name}: {status}")
            
            except Exception as e:
                logger.error(f"❌ {name}: {e}")


async def check_blockchain_integration():
    """Check blockchain data integration"""
    logger.info(f"\n🔗 BLOCKCHAIN INTEGRATION")
    logger.info("=" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test blockchain connections
            async with session.get('http://localhost:8000/api/v1/blockchain/data/test-connections') as response:
                if response.status == 200:
                    data = await response.json()
                    
                    logger.info(f"📊 Connection Status:")
                    for client, status in data['connection_results'].items():
                        status_icon = "✅" if status else "❌"
                        logger.info(f"  {status_icon} {client.title()}: {'Connected' if status else 'Failed'}")
                    
                    logger.info(f"\n📈 Summary:")
                    logger.info(f"  - Healthy connections: {data['healthy_connections']}/{data['total_connections']}")
                    
                    # Show API key status
                    logger.info(f"\n🔑 API Key Status:")
                    logger.info(f"  ✅ Flipside: Configured")
                    logger.info(f"  ✅ BitQuery: Configured")
                    logger.info(f"  ⚠️  CryptoRank: Placeholder")
                    logger.info(f"  ⚠️  DexTools: Placeholder")
                else:
                    logger.error(f"❌ Blockchain API error: {response.status}")
    
    except Exception as e:
        logger.error(f"❌ Blockchain integration error: {e}")


def show_next_steps():
    """Show recommended next steps"""
    logger.info(f"\n🚀 NEXT STEPS")
    logger.info("=" * 50)
    
    logger.info("1. 🔧 API Configuration:")
    logger.info("   - Update CryptoRank API key in .env")
    logger.info("   - Update DexTools API key in .env")
    logger.info("   - Verify Flipside API endpoint (currently 404)")
    
    logger.info("\n2. 🕷️  Scraper Improvements:")
    logger.info("   - Fine-tune selectors for new gaming sites")
    logger.info("   - Add RSS feed alternatives where available")
    logger.info("   - Implement rate limiting and retry logic")
    
    logger.info("\n3. 🧪 Testing:")
    logger.info("   - Run: curl -X POST http://localhost:8000/api/v1/news/scrape")
    logger.info("   - Check: curl http://localhost:8000/api/v1/blockchain/data/gaming-tokens")
    logger.info("   - Monitor: tail -f logs/scraper.log")
    
    logger.info("\n4. 📊 Monitoring:")
    logger.info("   - Set up scheduled scraping (cron job)")
    logger.info("   - Add alerting for failed scrapes")
    logger.info("   - Monitor API rate limits")
    
    logger.info("\n5. 🎯 Features:")
    logger.info("   - Add sentiment analysis")
    logger.info("   - Implement duplicate detection")
    logger.info("   - Add article categorization")


async def main():
    """Main status check function"""
    logger.info("🎮 WEB3 GAMING NEWS TRACKER - SYSTEM STATUS")
    logger.info("=" * 60)
    
    # Check database
    check_database_status()
    
    # Check API
    await check_api_status()
    
    # Check blockchain integration
    await check_blockchain_integration()
    
    # Show next steps
    show_next_steps()
    
    logger.info(f"\n✨ System Status Check Complete!")
    logger.info("📖 View API docs: http://localhost:8000/docs")


if __name__ == "__main__":
    asyncio.run(main())
