#!/usr/bin/env python3
"""
Update existing sources with slug fields and recreate database
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.base import engine, Base, SessionLocal
from models.gaming import Source, Article, GamingProject, BlockchainData, NFTCollection


def recreate_database():
    """Recreate database with updated schema"""
    print("🔄 Recreating database with updated schema...")
    
    # Drop all tables
    Base.metadata.drop_all(bind=engine)
    
    # Create all tables with new schema
    Base.metadata.create_all(bind=engine)
    
    print("✅ Database recreated successfully")


def seed_sources_with_slugs():
    """Seed sources with slug fields"""
    print("📡 Seeding sources with slug fields...")
    
    db = SessionLocal()
    
    try:
        # Gaming news sources with slugs
        sources_data = [
            {
                "name": "CoinDesk Gaming",
                "slug": "coindesk-gaming",
                "url": "https://www.coindesk.com/",
                "source_type": "RSS",
                "category": "Gaming",
                "is_active": True,
                "scrape_frequency": 3600,
                "reliability_score": 0.9,
                "config": {
                    "rss_url": "https://www.coindesk.com/arc/outboundfeeds/rss/",
                    "fetch_full_content": True
                }
            },
            {
                "name": "Decrypt Gaming",
                "slug": "decrypt-gaming",
                "url": "https://decrypt.co/",
                "source_type": "Scraper",
                "category": "Gaming",
                "is_active": True,
                "scrape_frequency": 1800,
                "reliability_score": 0.85,
                "config": {
                    "base_url": "https://decrypt.co/news/gaming",
                    "selectors": {
                        "article_links": "article h2 a",
                        "title": "h1.post-title",
                        "content": ".post-content",
                        "author": ".author-name"
                    }
                }
            },
            {
                "name": "The Block Gaming",
                "slug": "theblock-gaming",
                "url": "https://www.theblockcrypto.com/",
                "source_type": "RSS",
                "category": "Gaming",
                "is_active": True,
                "scrape_frequency": 3600,
                "reliability_score": 0.88,
                "config": {
                    "rss_url": "https://www.theblockcrypto.com/rss.xml",
                    "fetch_full_content": False
                }
            },
            {
                "name": "CoinTelegraph Gaming",
                "slug": "cointelegraph-gaming",
                "url": "https://cointelegraph.com/",
                "source_type": "RSS",
                "category": "Gaming",
                "is_active": True,
                "scrape_frequency": 2400,
                "reliability_score": 0.82,
                "config": {
                    "rss_url": "https://cointelegraph.com/rss/tag/games",
                    "fetch_full_content": True
                }
            },
            {
                "name": "NFT Gamer",
                "slug": "nft-gamer",
                "url": "https://nftgamer.io/",
                "source_type": "RSS",
                "category": "Gaming",
                "is_active": True,
                "scrape_frequency": 1800,
                "reliability_score": 0.75,
                "config": {
                    "rss_url": "https://nftgamer.io/feed/",
                    "fetch_full_content": False
                }
            },
            {
                "name": "Play to Earn",
                "slug": "play-to-earn",
                "url": "https://playtoearn.net/",
                "source_type": "Scraper",
                "category": "Gaming",
                "is_active": True,
                "scrape_frequency": 3600,
                "reliability_score": 0.78,
                "config": {
                    "base_url": "https://playtoearn.net/news",
                    "selectors": {
                        "article_links": ".news-item h3 a",
                        "title": "h1.entry-title",
                        "content": ".entry-content",
                        "author": ".author-name"
                    }
                }
            },
            {
                "name": "DappRadar",
                "slug": "dappradar",
                "url": "https://dappradar.com/",
                "source_type": "RSS",
                "category": "Gaming",
                "is_active": True,
                "scrape_frequency": 2400,
                "reliability_score": 0.83,
                "config": {
                    "rss_url": "https://dappradar.com/blog/feed",
                    "fetch_full_content": False
                }
            }
        ]
        
        for source_data in sources_data:
            # Check if source already exists
            existing = db.query(Source).filter(Source.slug == source_data["slug"]).first()
            if existing:
                print(f"   ⚠️  Source {source_data['name']} already exists, skipping")
                continue
            
            source = Source(**source_data)
            db.add(source)
            print(f"   ✅ Added source: {source_data['name']} ({source_data['slug']})")
        
        db.commit()
        print(f"✅ Successfully seeded {len(sources_data)} sources")
        
    except Exception as e:
        print(f"❌ Error seeding sources: {e}")
        db.rollback()
    finally:
        db.close()


def reseed_gaming_projects():
    """Re-seed gaming projects"""
    print("🎮 Re-seeding gaming projects...")
    
    db = SessionLocal()
    
    try:
        # Gaming projects data
        projects_data = [
            {
                "name": "Axie Infinity",
                "slug": "axie-infinity",
                "description": "A blockchain-based trading and battling game",
                "website": "https://axieinfinity.com/",
                "category": "P2E",
                "subcategory": "Battle",
                "blockchain": "ethereum",
                "contract_addresses": ["******************************************"],
                "token_symbol": "AXS",
                "token_address": "******************************************",
                "market_cap": 820000000,
                "token_price": 8.50,
                "daily_active_users": 350000,
                "total_value_locked": 45000000,
                "twitter_followers": 850000,
                "discord_members": 750000,
                "is_active": True,
                "extra_metadata": {
                    "launch_year": 2018,
                    "genre": "Strategy",
                    "platform": "Mobile/Web"
                }
            },
            {
                "name": "The Sandbox",
                "slug": "the-sandbox",
                "description": "A virtual world where players can build and monetize gaming experiences",
                "website": "https://www.sandbox.game/",
                "category": "Metaverse",
                "subcategory": "Virtual World",
                "blockchain": "ethereum",
                "contract_addresses": ["******************************************"],
                "token_symbol": "SAND",
                "token_address": "******************************************",
                "market_cap": 800000000,
                "token_price": 0.65,
                "daily_active_users": 75000,
                "total_value_locked": 25000000,
                "twitter_followers": 1200000,
                "discord_members": 500000,
                "is_active": True,
                "extra_metadata": {
                    "launch_year": 2020,
                    "genre": "Sandbox",
                    "platform": "PC/Mobile"
                }
            },
            {
                "name": "Splinterlands",
                "slug": "splinterlands",
                "description": "A digital collectible card game built on blockchain technology",
                "website": "https://splinterlands.com/",
                "category": "P2E",
                "subcategory": "Card Game",
                "blockchain": "hive",
                "contract_addresses": [],
                "token_symbol": "SPS",
                "token_address": "0x1633b7157e7638c4d6593436111bf125ee74703f",
                "market_cap": 800000000,
                "token_price": 0.045,
                "daily_active_users": 100000,
                "total_value_locked": 15000000,
                "twitter_followers": 180000,
                "discord_members": 85000,
                "is_active": True,
                "extra_metadata": {
                    "launch_year": 2018,
                    "genre": "Card Game",
                    "platform": "Web"
                }
            }
        ]
        
        for project_data in projects_data:
            # Check if project already exists
            existing = db.query(GamingProject).filter(GamingProject.slug == project_data["slug"]).first()
            if existing:
                print(f"   ⚠️  Project {project_data['name']} already exists, skipping")
                continue
            
            project = GamingProject(**project_data)
            db.add(project)
            print(f"   ✅ Added project: {project_data['name']} ({project_data['slug']})")
        
        db.commit()
        print(f"✅ Successfully seeded {len(projects_data)} gaming projects")
        
    except Exception as e:
        print(f"❌ Error seeding gaming projects: {e}")
        db.rollback()
    finally:
        db.close()


def main():
    """Main function"""
    print("🚀 Updating database schema and data")
    print("=" * 50)
    
    # Recreate database
    recreate_database()
    print()
    
    # Seed sources with slugs
    seed_sources_with_slugs()
    print()
    
    # Re-seed gaming projects
    reseed_gaming_projects()
    print()
    
    print("=" * 50)
    print("✅ Database update completed!")


if __name__ == "__main__":
    main()
