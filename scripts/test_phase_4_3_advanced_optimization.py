#!/usr/bin/env python3
"""
Phase 4.3: Advanced Performance Monitoring & Database Optimization Test Script

Tests:
1. Query performance analysis
2. Database index analysis
3. Materialized views creation and management
4. Advanced SQL optimization with joins
5. Performance monitoring and metrics
"""

import sys
import os
import time
import logging
from datetime import datetime
from typing import Dict, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.base import get_db
from services.sql_optimization import get_sql_optimizer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Phase43Tester:
    def __init__(self):
        self.db = next(get_db())
        self.optimizer = get_sql_optimizer(self.db)
        self.test_results = {}
        
    def log_test(self, test_name: str, message: str):
        """Log test progress with timestamp"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"[{timestamp}] {test_name}: {message}")
        
    def measure_performance(self, test_name: str, test_func, *args, **kwargs):
        """Measure test performance and handle errors"""
        try:
            start_time = time.time()
            result = test_func(*args, **kwargs)
            execution_time = time.time() - start_time
            
            # Handle different result types
            if isinstance(result, dict):
                data_points = sum(len(v) if isinstance(v, list) else 1 for v in result.values() if v is not None)
            else:
                data_points = len(result) if hasattr(result, '__len__') else 1
                
            self.log_test(test_name, f"✅ PASS - {execution_time:.3f}s - {data_points} data points")
            return True, execution_time, data_points
            
        except Exception as e:
            self.log_test(test_name, f"❌ FAIL - {str(e)}")
            # Add rollback to prevent transaction abort
            try:
                self.db.rollback()
            except:
                pass
            return False, 0, 0

    def test_query_performance_analysis(self):
        """Test query performance analysis functionality"""
        self.log_test("QUERY_ANALYSIS", "Testing query performance analysis...")
        
        # Test with a simple query
        test_query = "SELECT COUNT(*) FROM gaming_projects WHERE is_active = true"
        
        success, exec_time, data_points = self.measure_performance(
            "QUERY_ANALYSIS",
            self.optimizer.analyze_query_performance,
            test_query
        )
        
        if success:
            # Test with a more complex query
            complex_query = """
                SELECT gp.blockchain, COUNT(*) as project_count, AVG(gp.daily_active_users) as avg_users
                FROM gaming_projects gp 
                WHERE gp.is_active = true 
                GROUP BY gp.blockchain 
                ORDER BY project_count DESC
            """
            
            success2, exec_time2, data_points2 = self.measure_performance(
                "QUERY_ANALYSIS_COMPLEX",
                self.optimizer.analyze_query_performance,
                complex_query
            )
            
            return success and success2
        
        return success

    def test_database_index_analysis(self):
        """Test database index analysis"""
        self.log_test("INDEX_ANALYSIS", "Testing database index analysis...")
        
        success, exec_time, data_points = self.measure_performance(
            "INDEX_ANALYSIS",
            self.optimizer.get_database_index_analysis
        )
        
        return success

    def test_materialized_views(self):
        """Test materialized views creation and management"""
        self.log_test("MATERIALIZED_VIEWS", "Testing materialized views...")
        
        # Test creation
        success1, exec_time1, data_points1 = self.measure_performance(
            "MV_CREATE",
            self.optimizer.create_gaming_materialized_views
        )
        
        if success1:
            # Test stats
            success2, exec_time2, data_points2 = self.measure_performance(
                "MV_STATS",
                self.optimizer.get_materialized_view_stats
            )
            
            if success2:
                # Test refresh
                success3, exec_time3, data_points3 = self.measure_performance(
                    "MV_REFRESH",
                    self.optimizer.refresh_materialized_views,
                    concurrent=False  # Use non-concurrent for testing
                )
                
                return success1 and success2 and success3
            
            return success1 and success2
        
        return success1

    def test_optimized_gaming_queries(self):
        """Test advanced SQL optimization with joins"""
        self.log_test("OPTIMIZED_QUERIES", "Testing optimized gaming queries...")
        
        success, exec_time, data_points = self.measure_performance(
            "OPTIMIZED_QUERIES",
            self.optimizer.optimize_gaming_queries_with_joins
        )
        
        return success

    def test_performance_monitoring(self):
        """Test enhanced performance monitoring"""
        self.log_test("PERFORMANCE_MONITORING", "Testing enhanced performance monitoring...")
        
        success, exec_time, data_points = self.measure_performance(
            "PERFORMANCE_MONITORING",
            self.optimizer.get_performance_monitoring_data
        )
        
        return success

    def run_all_tests(self):
        """Run all Phase 4.3 tests"""
        print("🚀 Starting Phase 4.3 Advanced Performance Optimization Tests")
        print("=" * 80)
        
        # Collect database stats
        self.log_test("DB_STATS", "Collecting database statistics...")
        try:
            from models.gaming import Article, GamingProject, BlockchainData, NFTCollection
            articles_count = self.db.query(Article).count()
            projects_count = self.db.query(GamingProject).count()
            blockchain_count = self.db.query(BlockchainData).count()
            nft_count = self.db.query(NFTCollection).count()
            
            self.log_test("DB_STATS", f"Articles: {articles_count}, Gaming Projects: {projects_count}")
            self.log_test("DB_STATS", f"Blockchain Data: {blockchain_count}, NFT Collections: {nft_count}")
        except Exception as e:
            self.log_test("DB_STATS", f"Error collecting stats: {e}")
        
        # Run tests
        tests = [
            ("Query Performance Analysis", self.test_query_performance_analysis),
            ("Database Index Analysis", self.test_database_index_analysis),
            ("Materialized Views", self.test_materialized_views),
            ("Optimized Gaming Queries", self.test_optimized_gaming_queries),
            ("Performance Monitoring", self.test_performance_monitoring)
        ]
        
        total_start_time = time.time()
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            if test_func():
                passed_tests += 1
        
        total_time = time.time() - total_start_time
        success_rate = (passed_tests / total_tests) * 100
        
        print("=" * 80)
        print(f"✅ Phase 4.3 Testing Complete - {total_time:.3f}s total")
        print(f"📊 Overall Success Rate: {success_rate:.1f}%")
        print(f"📋 Tests Passed: {passed_tests}/{total_tests}")
        
        if success_rate == 100:
            print("🎉 All Phase 4.3 advanced optimization features working correctly!")
        elif success_rate >= 80:
            print("⚠️  Most features working, some issues to investigate")
        else:
            print("❌ Significant issues detected, requires investigation")
        
        return success_rate

if __name__ == "__main__":
    tester = Phase43Tester()
    success_rate = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success_rate >= 80 else 1)
