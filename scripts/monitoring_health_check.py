#!/usr/bin/env python3
"""
Comprehensive monitoring health check for Web3 Gaming News Tracker
Checks Grafana, Prometheus, and overall system health
"""
import sys
import os
import asyncio
import aiohttp
import subprocess
import json
from pathlib import Path
from datetime import datetime

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MonitoringHealthChecker:
    """Comprehensive health checker for monitoring components"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.services = {
            'prometheus': {'port': 9090, 'path': '/-/healthy'},
            'grafana': {'port': 3000, 'path': '/api/health'},
            'api': {'port': 8000, 'path': '/health'},
            'dashboard': {'port': 3001, 'path': '/'},
        }
        
    async def check_service_health(self, service_name: str, port: int, path: str) -> dict:
        """Check if a service is running and healthy"""
        try:
            url = f"http://localhost:{port}{path}"
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    return {
                        'service': service_name,
                        'status': 'healthy' if response.status == 200 else 'unhealthy',
                        'port': port,
                        'response_code': response.status,
                        'url': url
                    }
                    
        except aiohttp.ClientConnectorError:
            return {
                'service': service_name,
                'status': 'not_running',
                'port': port,
                'error': 'Connection refused',
                'url': f"http://localhost:{port}{path}"
            }
        except asyncio.TimeoutError:
            return {
                'service': service_name,
                'status': 'timeout',
                'port': port,
                'error': 'Request timeout',
                'url': f"http://localhost:{port}{path}"
            }
        except Exception as e:
            return {
                'service': service_name,
                'status': 'error',
                'port': port,
                'error': str(e),
                'url': f"http://localhost:{port}{path}"
            }
    
    def check_docker_status(self) -> dict:
        """Check Docker daemon and container status"""
        try:
            # Check if Docker is running
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            docker_available = result.returncode == 0
            
            if not docker_available:
                return {'status': 'not_installed', 'containers': []}
            
            # Check if Docker daemon is running
            result = subprocess.run(['docker', 'ps'], 
                                  capture_output=True, text=True, timeout=5)
            daemon_running = result.returncode == 0
            
            if not daemon_running:
                return {'status': 'daemon_not_running', 'containers': []}
            
            # Get container status
            result = subprocess.run(['docker', 'ps', '-a', '--format', 'json'], 
                                  capture_output=True, text=True, timeout=10)
            
            containers = []
            if result.returncode == 0 and result.stdout.strip():
                for line in result.stdout.strip().split('\n'):
                    try:
                        container = json.loads(line)
                        containers.append({
                            'name': container.get('Names', ''),
                            'status': container.get('Status', ''),
                            'image': container.get('Image', ''),
                            'ports': container.get('Ports', '')
                        })
                    except json.JSONDecodeError:
                        continue
            
            return {'status': 'running', 'containers': containers}
            
        except subprocess.TimeoutExpired:
            return {'status': 'timeout', 'containers': []}
        except FileNotFoundError:
            return {'status': 'not_installed', 'containers': []}
        except Exception as e:
            return {'status': 'error', 'error': str(e), 'containers': []}
    
    def check_configuration_files(self) -> dict:
        """Check if monitoring configuration files exist and are valid"""
        config_status = {}
        
        # Check docker-compose files
        compose_files = [
            'docker-compose.yml',
            'docker-compose.monitoring.yml'
        ]
        
        for file in compose_files:
            file_path = self.base_dir / file
            config_status[file] = {
                'exists': file_path.exists(),
                'path': str(file_path)
            }
        
        # Check Prometheus config
        prometheus_config = self.base_dir / 'prometheus' / 'prometheus.yml'
        config_status['prometheus.yml'] = {
            'exists': prometheus_config.exists(),
            'path': str(prometheus_config)
        }
        
        # Check Grafana configs
        grafana_datasource = self.base_dir / 'grafana' / 'provisioning' / 'datasources' / 'prometheus.yml'
        config_status['grafana_datasource'] = {
            'exists': grafana_datasource.exists(),
            'path': str(grafana_datasource)
        }
        
        # Check dashboard files
        dashboard_dir = self.base_dir / 'grafana' / 'dashboards'
        config_status['grafana_dashboards'] = {
            'exists': dashboard_dir.exists(),
            'path': str(dashboard_dir),
            'dashboard_count': len(list(dashboard_dir.glob('*.json'))) if dashboard_dir.exists() else 0
        }
        
        return config_status
    
    async def run_comprehensive_check(self) -> dict:
        """Run comprehensive health check"""
        logger.info("🔍 MONITORING HEALTH CHECK")
        logger.info("=" * 60)
        
        # Check Docker status
        logger.info("🐳 Checking Docker status...")
        docker_status = self.check_docker_status()
        
        # Check service health
        logger.info("🌐 Checking service health...")
        service_results = []
        for service_name, config in self.services.items():
            result = await self.check_service_health(
                service_name, config['port'], config['path']
            )
            service_results.append(result)
        
        # Check configuration files
        logger.info("📁 Checking configuration files...")
        config_status = self.check_configuration_files()
        
        # Compile results
        health_report = {
            'timestamp': datetime.utcnow().isoformat(),
            'docker': docker_status,
            'services': service_results,
            'configurations': config_status,
            'summary': self._generate_summary(docker_status, service_results, config_status)
        }
        
        return health_report
    
    def _generate_summary(self, docker_status: dict, service_results: list, config_status: dict) -> dict:
        """Generate summary and recommendations"""
        summary = {
            'overall_status': 'unknown',
            'healthy_services': 0,
            'total_services': len(service_results),
            'docker_available': docker_status['status'] == 'running',
            'configurations_complete': True,
            'recommendations': []
        }
        
        # Count healthy services
        for service in service_results:
            if service['status'] == 'healthy':
                summary['healthy_services'] += 1
        
        # Check configuration completeness
        for config_name, config_info in config_status.items():
            if not config_info['exists']:
                summary['configurations_complete'] = False
                summary['recommendations'].append(f"Missing configuration: {config_name}")
        
        # Determine overall status
        if summary['docker_available'] and summary['healthy_services'] == summary['total_services']:
            summary['overall_status'] = 'healthy'
        elif summary['docker_available'] and summary['healthy_services'] > 0:
            summary['overall_status'] = 'partial'
        elif summary['docker_available']:
            summary['overall_status'] = 'services_down'
        else:
            summary['overall_status'] = 'docker_unavailable'
        
        # Generate recommendations
        if docker_status['status'] == 'not_installed':
            summary['recommendations'].append("Install Docker Desktop")
        elif docker_status['status'] == 'daemon_not_running':
            summary['recommendations'].append("Start Docker daemon")
        elif summary['healthy_services'] == 0:
            summary['recommendations'].append("Start monitoring services with: docker-compose -f docker-compose.monitoring.yml up -d")
        elif summary['healthy_services'] < summary['total_services']:
            summary['recommendations'].append("Some services are unhealthy - check logs and restart if needed")
        
        return summary
    
    def print_health_report(self, report: dict):
        """Print formatted health report"""
        logger.info("\n📊 HEALTH REPORT")
        logger.info("=" * 50)
        
        # Overall status
        status_emoji = {
            'healthy': '✅',
            'partial': '⚠️',
            'services_down': '❌',
            'docker_unavailable': '🚫',
            'unknown': '❓'
        }
        
        overall_status = report['summary']['overall_status']
        logger.info(f"{status_emoji.get(overall_status, '❓')} Overall Status: {overall_status.upper()}")
        
        # Docker status
        logger.info(f"\n🐳 Docker Status: {report['docker']['status']}")
        if report['docker']['containers']:
            logger.info("   Running containers:")
            for container in report['docker']['containers']:
                logger.info(f"   - {container['name']}: {container['status']}")
        
        # Service status
        logger.info(f"\n🌐 Services ({report['summary']['healthy_services']}/{report['summary']['total_services']} healthy):")
        for service in report['services']:
            status_icon = "✅" if service['status'] == 'healthy' else "❌"
            logger.info(f"   {status_icon} {service['service'].title()}: {service['status']} (port {service['port']})")
        
        # Configuration status
        logger.info(f"\n📁 Configuration Files:")
        for config_name, config_info in report['configurations'].items():
            status_icon = "✅" if config_info['exists'] else "❌"
            logger.info(f"   {status_icon} {config_name}: {'Found' if config_info['exists'] else 'Missing'}")
        
        # Recommendations
        if report['summary']['recommendations']:
            logger.info(f"\n💡 Recommendations:")
            for i, rec in enumerate(report['summary']['recommendations'], 1):
                logger.info(f"   {i}. {rec}")
        
        # Quick start commands
        logger.info(f"\n🚀 Quick Start Commands:")
        logger.info("   Start monitoring stack:")
        logger.info("   docker-compose -f docker-compose.monitoring.yml up -d")
        logger.info("\n   Check service logs:")
        logger.info("   docker-compose -f docker-compose.monitoring.yml logs -f")
        logger.info("\n   Access dashboards:")
        logger.info("   - Grafana: http://localhost:3000 (admin/admin)")
        logger.info("   - Prometheus: http://localhost:9090")
        logger.info("   - API Docs: http://localhost:8000/docs")


async def main():
    """Main function"""
    checker = MonitoringHealthChecker()
    report = await checker.run_comprehensive_check()
    checker.print_health_report(report)
    
    # Save report to file
    report_file = Path(__file__).parent.parent / 'monitoring_health_report.json'
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"\n📄 Full report saved to: {report_file}")


if __name__ == "__main__":
    asyncio.run(main())
